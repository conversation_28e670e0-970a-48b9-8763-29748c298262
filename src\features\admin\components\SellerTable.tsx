import React from "react";
import DataTable from "@/components/ui/data-table/DataTable";
import { Column } from "@/components/ui/data-table/types";
import { Seller } from "../type";
import { Button } from "@/components/ui/button";
import CheckedIcon from "@/assets/white-checked-icon.png";
import CloseIcon from "@/assets/close-orange.png";
import { IoEllipsisVertical } from "react-icons/io5";

interface SellerTableProps {
  data: Seller[];
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  handleSellerApprove: (sellerId: string, isApproved: boolean) => void;
  onOpen: (id: string) => void;
  focus: string;
  loading: boolean;
}

const SellerTable: React.FC<SellerTableProps> = ({
  data,
  currentPage,
  totalPages,
  onPageChange,
  focus,
  loading,
  handleSellerApprove,
  onOpen,
}) => {
  const columns: Column<Seller>[] = [
    {
      header: "Company details",
      accessorKey: "name",
      className: "w-[250px]",
      cell: (seller) => (
        <div className="flex items-center gap-2">
          <div>
            <div>{seller.companyName}</div>
            <div className="text-sm text-gray-500">{seller.authId.email}</div>
          </div>
        </div>
      ),
    },
    {
      header: "Category",
      accessorKey: "category",
    },
    {
      header: "Phone number",
      accessorKey: "phone",
    },
    {
      header: "Tax ID",
      accessorKey: "taxId",
    },
    {
      header: "Return Policy",
      accessorKey: "refundPolicy",
      cell: (seller) => (
        <div className="text-sm truncate w-80">{seller.refundPolicy}</div>
      ),
    },
    {
      header: "Video",
      accessorKey: "video",
      cell: (seller) => <span>{seller.video}1 file</span>,
    },

    {
      header: "",
      accessorKey: "actions",
      className: "w-[50px]",
      cell: (seller) =>
        focus === "pending" ? (
          <div className="flex gap-x-2">
            <Button
              variant={"outline"}
              className="p-4"
              onClick={() => handleSellerApprove(seller._id, false)}
            >
              <img src={CloseIcon} alt="checked" className="min-h-5 min-w-5" />
            </Button>
            <Button
              className="p-4"
              onClick={() => handleSellerApprove(seller._id, true)}
            >
              <img
                src={CheckedIcon}
                alt="checked"
                className="min-h-6 min-w-6"
              />
            </Button>
          </div>
        ) : (
          <IoEllipsisVertical
            onClick={() => onOpen(seller._id)}
            className="w-5 h-5 cursor-pointer"
          />
        ),
    },
  ];

  return (
    <div className="max-h-[80vh] overflow-y-scroll scrollbar-hide">
      <DataTable
        data={data}
        columns={columns}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
        loading={loading}
      />
    </div>
  );
};

export default SellerTable;
