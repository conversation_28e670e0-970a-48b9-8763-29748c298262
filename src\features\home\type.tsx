export type MenuDrawerProps = {
  open: boolean;
  onClose: () => void;
};

export type ServiceProviderProps = {
  id: string;
  image: string;
  name: string;
  specialty: string;
  rating: number;
  experience: number;
  services: Pick<ServiceData, "services">;
};

export type ProductQueryParams = {
  page?: string;
  category?: string;
  minPrice?: string;
  brand?: string;
};

export type Product = {
  _id: string;
  images: { image: string; key: string }[];
  title: string;
  seller: { companyName: string };
  price: string;
  totalRating: number;
};

export type CarePlanCardItem = {
  title: string;
  description: string;
  frequency: string;
  category: string;
};

export type ServiceData = {
  provider: {
    firstName: string;
    specialty: string;
    experience: number;
    serviceLocation: string[];
    rating: number;
    id: string;
    profilePicture: {
      url?: string;
      key: string;
    }[];
  };
  services: {
    id: string;
    title: string;
    price: number;
  }[];
};
