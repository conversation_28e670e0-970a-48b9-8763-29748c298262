import { useState } from "react";
import OrderStatusDisplay from "./OrderStatusDisplay";
import PolicyModal from "./PolicyModal";
import { fetchSellerRefundPolicy } from "../api";
import { useQuery } from "@tanstack/react-query";
import OrderCancelModal from "./OrderCancelModal";
import ReturnAndExchangeModal from "./ReturnAndExchangeModal";

function OrderStatusSection({
  orderProgress,
  sellerId,
}: {
  orderProgress: { status: string; date: string; completed: boolean }[];
  sellerId: string;
}) {
  const [isOpen, setIsOpen] = useState({
    policy: false,
    cancel: false,
    return: false,
    exchange: false,
  });

  const onOpen = (modal: keyof typeof isOpen) => {
    setIsOpen((prev) => ({ ...prev, [modal]: true }));
  };
  const onClose = (modal: keyof typeof isOpen) => {
    setIsOpen((prev) => ({ ...prev, [modal]: false }));
  };

  const {
    data,
    isPending: loading,
    refetch,
  } = useQuery({
    queryKey: ["seller-refund-policy", sellerId],
    queryFn: () => fetchSellerRefundPolicy(sellerId),
    enabled: false,
  });
  const handleClick = () => {
    refetch();
    onOpen("policy");
  };
  function fetchCurrentOrderStatus(): string {
    for (let i = orderProgress.length - 1; i >= 0; i--) {
      if (orderProgress[i].completed) {
        return orderProgress[i].status;
      }
    }
    return "unknown";
  }
  const currentOrderStatus = fetchCurrentOrderStatus();

  return (
    <>
      <div className="p-4 border rounded-lg border-gray-2">
        <h1 className="font-bold">Order Status</h1>
        <div className="flex flex-col mt-4 gap-y-4">
          {orderProgress?.map((order) => (
            <div key={order.status} className="flex flex-col gap-y-5">
              <OrderStatusDisplay
                status={order.status}
                date={order.date}
                completed={order.completed}
              />
            </div>
          ))}
          <div className="flex flex-col justify-between gap-y-4 md:gap-y-0 md:flex-row">
            {!["order confirmed", "item shipped"].includes(
              currentOrderStatus as string
            ) && (
              <div className="flex shrink-0">
                <p className="text-sm text-neutral-300 md:text-base">
                  Please read our
                  <button
                    onClick={handleClick}
                    className="mx-1 text-sm underline md:text-base"
                  >
                    return/exchange & refund policy
                  </button>
                </p>
              </div>
            )}
            <div className="ml-auto ">
              {["order confirmed", "item shipped"].includes(
                currentOrderStatus as string
              ) && (
                <button
                  onClick={() => onOpen("cancel")}
                  className="text-sm underline md:text-base"
                >
                  cancel order
                </button>
              )}
              {currentOrderStatus === "item delivered" && (
                <div className="flex justify-between w-full gap-x-3 md:justify-normal md:w-auto">
                  <button
                    onClick={() => onOpen("return")}
                    className="text-sm underline md:text-base"
                  >
                    Return order
                  </button>
                  <button
                    onClick={() => onOpen("exchange")}
                    className="text-sm underline md:text-base"
                  >
                    Exchange item
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      <PolicyModal
        isOpen={isOpen.policy}
        onClose={() => onClose("policy")}
        refundPolicy={data?.data?.refundPolicy}
        loading={loading}
      />
      <OrderCancelModal
        isOpen={isOpen.cancel}
        onClose={() => onClose("cancel")}
      />
      <ReturnAndExchangeModal
        type="return"
        isOpen={isOpen.return}
        onClose={() => onClose("return")}
      />
      <ReturnAndExchangeModal
        type="exchange"
        isOpen={isOpen.exchange}
        onClose={() => onClose("exchange")}
      />
    </>
  );
}

export default OrderStatusSection;
