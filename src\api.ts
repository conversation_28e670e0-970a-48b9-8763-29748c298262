import api from "@/lib/axios";

type Product = {
  id: string;
  title: string;
};
type Service = {
  id: string;
  title: string;
};
type SearchResult = {
  products: Product[];
  services: Service[];
  totalProducts: number;
  totalServices: number;
};
type addRecentSearchApiPayload = {
  type: string;
  item: {
    id: string;
    title: string;
  };
};
export type Notification = {
  _id: string;
  message: string;
  createdAt: string;
  image: string;
};

export const fetchRefreshTokenApi = async (role: string) => {
  const { data } = await api.post("/api/v1/auth/refresh-token", { role });
  return data;
};

export const logoutApi = async () => {
  const { data } = await api.delete("/api/v1/auth/logout");
  return data;
};

export const fetchRecentSearchApi = async (): Promise<SearchResult> => {
  const { data } = await api.get("/api/v1/search/recent-search");
  return data?.data?.recentSearch;
};

export const addRecentSearchApi = async (
  payload: addRecentSearchApiPayload
) => {
  const { data } = await api.post("/api/v1/search/recent-search", payload);
  return data;
};

export const fetchProductAndServiceApi = async (
  search: string
): Promise<SearchResult> => {
  const { data } = await api.get("/api/v1/search", { params: { search } });
  return data?.data;
};

export const fetchUnreadConversationCount = async () => {
  const { data } = await api.get("api/v1/chat/conversation/unread-count");
  return data?.data;
};
export const fetchUnreadNotificationCount = async () => {
  const { data } = await api.get("api/v1/notification/unread");
  return data?.data;
};

export const fetchNotificationApi = async (
  page: number
): Promise<{ notifications: Notification[]; totalPages: number }> => {
  const { data } = await api.get("/api/v1/notification", { params: { page } });
  return data?.data;
};

export const markNotificationAsReadApi = async (notificationId: string) => {
  const { data } = await api.patch(
    `/api/v1/notification/${notificationId}/seen`
  );
  return data;
};
