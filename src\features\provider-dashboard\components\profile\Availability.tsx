import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Plus, Trash2 } from "lucide-react";
import { TimePicker } from "@/components/ui/time-picker";
import {
  fetchProviderAvailabilityApi,
  updateProviderAvailabilityApi,
} from "../../api";
import { useMutation, useQuery } from "@tanstack/react-query";
import type { Availability } from "../../type";
import { showToast } from "@/lib/toast";
import AvailabilitySkeleton from "./AvailabilitySkelton";

export default function Availability() {
  const [timeSlots, setTimeSlots] = useState<Availability["timeSlot"]>({
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: [],
    saturday: [],
    sunday: [],
  });

  const { data, isPending: availablityLoading } = useQuery({
    queryKey: ["availability"],
    queryFn: fetchProviderAvailabilityApi,
  });

  const days = [
    "sunday",
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
  ] as const;

  useEffect(() => {
    if (data?.availability?.timeSlot) {
      setTimeSlots(data.availability.timeSlot);
    }
  }, [data]);

  const toggleDayAvailability = (day: keyof typeof timeSlots) => {
    const isCurrentlyAvailable = timeSlots[day].length > 0;

    if (isCurrentlyAvailable) {
      setTimeSlots((prev) => ({ ...prev, [day]: [] }));
    } else {
      setTimeSlots((prev) => ({
        ...prev,
        [day]: [{ start: "9:00 AM", end: "5:00 PM" }],
      }));
    }
  };

  const addTimeSlot = (day: keyof typeof timeSlots) => {
    setTimeSlots((prev) => ({
      ...prev,
      [day]: [...prev[day], { start: "9:00 AM", end: "5:00 PM" }],
    }));
  };

  const removeTimeSlot = (day: keyof typeof timeSlots, slotIndex: number) => {
    setTimeSlots((prev) => ({
      ...prev,
      [day]: prev[day].filter((_, index) => index !== slotIndex),
    }));
  };

  const updateTimeSlot = (
    day: keyof typeof timeSlots,
    slotIndex: number,
    field: "start" | "end",
    value: string
  ) => {
    setTimeSlots((prev) => ({
      ...prev,
      [day]: prev[day].map((slot, index) =>
        index === slotIndex ? { ...slot, [field]: value } : slot
      ),
    }));
  };

  const { mutate, isPending: loading } = useMutation({
    mutationFn: updateProviderAvailabilityApi,
    onSuccess: () => {
      showToast("Availability updated successfully", "success");
    },
    onError: () => {
      showToast("Failed to update availability", "error");
    },
  });

  const handleSubmit = () => {
    mutate(timeSlots);
  };

  if (availablityLoading) {
    return <AvailabilitySkeleton />;
  }

  return (
    <div className="w-full p-8">
      {/* Days of the week with time slots */}
      <div className="mb-8 space-y-4">
        {days.map((day) => {
          const daySlots = timeSlots[day];
          const isAvailable = daySlots.length > 0;
          const displayDay = day.charAt(0).toUpperCase() + day.slice(1);

          return (
            <div key={day} className="flex items-center gap-6">
              {/* Day checkbox */}
              <div className="flex items-center w-20">
                <Checkbox
                  id={`day-${day}`}
                  checked={isAvailable}
                  onCheckedChange={() => toggleDayAvailability(day)}
                  className="data-[state=checked]:bg-orange-1 data-[state=checked]:text-white data-[state=checked]:border-0 mr-2"
                />
                <Label
                  htmlFor={`day-${day}`}
                  className="font-medium text-gray-700"
                >
                  {displayDay}
                </Label>
              </div>

              {/* Time slots or "Unavailable" text */}
              <div className="flex-1">
                {isAvailable ? (
                  daySlots.map((slot, slotIndex) => (
                    <div key={slotIndex} className="flex items-center mb-2">
                      <div className="w-24">
                        <TimePicker
                          value={slot.start}
                          onChange={(value) =>
                            updateTimeSlot(day, slotIndex, "start", value)
                          }
                        />
                      </div>
                      <span className="mx-2">-</span>
                      <div className="w-24">
                        <TimePicker
                          value={slot.end}
                          onChange={(value) =>
                            updateTimeSlot(day, slotIndex, "end", value)
                          }
                        />
                      </div>

                      {slotIndex === 0 ? (
                        <Button
                          onClick={() => addTimeSlot(day)}
                          variant="ghost"
                          size="icon"
                          className="ml-2 text-orange-1"
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      ) : (
                        <Button
                          onClick={() => removeTimeSlot(day, slotIndex)}
                          variant="ghost"
                          size="icon"
                          className="ml-2 text-gray-500"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  ))
                ) : (
                  <div
                    className="flex items-center h-10 px-2 transition-colors rounded-md cursor-pointer hover:bg-gray-50"
                    onClick={() => toggleDayAvailability(day)}
                  >
                    <span className="text-gray-500">Unavailable</span>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Save Button */}
      <Button
        onClick={handleSubmit}
        disabled={loading}
        className="w-full md:w-auto bg-orange-1 hover:bg-orange-1/90"
      >
        {loading ? "Saving..." : "Save Changes"}
      </Button>
    </div>
  );
}
