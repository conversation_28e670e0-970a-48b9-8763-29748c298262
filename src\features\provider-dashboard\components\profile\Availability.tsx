import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Plus, Trash2 } from "lucide-react";
import { TimePicker } from "@/components/ui/time-picker";
import { fetchProviderAvailabilityApi, updateProviderAvailabilityApi, UpdateAvailabilityPayload } from "../../api";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { showToast } from "@/lib/toast";

type TimeSlot = {
  id: string;
  startTime: string;
  endTime: string;
};

type DaySchedule = {
  day: string;
  isAvailable: boolean;
  timeSlots: TimeSlot[];
};

// API response types
type ApiTimeSlot = {
  start: string;
  end: string;
};

type ApiAvailabilityData = {
  availability: {
    timeSlot: {
      monday: ApiTimeSlot[];
      tuesday: ApiTimeSlot[];
      wednesday: ApiTimeSlot[];
      thursday: ApiTimeSlot[];
      friday: ApiTimeSlot[];
      saturday: ApiTimeSlot[];
      sunday: ApiTimeSlot[];
    };
    _id: string;
    providerId: string;
    createdAt: string;
    __v: number;
  };
};

export default function Availability() {
  const queryClient = useQueryClient();

  // Initialize schedule for all days of the week with default empty state
  const [schedule, setSchedule] = useState<DaySchedule[]>([
    { day: "SUN", isAvailable: false, timeSlots: [] },
    { day: "MON", isAvailable: false, timeSlots: [] },
    { day: "TUE", isAvailable: false, timeSlots: [] },
    { day: "WED", isAvailable: false, timeSlots: [] },
    { day: "THU", isAvailable: false, timeSlots: [] },
    { day: "FRI", isAvailable: false, timeSlots: [] },
    { day: "SAT", isAvailable: false, timeSlots: [] },
  ]);

  const { data, isPending: availablityLoading } = useQuery({
    queryKey: ["availability"],
    queryFn: fetchProviderAvailabilityApi,
  });

  // Mutation for updating availability
  const { mutate: updateAvailability, isPending: isUpdating } = useMutation({
    mutationFn: updateProviderAvailabilityApi,
    onSuccess: () => {
      showToast("Availability updated successfully", "success");
      queryClient.invalidateQueries({ queryKey: ["availability"] });
    },
    onError: (error) => {
      console.error("Failed to update availability:", error);
      showToast("Failed to update availability", "error");
    },
  });

  // Transform API data to component format
  const transformApiDataToSchedule = (apiData: ApiAvailabilityData): DaySchedule[] => {
    if (!apiData?.availability?.timeSlot) {
      return [
        { day: "SUN", isAvailable: false, timeSlots: [] },
        { day: "MON", isAvailable: false, timeSlots: [] },
        { day: "TUE", isAvailable: false, timeSlots: [] },
        { day: "WED", isAvailable: false, timeSlots: [] },
        { day: "THU", isAvailable: false, timeSlots: [] },
        { day: "FRI", isAvailable: false, timeSlots: [] },
        { day: "SAT", isAvailable: false, timeSlots: [] },
      ];
    }

    const { timeSlot } = apiData.availability;

    // Map API day names to component day abbreviations
    const dayMapping: Record<string, string> = {
      'sunday': 'SUN',
      'monday': 'MON',
      'tuesday': 'TUE',
      'wednesday': 'WED',
      'thursday': 'THU',
      'friday': 'FRI',
      'saturday': 'SAT'
    };

    const transformedSchedule: DaySchedule[] = [];

    // Process each day in the correct order
    const orderedDays: (keyof typeof timeSlot)[] = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

    orderedDays.forEach(apiDay => {
      const dayAbbr = dayMapping[apiDay];
      const daySlots = timeSlot[apiDay] || [];

      const transformedTimeSlots: TimeSlot[] = daySlots.map((slot: ApiTimeSlot) => ({
        id: crypto.randomUUID(),
        startTime: slot.start,
        endTime: slot.end
      }));

      transformedSchedule.push({
        day: dayAbbr,
        isAvailable: transformedTimeSlots.length > 0,
        timeSlots: transformedTimeSlots
      });
    });

    return transformedSchedule;
  };

  // Update schedule when API data is loaded
  useEffect(() => {
    if (data && !availablityLoading) {
      const transformedData = transformApiDataToSchedule(data);
      setSchedule(transformedData);
    }
  }, [data, availablityLoading]);
  // Toggle day availability
  const toggleDayAvailability = (dayIndex: number) => {
    const updatedSchedule = [...schedule];
    updatedSchedule[dayIndex].isAvailable =
      !updatedSchedule[dayIndex].isAvailable;

    // If day is marked as unavailable, clear time slots
    if (!updatedSchedule[dayIndex].isAvailable) {
      updatedSchedule[dayIndex].timeSlots = [];
    } else if (updatedSchedule[dayIndex].timeSlots.length === 0) {
      // If day is marked as available and has no time slots, add a default one
      updatedSchedule[dayIndex].timeSlots = [
        { id: crypto.randomUUID(), startTime: "09:00 am", endTime: "05:00 pm" },
      ];
    }
    setSchedule(updatedSchedule);
  };

  // Add a new time slot for a day
  const addTimeSlot = (dayIndex: number) => {
    const updatedSchedule = [...schedule];
    updatedSchedule[dayIndex].timeSlots.push({
      id: crypto.randomUUID(),
      startTime: "09:00 am",
      endTime: "05:00 pm",
    });
    setSchedule(updatedSchedule);
  };

  // Remove a time slot
  const removeTimeSlot = (dayIndex: number, slotId: string) => {
    const updatedSchedule = [...schedule];
    updatedSchedule[dayIndex].timeSlots = updatedSchedule[
      dayIndex
    ].timeSlots.filter((slot) => slot.id !== slotId);
    setSchedule(updatedSchedule);
  };

  // Update time slot values
  const updateTimeSlot = (
    dayIndex: number,
    slotId: string,
    field: "startTime" | "endTime",
    value: string
  ) => {
    const updatedSchedule = [...schedule];
    const slotIndex = updatedSchedule[dayIndex].timeSlots.findIndex(
      (slot) => slot.id === slotId
    );
    if (slotIndex !== -1) {
      updatedSchedule[dayIndex].timeSlots[slotIndex][field] = value;
      setSchedule(updatedSchedule);
    }
  };

  // Transform component data back to API format
  const transformScheduleToApiFormat = (): UpdateAvailabilityPayload => {
    const timeSlot: UpdateAvailabilityPayload['timeSlot'] = {
      monday: [],
      tuesday: [],
      wednesday: [],
      thursday: [],
      friday: [],
      saturday: [],
      sunday: []
    };

    // Map day abbreviations to full day names for the API
    const dayMapping: Record<string, keyof typeof timeSlot> = {
      'SUN': 'sunday',
      'MON': 'monday',
      'TUE': 'tuesday',
      'WED': 'wednesday',
      'THU': 'thursday',
      'FRI': 'friday',
      'SAT': 'saturday'
    };

    // Convert each day's schedule to the API format
    schedule.forEach(day => {
      const apiDayKey = dayMapping[day.day];

      if (day.isAvailable && day.timeSlots.length > 0) {
        timeSlot[apiDayKey] = day.timeSlots.map(slot => ({
          start: slot.startTime,
          end: slot.endTime
        }));
      } else {
        timeSlot[apiDayKey] = [];
      }
    });

    return { timeSlot };
  };

  // Handle form submission
  const handleSubmit = () => {
    const payload = transformScheduleToApiFormat();
    updateAvailability(payload);
  };

  if (availablityLoading) {
    return (
      <div className="flex items-center justify-center w-full p-8">
        <div className="text-gray-500">Loading availability...</div>
      </div>
    );
  }

  return (
    <div className="w-full p-8">
      {/* Days of the week with time slots */}
      <div className="mb-8 space-y-4">
        {schedule.map((day, dayIndex) => (
          <div key={day.day} className="flex items-center gap-6">
            {/* Day checkbox */}
            <div className="flex items-center w-16">
              <Checkbox
                id={`day-${day.day}`}
                checked={day.isAvailable}
                onCheckedChange={() => toggleDayAvailability(dayIndex)}
                className="data-[state=checked]:bg-orange-1 data-[state=checked]:text-white data-[state=checked]:border-0 mr-2"
              />
              <Label
                htmlFor={`day-${day.day}`}
                className="font-medium text-gray-700"
              >
                {day.day}
              </Label>
            </div>

            {/* Time slots or "Unavailable" text */}
            <div className="flex-1">
              {day.isAvailable ? (
                day.timeSlots.map((slot, slotIndex) => (
                  <div key={slot.id} className="flex items-center mb-2">
                    <div className="w-24">
                      <TimePicker
                        value={slot.startTime}
                        onChange={(value) =>
                          updateTimeSlot(dayIndex, slot.id, "startTime", value)
                        }
                      />
                    </div>
                    <span className="mx-2">-</span>
                    <div className="w-24">
                      <TimePicker
                        value={slot.endTime}
                        onChange={(value) =>
                          updateTimeSlot(dayIndex, slot.id, "endTime", value)
                        }
                      />
                    </div>

                    {slotIndex === 0 ? (
                      <Button
                        onClick={() => addTimeSlot(dayIndex)}
                        variant="ghost"
                        size="icon"
                        className="ml-2 text-orange-1"
                      >
                        <Plus className="w-4 h-4" />
                      </Button>
                    ) : (
                      <Button
                        onClick={() => removeTimeSlot(dayIndex, slot.id)}
                        variant="ghost"
                        size="icon"
                        className="ml-2 text-gray-500"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                ))
              ) : (
                <div
                  className="flex items-center h-10 px-2 transition-colors rounded-md cursor-pointer hover:bg-gray-50"
                  onClick={() => toggleDayAvailability(dayIndex)}
                >
                  <span className="text-gray-500">Unavailable</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Save Button */}
      <Button
        onClick={handleSubmit}
        disabled={isUpdating || availablityLoading}
        className="w-full md:w-auto bg-orange-1 hover:bg-orange-1/90"
      >
        {isUpdating ? "Saving..." : "Save Changes"}
      </Button>
    </div>
  );
}
