import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Plus, Trash2 } from "lucide-react";
import { TimePicker } from "@/components/ui/time-picker";
import { fetchProviderAvailabilityApi } from "../../api";
import { useQuery } from "@tanstack/react-query";

// API response types - using directly without transformation
type ApiTimeSlot = {
  start: string;
  end: string;
  id?: string; // Add id for UI operations
};

type ApiAvailabilityData = {
  availability: {
    timeSlot: {
      monday: ApiTimeSlot[];
      tuesday: ApiTimeSlot[];
      wednesday: ApiTimeSlot[];
      thursday: ApiTimeSlot[];
      friday: ApiTimeSlot[];
      saturday: ApiTimeSlot[];
      sunday: ApiTimeSlot[];
    };
    _id: string;
    providerId: string;
    createdAt: string;
    __v: number;
  };
};

export default function Availability() {
  // State using API format directly - O(1) space complexity for days
  const [timeSlots, setTimeSlots] = useState<ApiAvailabilityData['availability']['timeSlot']>({
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: [],
    saturday: [],
    sunday: []
  });

  const { data, isPending: availablityLoading } = useQuery({
    queryKey: ["availability"],
    queryFn: fetchProviderAvailabilityApi,
  });

  // Day mapping for display - O(1) lookup
  const dayDisplayMap = {
    sunday: 'SUN',
    monday: 'MON',
    tuesday: 'TUE',
    wednesday: 'WED',
    thursday: 'THU',
    friday: 'FRI',
    saturday: 'SAT'
  } as const;

  // Ordered days for consistent display - O(1) space
  const orderedDays = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'] as const;

  // Update timeSlots when API data is loaded - O(1) operation
  useEffect(() => {
    if (data?.availability?.timeSlot) {
      // Add IDs to slots for UI operations - O(n) where n is total slots across all days
      const slotsWithIds = Object.entries(data.availability.timeSlot).reduce((acc, [day, slots]) => {
        acc[day as keyof typeof acc] = slots.map(slot => ({
          ...slot,
          id: slot.id || crypto.randomUUID()
        }));
        return acc;
      }, {} as typeof timeSlots);

      setTimeSlots(slotsWithIds);
    }
  }, [data]);
  // Toggle day availability - O(1) operation
  const toggleDayAvailability = (day: keyof typeof timeSlots) => {
    setTimeSlots(prev => {
      const isCurrentlyAvailable = prev[day].length > 0;

      if (isCurrentlyAvailable) {
        // Clear time slots - O(1) operation
        return { ...prev, [day]: [] };
      } else {
        // Add default time slot - O(1) operation
        return {
          ...prev,
          [day]: [{
            start: "9:00 AM",
            end: "5:00 PM",
            id: crypto.randomUUID()
          }]
        };
      }
    });
  };

  // Add a new time slot for a day - O(1) operation
  const addTimeSlot = (day: keyof typeof timeSlots) => {
    setTimeSlots(prev => ({
      ...prev,
      [day]: [...prev[day], {
        start: "9:00 AM",
        end: "5:00 PM",
        id: crypto.randomUUID()
      }]
    }));
  };

  // Remove a time slot - O(n) where n is slots for that day
  const removeTimeSlot = (day: keyof typeof timeSlots, slotId: string) => {
    setTimeSlots(prev => ({
      ...prev,
      [day]: prev[day].filter(slot => slot.id !== slotId)
    }));
  };

  // Update time slot values - O(n) where n is slots for that day
  const updateTimeSlot = (
    day: keyof typeof timeSlots,
    slotId: string,
    field: "start" | "end",
    value: string
  ) => {
    setTimeSlots(prev => ({
      ...prev,
      [day]: prev[day].map(slot =>
        slot.id === slotId ? { ...slot, [field]: value } : slot
      )
    }));
  };

  // Handle form submission - just log the current state
  const handleSubmit = () => {
    console.log("Current availability data:", timeSlots);
    // TODO: Connect to update API when needed
  };

  if (availablityLoading) {
    return (
      <div className="flex items-center justify-center w-full p-8">
        <div className="text-gray-500">Loading availability...</div>
      </div>
    );
  }

  return (
    <div className="w-full p-8">
      {/* Days of the week with time slots */}
      <div className="mb-8 space-y-4">
        {orderedDays.map((day) => {
          const daySlots = timeSlots[day];
          const isAvailable = daySlots.length > 0;
          const displayDay = dayDisplayMap[day];

          return (
            <div key={day} className="flex items-center gap-6">
              {/* Day checkbox */}
              <div className="flex items-center w-16">
                <Checkbox
                  id={`day-${day}`}
                  checked={isAvailable}
                  onCheckedChange={() => toggleDayAvailability(day)}
                  className="data-[state=checked]:bg-orange-1 data-[state=checked]:text-white data-[state=checked]:border-0 mr-2"
                />
                <Label
                  htmlFor={`day-${day}`}
                  className="font-medium text-gray-700"
                >
                  {displayDay}
                </Label>
              </div>

              {/* Time slots or "Unavailable" text */}
              <div className="flex-1">
                {isAvailable ? (
                  daySlots.map((slot, slotIndex) => (
                    <div key={slot.id} className="flex items-center mb-2">
                      <div className="w-24">
                        <TimePicker
                          value={slot.start}
                          onChange={(value) =>
                            updateTimeSlot(day, slot.id!, "start", value)
                          }
                        />
                      </div>
                      <span className="mx-2">-</span>
                      <div className="w-24">
                        <TimePicker
                          value={slot.end}
                          onChange={(value) =>
                            updateTimeSlot(day, slot.id!, "end", value)
                          }
                        />
                      </div>

                      {slotIndex === 0 ? (
                        <Button
                          onClick={() => addTimeSlot(day)}
                          variant="ghost"
                          size="icon"
                          className="ml-2 text-orange-1"
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      ) : (
                        <Button
                          onClick={() => removeTimeSlot(day, slot.id!)}
                          variant="ghost"
                          size="icon"
                          className="ml-2 text-gray-500"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  ))
                ) : (
                  <div
                    className="flex items-center h-10 px-2 transition-colors rounded-md cursor-pointer hover:bg-gray-50"
                    onClick={() => toggleDayAvailability(day)}
                  >
                    <span className="text-gray-500">Unavailable</span>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Save Button */}
      <Button
        onClick={handleSubmit}
        disabled={availablityLoading}
        className="w-full md:w-auto bg-orange-1 hover:bg-orange-1/90"
      >
        Save Changes
      </Button>
    </div>
  );
}
