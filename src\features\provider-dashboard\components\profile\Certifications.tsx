import { Button } from "@/components/ui/button";
import { Plus, Trash2, ExternalLink } from "lucide-react";
import { useState } from "react";
import CertificationUploadDialog from "./CertificationUploadDialog";

interface Certification {
  _id: string;
  url: string;
  key: string;
}

interface CertificationsProps {
  certifications: Certification[];
  onCertificationUpload?: (files: File[]) => void;
  onCertificationDelete?: (key: string) => void;
  isLoading?: boolean;
}

function Certifications({
  certifications = [],
  onCertificationUpload,
  onCertificationDelete,
  isLoading = false
}: CertificationsProps) {
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);

  const handleCertificationClick = (fileUrl: string) => {
    // Open the certification file in a new tab
    window.open(fileUrl, '_blank', 'noopener,noreferrer');
  };

  const handleCertificationUpload = (files: File[]) => {
    if (onCertificationUpload) {
      onCertificationUpload(files);
    }
    // Close dialog after submission - the dialog will reset its state
    setIsUploadDialogOpen(false);
  };

  const handleCertificationDelete = (key: string) => {
    if (onCertificationDelete) {
      onCertificationDelete(key);
    }
  };

  // Extract filename from URL for display
  const getFileName = (url: string, index: number) => {
    try {
      const urlParts = url.split('/');
      const fileName = urlParts[urlParts.length - 1];
      // Remove any query parameters
      const cleanFileName = fileName.split('?')[0];

      // Extract file extension
      const fileExtension = cleanFileName.split('.').pop()?.toLowerCase() || 'file';

      // If it's a timestamp-based filename (all numbers), create a friendly name
      const nameWithoutExt = cleanFileName.replace(/\.[^/.]+$/, '');
      if (/^\d+$/.test(nameWithoutExt)) {
        return `Certificate ${index + 1}.${fileExtension}`;
      }

      // If it has a meaningful name, use it
      return cleanFileName || `Certificate ${index + 1}.${fileExtension}`;
    } catch {
      return `Certificate ${index + 1}`;
    }
  };

  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-base">Certifications</h2>
        <Button
          variant="outline"
          onClick={() => setIsUploadDialogOpen(true)}
          className="flex items-center gap-2 text-orange-1 border-orange-1"
          disabled={isLoading}
        >
          Add <Plus className="w-4 h-4" />
        </Button>
      </div>

      {certifications.length > 0 ? (
        <div className="space-y-3">
          {certifications.map((cert, index) => (
            <div
              key={cert._id}
              className="flex justify-between items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <button
                onClick={() => handleCertificationClick(cert.url)}
                className="flex items-center gap-2 text-lg font-medium hover:text-blue-800 hover:underline cursor-pointer"
              >
                <span>{getFileName(cert.url, index)}</span>
                <ExternalLink className="w-4 h-4" />
              </button>
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-500 hover:text-red-500"
                onClick={() => handleCertificationDelete(cert.key)}
                disabled={isLoading}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-gray-500">No certifications added yet.</p>
      )}

      {/* Certification Upload Dialog */}
      <CertificationUploadDialog
        open={isUploadDialogOpen}
        onOpenChange={setIsUploadDialogOpen}
        onSubmit={handleCertificationUpload}
        isLoading={isLoading}
      />
    </div>
  );
}

export default Certifications;
