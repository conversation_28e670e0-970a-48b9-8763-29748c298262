import { useState } from "react";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import Joi from "joi";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { X, Upload, Image as  Trash2 } from "lucide-react";
import { showToast } from "@/lib/toast";

interface PhotoUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (files: File[]) => void;
  isLoading?: boolean;
}

interface PhotoFormData {
  photos: File[];
}

// Validation schema for photo upload
const photoUploadSchema = Joi.object({
  photos: Joi.array().min(1).required().messages({
    "array.min": "At least one photo is required",
    "any.required": "Photo files are required",
  }),
});

// Allowed file types and size limit
const allowedFileTypes = ["image/jpeg", "image/png", "image/jpg", "image/webp"];
const maxFileSize = 10 * 1024 * 1024; // 10MB per file

export default function PhotoUploadDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
}: PhotoUploadDialogProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [previews, setPreviews] = useState<string[]>([]);

  const {
    handleSubmit,
    setValue,
    trigger,
    formState: { errors },
  } = useForm<PhotoFormData>({
    resolver: joiResolver(photoUploadSchema),
  });

  // Handle file selection
  const handleFileSelect = (files: FileList) => {
    const validFiles: File[] = [];
    const invalidFiles: string[] = [];
    const newPreviews: string[] = [];

    Array.from(files).forEach((file) => {
      // Validate file type
      if (!allowedFileTypes.includes(file.type)) {
        invalidFiles.push(`${file.name} - Invalid file type`);
        return;
      }

      // Validate file size
      if (file.size > maxFileSize) {
        invalidFiles.push(`${file.name} - File too large (max 10MB)`);
        return;
      }

      validFiles.push(file);
      newPreviews.push(URL.createObjectURL(file));
    });

    if (invalidFiles.length > 0) {
      showToast(`Some files were rejected: ${invalidFiles.join(", ")}`, "error");
    }

    if (validFiles.length > 0) {
      const updatedFiles = [...selectedFiles, ...validFiles];
      const updatedPreviews = [...previews, ...newPreviews];

      setSelectedFiles(updatedFiles);
      setPreviews(updatedPreviews);
      setValue("photos", updatedFiles);
      trigger("photos");
    }
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFileSelect(e.target.files);
    }
  };

  // Handle drag and drop
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  // Remove selected file
  const removeFile = (index: number) => {
    const updatedFiles = selectedFiles.filter((_, i) => i !== index);
    const updatedPreviews = previews.filter((_, i) => i !== index);

    // Revoke the object URL to prevent memory leaks
    URL.revokeObjectURL(previews[index]);

    setSelectedFiles(updatedFiles);
    setPreviews(updatedPreviews);
    setValue("photos", updatedFiles);
    trigger("photos");
  };

  // Handle form submission
  const handleFormSubmit = (data: PhotoFormData) => {
    onSubmit(data.photos);
  };

  // Reset form when dialog closes
  const handleDialogClose = () => {
    // Revoke all object URLs to prevent memory leaks
    previews.forEach(preview => URL.revokeObjectURL(preview));

    setSelectedFiles([]);
    setPreviews([]);
    setValue("photos", []);
    onOpenChange(false);
  };

  // Reset form when dialog opens
  const handleDialogOpen = (open: boolean) => {
    if (open) {
      // Reset state when opening
      // Revoke any existing object URLs first
      previews.forEach(preview => URL.revokeObjectURL(preview));
      setSelectedFiles([]);
      setPreviews([]);
      setValue("photos", []);
    }
    onOpenChange(open);
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogOpen}>
      <DialogContent className="max-w-2xl mx-auto max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-lg font-semibold">Upload Photos</DialogTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDialogClose}
            className="h-6 w-6"
            disabled={isLoading}
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              dragActive
                ? "border-orange-500 bg-orange-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <div className="space-y-2">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-gray-100 rounded-full">
                <Upload className="w-6 h-6 text-gray-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600">
                  Drag and drop your photos here, or{" "}
                  <button
                    type="button"
                    className="text-orange-600 hover:text-orange-700 font-medium"
                    onClick={() => document.getElementById('photo-upload')?.click()}
                    disabled={isLoading}
                  >
                    browse
                  </button>
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  JPG, PNG, WebP up to 10MB each. Multiple files allowed.
                </p>
              </div>
            </div>

            <input
              id="photo-upload"
              type="file"
              accept={allowedFileTypes.join(',')}
              multiple
              className="hidden"
              onChange={handleFileInputChange}
              disabled={isLoading}
            />
          </div>

          {/* Selected Photos Preview */}
          {selectedFiles.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-900">Selected Photos:</h4>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-4 max-h-60 overflow-y-auto">
                {selectedFiles.map((file, index) => (
                  <div
                    key={`${file.name}-${index}`}
                    className="relative group aspect-square rounded-lg overflow-hidden bg-gray-100"
                  >
                    <img
                      src={previews[index]}
                      alt={file.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                        className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-white hover:text-red-300 bg-black bg-opacity-50 hover:bg-opacity-70"
                        disabled={isLoading}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 truncate">
                      {file.name} ({formatFileSize(file.size)})
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {errors.photos && (
            <p className="text-sm text-red-500">{errors.photos.message}</p>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleDialogClose}
              className="flex-1"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1"
              disabled={selectedFiles.length === 0 || isLoading}
            >
              {isLoading ? "Uploading..." : `Upload ${selectedFiles.length} Photo${selectedFiles.length !== 1 ? 's' : ''}`}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
