import { useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import PageLayout from "@/components/layout/PageLayout";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Star, Clock, MapPin, Check } from "lucide-react";
import { getServiceDetailsApi } from "../../provider-dashboard/api";
import { fetchProviderApi } from "../../admin/api";
import DateTimeSelector from "../components/DateTimeSelector";

function ServiceBooking() {
  const { serviceId } = useParams<{ serviceId: string }>();
  const navigate = useNavigate();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string>("");
  const [promoCode, setPromoCode] = useState<string>("");

  // Fetch service details
  const {
    data: serviceData,
    isPending: loading,
    error,
  } = useQuery({
    queryKey: ["serviceDetails", serviceId],
    queryFn: () => getServiceDetailsApi(serviceId!),
    enabled: !!serviceId,
  });

  const service = serviceData?.data?.service || null;

  // Fetch provider details using providerId from service
  const {
    data: providerData,
    isPending: providerLoading,
    error: providerError,
  } = useQuery({
    queryKey: ["provider", service?.providerId],
    queryFn: () => fetchProviderApi(service!.providerId),
    enabled: !!service?.providerId,
  });

  const provider = providerData?.provider || null;

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    setSelectedTimeSlot(""); // Reset time slot when date changes
  };

  const handleTimeSlotSelect = (timeSlot: string) => {
    setSelectedTimeSlot(timeSlot);
  };

  const handleApplyPromo = () => {
    // TODO: Implement promo code application
    console.log("Applying promo code:", promoCode);
  };

  const handleProceedToPayment = () => {
    if (selectedDate && selectedTimeSlot) {
      console.log("Proceeding to payment with:", {
        serviceId,
        selectedDate,
        selectedTimeSlot,
        promoCode
      });
      // TODO: Implement payment flow
    }
  };

  if (loading || providerLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading service details...</div>
        </div>
      </PageLayout>
    );
  }

  if (error || providerError || !service || !provider) {
    return (
      <PageLayout>
        <div className="flex flex-col items-center justify-center h-64 gap-4">
          <div className="text-lg text-red-500">
            {error || providerError ? "Failed to load service details. Please try again." : "Service not found"}
          </div>
          <Button onClick={handleGoBack} variant="outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go back
          </Button>
        </div>
      </PageLayout>
    );
  }

  // Get provider display data
  const providerName = `${provider.firstName} ${provider.lastName || ''}`.trim();
  const providerInitials = provider.firstName.charAt(0) + (provider.lastName?.charAt(0) || '');
  const profileImage = provider.profilePicture?.[0]?.url;



  return (
    <PageLayout>
      <div className="w-11/12 mx-auto mt-2">
        {/* Go Back Button */}
        <Button
          onClick={handleGoBack}
          variant="ghost"
          className="h-auto p-0 mb-6 hover:text-gray-900"
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          Go back
        </Button>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          {/* Left Side - Service Details */}
          <div className="space-y-6">
            {/* Provider Info */}
            <div className="flex items-center gap-4">
              {profileImage ? (
                <div className="w-12 h-12 overflow-hidden rounded-full">
                  <img
                    src={profileImage}
                    alt={providerName}
                    className="object-cover w-full h-full"
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center w-12 h-12 bg-gray-200 rounded-full">
                  <span className="text-lg font-semibold">{providerInitials}</span>
                </div>
              )}
              <div>
                <div className="flex gap-2">
                <h2 className="font-semibold">{providerName}</h2>
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 text-orange-500 fill-orange-500" />
                  <span className="text-sm text-gray-600">4.6</span>
                </div>
                </div>
                <p className="text-sm text-gray-600">{provider.specialty}</p>
              </div>
            </div>

            {/* Service Details */}
            <div>
              <h1 className="mb-2 text-lg font-semibold">{service.title}</h1>
              <div className="flex items-center gap-4 mb-4 text-gray-600">
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{service.duration} hour</span>
                </div>
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4" />
                  <span>{provider.serviceLocation?.join(", ") || "Not specified"}</span>
                </div>
              </div>

              {/* Service Highlights */}
              {service.highlights && service.highlights.length > 0 && (
                <div className="p-6 mb-6 border rounded-lg border-tints-50 bg-tints-30">
                  <h2 className="mb-4 text-xs text-gray-700">SERVICE HIGHLIGHTS</h2>
                  <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
                    {service.highlights.map((highlight: string, index: number) => (
                      <div key={index} className="flex items-start gap-2">
                        <Check className="flex-shrink-0 w-4 h-4"/>
                        <span className="text-sm text-gray-700">{highlight}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Description */}
              <div>
                <p className="leading-relaxed text-gray-700">
                  {service.description}
                </p>
              </div>
            </div>
          </div>

          {/* Right Side - Booking */}
          <div className="space-y-6">
            <DateTimeSelector
              selectedDate={selectedDate}
              selectedTimeSlot={selectedTimeSlot}
              onDateSelect={handleDateSelect}
              onTimeSlotSelect={handleTimeSlotSelect}
              servicePrice={service.price}
              onProceedToPayment={handleProceedToPayment}
              showPromoCode={true}
              promoCode={promoCode}
              onPromoCodeChange={setPromoCode}
              onApplyPromo={handleApplyPromo}
            />
          </div>
        </div>
      </div>
    </PageLayout>
  );
}

export default ServiceBooking;
