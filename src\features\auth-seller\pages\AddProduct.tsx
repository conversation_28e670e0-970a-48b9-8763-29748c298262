import { Navigate } from "react-router-dom";
import AuthLayout from "../components/AuthLayout";
import AddProductForm from "../components/AddProductForm";

function SellerAddProduct() {
  const isLoggedIn = localStorage.getItem("nurtureLogin") === "true";
  return isLoggedIn ? (
    <Navigate to={"/"} />
  ) : (
    <AuthLayout>
      <AddProductForm />
    </AuthLayout>
  );
}

export default SellerAddProduct;
