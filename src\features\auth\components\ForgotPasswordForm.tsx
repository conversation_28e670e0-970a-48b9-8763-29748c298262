import FingerPrintImage from "@/assets/fingerprint.png";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FaArrowLeft } from "react-icons/fa6";
import { Link, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { useMutation } from "@tanstack/react-query";

import { forgotPasswordPayload } from "../type";
import { forgotPasswordApi } from "../api";
import { forgotPasswordSchema } from "../validation";
import { useState } from "react";
import { showToast } from "@/lib/toast";
function ForgotPasswordForm() {
  const [email, setEmail] = useState<string>("");
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<forgotPasswordPayload>({
    resolver: joiResolver(forgotPasswordSchema),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: forgotPasswordApi,
    onSuccess: () => {
      navigate("/forgot-password/check-email", {
        state: {
          email,
        },
      });
    },
    onError: () => {
      showToast("Something went wrong", "error");
    },
  });

  const onSubmit = (data: forgotPasswordPayload) => {
    setEmail(data.email);
    mutate(data);
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="w-full max-w-md text-center"
    >
      <div className="flex flex-col p-4 md:justify-center md:items-center">
        <div className="w-full max-w-md p-6 bg-white rounded-lg">
          <div className="flex flex-col items-center">
            <Link to={"/"}>
              <img
                src={FingerPrintImage}
                alt="Fingerprint"
                className="object-cover mx-auto"
              />
            </Link>

            {/* Header with back button */}
            <div className="flex items-center w-full my-6">
              <Link to={"/login"}>
                <FaArrowLeft size={20} />
              </Link>
              <h1 className="flex-grow mx-auto text-3xl font-medium text-center font-prettywise">
                Forgot Password
              </h1>
            </div>

            <p className="mb-6 text-center text-gray-500">
              No worries! Just enter your email address below and we'll send you
              a link to reset your password.
            </p>

            <div className="w-full mb-4">
              <Input
                type="email"
                placeholder="Email"
                className="w-full"
                {...register("email")}
              />
              {errors.email && (
                <p className="text-sm text-red-500 text-start">
                  {errors.email.message}
                </p>
              )}
            </div>
            <Button type="submit" className="w-full mt-8" disabled={isPending}>
              {isPending ? "Sending..." : "Send reset link"}
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
}

export default ForgotPasswordForm;
