import { Service } from "../type";

function ServiceDetailCard({ service }: { service: Service }) {
  return (
    <div className="flex flex-col justify-between p-3 border rounded-lg cursor-pointer gap-y-2 border-tints-50">
      <div className="flex justify-between">
        <div className="flex gap-x-2">
          <div>
            <h1 className="text-lg font-semibold">{service.title}</h1>
            <p className=" text-neutral-300">
              {service.duration > 1
                ? `${service.duration} hours`
                : `${service.duration} hour`}
            </p>
          </div>
        </div>
        <div className="my-auto">
          <p className="my-auto text-lg font-semibold">${service.price}</p>
        </div>
      </div>
      {/* footer section */}
      <div>
        <p className="mt-2 text-sm text-neutral-500 lg:w-[500px] truncate">
          {service.description}
        </p>
      </div>
    </div>
  );
}

export default ServiceDetailCard;
