import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { useMutation } from "@tanstack/react-query";
import { useParams } from "react-router-dom";
import { cancelOrderApi } from "../api";
import { showToast } from "@/lib/toast";
import { useOrderStore } from "@/store/orderStore";

function OrderCancelModal({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) {
  const { id } = useParams();
  const { fetchOrders } = useOrderStore();
  const { mutate, isPending: loading } = useMutation({
    mutationFn: cancelOrderApi,
    onSuccess: () => {
      showToast("order cancelled successfully", "success");
      fetchOrders();
      onClose();
    },
    onError: () => {
      showToast("failed to cancel order", "error");
    },
  });
  const onConfirm = () => {
    mutate(id as string);
  };
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <h2 className="mb-2 text-lg font-semibold text-gray-900">
        Do you want to cancel this order?
      </h2>
      <p className="mb-6 text-neutral-300">
        Lorem ipsum dolor sit amet consectetur. Adipiscing feugiat vivamus
        aliquet amet pellentesque fermentum.
      </p>

      <div className="flex justify-end gap-4">
        <Button
          disabled={loading}
          variant={"outline"}
          onClick={onClose}
          className="px-8 py-2 border border-gray-300 rounded-full hover:bg-gray-100"
        >
          No
        </Button>
        <Button disabled={loading} onClick={onConfirm}>
          {loading ? "Cancelling..." : " Yes, Cancel"}
        </Button>
      </div>
    </Modal>
  );
}

export default OrderCancelModal;
