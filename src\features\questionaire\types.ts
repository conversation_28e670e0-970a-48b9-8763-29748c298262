export type QuestionType =
  | "single_select"
  | "multiselect"
  | "text_description"
  | "multiselect_or_description";

export type Question = {
  question: string;
  type: QuestionType;
  options?: string[]; // for single_select, multiselect, multiselect_or_description
  placeholder?: string; // for text_description
  allowDescription?: boolean; // only for multiselect_or_description
};

export type PhaseQuestions = {
  phase: string;
  questions: Question[];
};
export type QuestionAnswer=null | Array<string> | string
export type QuestionnaireData = PhaseQuestions[];

export type FormattedQuestionnaireData = {
  personal_details: string[];
  postnatal_assessment_questions: {
    question: string;
    answer: string[];
  }[];
};
