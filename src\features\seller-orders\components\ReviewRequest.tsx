import { Button } from "@/components/ui/button";
import { requestReviewApi } from "../api";
import { useMutation } from "@tanstack/react-query";
import { showToast } from "@/lib/toast";
import { useParams } from "react-router-dom";

function ReviewRequest({ refetch }: { refetch: () => void }) {
  const { id } = useParams();
  const { mutate, isPending: loading } = useMutation({
    mutationFn: requestReviewApi,
    onSuccess: () => {
      showToast("Review requested", "success");
      refetch()
    },
    onError: () => {
      showToast("Failed to request review", "error");
    },
  });
  const handleRequestReview = () => {
    mutate(id as string);
  };
  return (
    <>
      <div className="flex flex-col w-full p-5 rounded-lg md:flex-row gap-y-2 md:justify-between bg-tints-50">
        <h1 className="my-auto text-center">
          The product was delivered successfully.
        </h1>
        <Button
          disabled={loading}
          className="mx-auto md:mx-0"
          onClick={handleRequestReview}
        >
          {loading ? "Requesting..." : "Request a review"}
        </Button>
      </div>
    </>
  );
}

export default ReviewRequest;
