import { Link, useLocation } from "react-router-dom";
import Seller<PERSON>ashboard<PERSON>ogo from "@/assets/seller-dashboard.png";
import Message<PERSON>ogo from "@/assets/message.png";
import ReviewLogo from "@/assets/review.png";
import OrderLogo from "@/assets/order.png";

function Footer() {
  const footerItems = [
    { icon: SellerDashboardLogo, name: "Dashboard", href: "/provider/dashboard" },
    { icon: OrderLogo, name: "Services", href: "/provider/services" },
    { icon: MessageLogo, name: "Messages", href: "/provider/messages" },
    { icon: ReviewLogo, name: "Reviews", href: "/provider/reviews" },
  ];

  const { pathname } = useLocation();

  // Extract the key from the pathname for active state detection
  let key = pathname.split("/")[2];

  // Special handling for dashboard-preview route
  if (key === "dashboard-preview") {
    key = "dashboard";
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
      <div className="flex justify-between px-6 py-3">
        {footerItems.map((item) => (
          <Link
            to={item.href}
            key={item.name}
            className="flex flex-col items-center"
          >
            <div
              className={`${
                (key === item.name.toLocaleLowerCase() ||
                 (key === "dashboard-preview" && item.name.toLocaleLowerCase() === "dashboard"))
                  ? "text-orange-1" : "text-gray-500"
              }`}
            >
              {typeof item.icon === 'string' ? (
                <img src={item.icon} alt={item.name} className="w-5 h-5" />
              ) : (
                item.icon
              )}
            </div>
            <span
              className={`text-xs mt-1 ${
                (key === item.name.toLocaleLowerCase() ||
                 (key === "dashboard-preview" && item.name.toLocaleLowerCase() === "dashboard"))
                  ? "text-orange-1 font-medium" : "text-gray-500"
              }`}
            >
              {item.name}
            </span>
          </Link>
        ))}
      </div>
    </div>
  );
}

export default Footer;
