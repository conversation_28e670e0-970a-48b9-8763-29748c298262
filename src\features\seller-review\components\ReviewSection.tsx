import RatingIcon from "@/assets/star.svg";
import ReviewCard from "./ReviewCard";
import DynamicPagination from "@/components/ui/dynamic-pagination";
import { useState } from "react";
import { Separator } from "@/components/ui/separator";
import { fetchSellerReviewsApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import ReviewCardSkelton from "./ReviewCardSkelton";

function ReviewSection() {
  const [currentPage, setCurrentPage] = useState(1);
  let totalPage = 1;
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const {
    data,
    isPending: loading,
    isSuccess,
  } = useQuery({
    queryKey: ["reviews", currentPage],
    queryFn: () => fetchSellerReviewsApi(currentPage),
  });
  if (isSuccess) {
    totalPage = data?.totalPages;
  }
  return (
    <div className="mt-10 ">
      {/* header section */}
      <div className="flex gap-x-3">
        <div className="flex gap-x-1">
          <img
            src={RatingIcon}
            className="w-5 h-5 mt-1 mb-auto md:my-auto md:w-6 md:h-6"
          />
          <p>
            <span className="my-auto mr-2 text-xl md:text-3xl">
              {data?.overAllRating || 0}
            </span>
            <span className="mt-auto text-neutral-300">overall rating</span>
          </p>
        </div>
        <Separator
          orientation="vertical"
          className="h-8 my-auto text-neutral-300"
        />
        <div className="flex">
          <span className="my-auto mr-2 text-xl md:text-3xl">
            {data?.totalReviews || 0}
          </span>
          <span className="my-auto md:mt-3 text-neutral-300">reviews</span>
        </div>
      </div>
      <div className="mt-5 max-h-[80vh] overflow-y-scroll scrollbar-hide">
        {loading
          ? [1, 2, 3].map((_, index) => <ReviewCardSkelton key={index} />)
          : data?.reviews.map(
              (review: {
                _id?: string;
                userId: { firstName: string };
                rating: number;
                review: string;
                productId: { images: { image: string; key: string }[] };
              }) => (
                <ReviewCard
                  key={review._id}
                  name={review.userId.firstName}
                  rating={review.rating}
                  content={review.review}
                  image={review.productId.images[0].image}
                />
              )
            )}
        {data?.reviews.length === 0 && (
          <div className="py-8 text-center">
            <p className="text-gray-500">No reviews found</p>
          </div>
        )}
      </div>
      {data?.reviews.length > 0 && (
        <div className="flex justify-end">
          <DynamicPagination
            currentPage={currentPage}
            totalPages={totalPage}
            onPageChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
}

export default ReviewSection;
