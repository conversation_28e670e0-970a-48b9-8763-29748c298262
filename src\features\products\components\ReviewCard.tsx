import RatingIcon from "@/assets/rating-star.png";
import { ProductReviewCardProps } from "../type";

function ReviewCard({ name, rating, content }: ProductReviewCardProps) {
  return (
    <div className="flex flex-col p-4 my-3 border rounded-lg gap-y-3 border-gray-2">
      <div className="flex gap-x-4">
        <h3 className="text-lg">{name}</h3>
        <div className="flex gap-x-1">
          <img src={RatingIcon} className="w-5 h-5 my-auto" />
          <p className="my-auto text-neutral-300">{rating}</p>
        </div>
      </div>
      <p className="text-neutral-300">{content}</p>
    </div>
  );
}

export default ReviewCard;
