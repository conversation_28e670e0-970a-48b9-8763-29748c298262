import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  Sheet<PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";
type ReusableSheetProps = {
  title?: string;
  description?: string;
  children: React.ReactNode;
  side?: "top" | "bottom" | "left" | "right";
  contentClassName?: string;
  openDrawer?: boolean;
  onCloseDrawer?: () => void;
};

const Drawer: React.FC<ReusableSheetProps> = ({
  title,
  description,
  children,
  side,
  contentClassName,
  openDrawer,
  onCloseDrawer,
}) => {
  return (
    <Sheet open={openDrawer} onOpenChange={onCloseDrawer}>
      <SheetContent side={side} className={contentClassName}>
        {(title || description) && (
          <SheetHeader>
            {title && <SheetTitle>{title}</SheetTitle>}
            {description && <SheetDescription>{description}</SheetDescription>}
          </SheetHeader>
        )}
        <div className="mt-5">{children}</div>
      </SheetContent>
    </Sheet>
  );
};

export default Drawer;
