import PageLayout from "@/components/layout/PageLayout";
import { useParams } from "react-router-dom";
import Product from "../components/Product";
import ExploreMore from "../components/ExploreMore";
import ReviewSection from "../components/ReviewSection";

function ProductDetail() {
  const { id } = useParams();

  return (
    <PageLayout>
      <div className="w-11/12 mx-auto ">
        <Product productId={id || ""} />
        <ExploreMore />
        <ReviewSection productId={id || ""} />
      </div>
    </PageLayout>
  );
}

export default ProductDetail;
