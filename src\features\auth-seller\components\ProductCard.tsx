import DeleteIcon from "@/assets/delete.png";

function ProductCard({
  image,
  title,
  price,
  index,
  onDelete,
}: {
  image?: string;
  title: string;
  price: number;
  index: number;
  onDelete: (index: number) => void;
}) {
  return (
    <div className="border-2 border-gray-2 p-4  rounded-lg ">
      <div className="flex ">
        <div className="flex justify-center items-center border-2 p-4 border-gray-2">
          <img src={image} className=" my-auto h-20 w-20" alt="product" />
        </div>
        <div className="flex grow flex-col ml-4 justify-between gap-y-10">
          <h1 className="text-lg font-medium text-start">{title}</h1>
          <p className="text-start font-bold">${price}</p>
        </div>
        <div className="flex justify-start">
          <img
            onClick={() => onDelete(index)}
            src={DeleteIcon}
            alt="delete"
            className="h-5 w-5 mx-2 cursor-pointer"
          />
        </div>
      </div>
    </div>
  );
}

export default ProductCard;
