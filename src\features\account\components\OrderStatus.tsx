import CancelIcon from "@/assets/cancel.svg";
import DeliverIcon from "@/assets/deliver.svg";
import ReturnIcon from "@/assets/return.svg";
import { Orderstatus } from "../type";
import { dateFormatter } from "@/lib/utils";
// You may need to add these icons to your assets folder
import ShippingIcon from "@/assets/shipping.svg";
import PendingIcon from "@/assets/pending.svg";

function OrderStatus({ status, date }: { status: Orderstatus; date: string }) {
  const formatedDate = dateFormatter(date);
  switch (status) {
    // Order statuses
    case "order confirmed":
      return (
        <p className="text-sm text-neutral-300">Ordered on {formatedDate}</p>
      );
    case "item shipped":
      return (
        <div className="flex gap-x-1">
          <img src={ShippingIcon} alt="shipping" />
          <p className="text-sm text-neutral-300">
            Shipped on {formatedDate}
          </p>
        </div>
      );
    case "item delivered":
      return (
        <div className="flex gap-x-1">
          <img src={DeliverIcon} alt="deliver" />
          <p className="text-sm text-neutral-300">
            Delivered on {formatedDate}
          </p>
        </div>
      );
    case "item cancelled":
      return (
        <div className="flex gap-x-1">
          <img src={CancelIcon} alt="cancel" />
          <p className="text-sm text-neutral-300">
            Cancelled on {formatedDate}
          </p>
        </div>
      );

    // Return statuses
    case "return requested":
      return (
        <div className="flex gap-x-1">
          <img src={ReturnIcon} alt="return" />
          <p className="text-sm text-neutral-300">
            Return requested on {formatedDate}
          </p>
        </div>
      );
    case "return approved":
      return (
        <div className="flex gap-x-1">
          <img src={ReturnIcon} alt="return" />
          <p className="text-sm text-neutral-300">
            Return approved on {formatedDate}
          </p>
        </div>
      );
    case "return rejected":
      return (
        <div className="flex gap-x-1">
          <img src={CancelIcon} alt="cancel" />
          <p className="text-sm text-neutral-300">
            Return rejected on {formatedDate}
          </p>
        </div>
      );
    case "item returned":
      return (
        <div className="flex gap-x-1">
          <img src={ReturnIcon} alt="return" />
          <p className="text-sm text-neutral-300">Returned on {formatedDate}</p>
        </div>
      );

    // Exchange statuses
    case "exchange requested":
      return (
        <div className="flex gap-x-1">
          <img src={ReturnIcon} alt="exchange" />
          <p className="text-sm text-neutral-300">
            Exchange requested on {formatedDate}
          </p>
        </div>
      );
    case "exchange approved":
      return (
        <div className="flex gap-x-1">
          <img src={ReturnIcon} alt="exchange" />
          <p className="text-sm text-neutral-300">
            Exchange approved on {formatedDate}
          </p>
        </div>
      );
    case "exchange rejected":
      return (
        <div className="flex gap-x-1">
          <img src={CancelIcon} alt="cancel" />
          <p className="text-sm text-neutral-300">
            Exchange rejected on {formatedDate}
          </p>
        </div>
      );

    // Pending statuses
    case "shipment pending":
      return (
        <div className="flex gap-x-1">
          <img src={PendingIcon} alt="pending" />
          <p className="text-sm text-neutral-300">
            Shipment pending since {formatedDate}
          </p>
        </div>
      );
    case "delivery pending":
      return (
        <div className="flex gap-x-1">
          <img src={PendingIcon} alt="pending" />
          <p className="text-sm text-neutral-300">
            Delivery pending since {formatedDate}
          </p>
        </div>
      );

    // Request statuses
    case "request approved":
      return (
        <div className="flex gap-x-1">
          <img src={DeliverIcon} alt="approved" />
          <p className="text-sm text-neutral-300">
            Request approved on {formatedDate}
          </p>
        </div>
      );
    case "request rejected":
      return (
        <div className="flex gap-x-1">
          <img src={CancelIcon} alt="rejected" />
          <p className="text-sm text-neutral-300">
            Request rejected on {formatedDate}
          </p>
        </div>
      );

    default:
      // Fallback for any unhandled status
      return (
        <p className="text-sm text-neutral-300">
          {status} on {formatedDate}
        </p>
      );
  }
}
export default OrderStatus;
