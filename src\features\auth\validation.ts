import Joi from "joi";

export const signupSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .lowercase()
    .trim()
    .messages({
      "any.required": "Email is a required field.",
      "string.base": "Email must be a string",
      "string.email": "Invalid email",
      "string.empty": "Email cannot be empty",
    }),

  password: Joi.string()
    .trim()
    .required()
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    )
    .messages({
      "string.base": "Password must be a string",
      "string.empty": "Password cannot be empty",
      "any.required": "Password is a required field",
      "string.pattern.base":
        "Password must be 8-32 characters with at least one uppercase, lowercase, and special character",
    }),
  confirmPassword: Joi.string()
    .valid(Joi.ref("password"))
    .required()
    .label("Confirm password")
    .messages({
      "any.only": "{{#label}} does not match",
    }),
});
export const LoginSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .lowercase()
    .trim()
    .messages({
      "any.required": "Email is a required field.",
      "string.base": "Email must be a string",
      "string.email": "Invalid email",
      "string.empty": "Email cannot be empty",
    }),

  password: Joi.string()
    .trim()
    .required()

    .messages({
      "string.base": "Password must be a string",
      "string.empty": "Password cannot be empty",
      "any.required": "Password is a required field",
      "string.pattern.base":
        "Password must be 8-32 characters with at least one uppercase, lowercase, and special character",
    }),
});

export const forgotPasswordSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .lowercase()
    .trim()
    .messages({
      "any.required": "Email is a required field.",
      "string.base": "Email must be a string",
      "string.email": "Invalid email",
      "string.empty": "Email cannot be empty",
    }),
});
export const resetPasswordSchema = Joi.object({
  newPassword: Joi.string()
    .trim()
    .required()
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    )
    .messages({
      "string.base": "Password must be a string",
      "string.empty": "Password cannot be empty",
      "any.required": "Password is a required field",
      "string.pattern.base":
        "Password must be 8-32 characters with at least one uppercase, lowercase, and special character",
    }),
  confirmPassword: Joi.string()
    .valid(Joi.ref("newPassword"))
    .required()
    .label("Confirm password")
    .messages({
      "any.only": "{{#label}} does not match",
    }),
});
export const setupProfileSchema = Joi.object({
  firstName: Joi.string().required().min(3).max(26).trim().messages({
    "any.required": "First name is a required field",
    "string.base": "First name must be a string",
    "string.min": "First name atleast need 3 characters",
    "string.max": "First name cannot exceed 26 characters",
    "string.empty": "First name cannot be empty",
  }),

  phone: Joi.string().pattern(/^\d+$/).required().messages({
    "string.pattern.base": "Phone number must contain digits only.",
    "string.empty": "Phone number cannot be empty.",
    "any.required": "Phone number is required.",
  }),
  lastName: Joi.string().required().min(3).max(26).trim().messages({
    "any.required": "Last name is a required field",
    "string.base": "Last name must be a string",
    "string.min": "Last name atleast need 3 characters",
    "string.max": "Last name cannot exceed 26 characters",
    "string.empty": "Last name cannot be empty",
  }),

  address: Joi.string().required().trim().min(5).messages({
    "any.required": "Address is a required field",
    "string.base": "Address must be a string",
    "string.min": "Address atleast need 5 characters",
    "string.empty": "Address cannot be empty",
  }),

  birthStatus: Joi.string()
    .required()
    .valid("currently pregnant", "postpartum", "loss history")
    .messages({
      "any.only":
        "Birth status must be one of: Currently pregnant, Postpartum, or Loss history",
      "any.required": "Birth status is a required field",
      "string.base": "Birth status must be a string",
    }),

  preferredLanguage: Joi.string().required().valid("english").messages({
    "any.only": "Prefferred Language must be one of: English",
    "any.required": "Preferred Language is a required field",
    "string.base": "Preferred Language must be a string",
  }),
});
