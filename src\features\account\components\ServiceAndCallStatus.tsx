import { dateFormatter } from "@/lib/utils";
import BookedIcon from "@/assets/pending.svg";
import CompletedIcon from "@/assets/deliver.svg";
import CancelledIcon from "@/assets/cancel.svg";
import { Separator } from "@/components/ui/separator";
function ServiceAndCallStatus({
  status,
  date,
  openRefundPolicy,
}: {
  status: string;
  date: string;
  openRefundPolicy: () => void;
}) {
  const formatedDate = dateFormatter(date);
  switch (status) {
    case "pending":
      return (
        <div className="flex gap-x-2">
          <h1 className="text-sm text-neutral-300 md:text-base">
            Appointment booked for {formatedDate}
          </h1>
          <span className="p-1 my-auto text-xs text-white rounded-full md:text-sm md:px-3 bg-gray-3">
            Pending
          </span>
        </div>
      );
    case "accepted":
      return (
        <div className="flex gap-x-1">
          <img src={BookedIcon} alt="booked" />
          <h1 className="text-sm text-neutral-300 md:text-base">
            Appointment on {formatedDate} has been confirmed
          </h1>
        </div>
      );
    case "rejected":
      return (
        <div className="flex gap-x-1">
          <img src={CancelledIcon} alt="booked" />
          <h1 className="text-sm text-neutral-300 md:text-base">
            Appointment on {formatedDate} has been rejected
          </h1>
        </div>
      );
    case "completed":
      return (
        <div className="flex gap-x-1">
          <img src={CompletedIcon} alt="completed" />
          <h1 className="text-sm text-neutral-300 md:text-base">
            Appointment completed on {formatedDate}
          </h1>
        </div>
      );
    case "cancelled":
      return (
        <div className="flex flex-col md:flex-row gap-y-1 md:gap-y-0">
          <div className="flex gap-x-1">
            <img src={CancelledIcon} alt="cancelled" />
            <h1 className="text-sm text-neutral-300 md:text-base">
              Appointment cancelled on {formatedDate}
            </h1>
          </div>
          <div className="hidden w-1 h-1 mx-2 my-auto bg-black rounded-full md:block"></div>
          <Separator className="my-1 text-neutral-300 md:hidden" />
          <h3 className="text-sm text-neutral-300 md:text-base">
            Cash will be refunded. Please read our
            <span
              onClick={openRefundPolicy}
              className="ml-1 underline cursor-pointer "
            >
              refund policy
            </span>
          </h3>
        </div>
      );
    default:
      return <div></div>;
  }
}

export default ServiceAndCallStatus;
