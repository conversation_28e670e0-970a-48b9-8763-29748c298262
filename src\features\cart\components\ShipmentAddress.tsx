import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { addressSchema } from "../validation";
import { AddressFormData } from "../type";
import { fetchShipmentAddressApi, updateShipmentAddressApi } from "../api";
import { useMutation, useQuery } from "@tanstack/react-query";
import { showToast } from "@/lib/toast";

function ShipmentAddress({
  updateHasAddress,
}: {
  updateHasAddress: (address: boolean) => void;
}) {
  const [isEditing, setIsEditing] = useState(false);
  const [refetch, setRefetch] = useState(false);
  const { data } = useQuery({
    queryKey: ["shipmentAddress", refetch],
    queryFn: fetchShipmentAddressApi,
  });
  const [shippingAddress, setShippingAddress] = useState<AddressFormData>(
    {} as AddressFormData
  );

  useEffect(() => {
    if (data?.data?.shippingAddress &&Object.keys(data?.data?.shippingAddress).length) {
      reset(data?.data?.shippingAddress);
      setShippingAddress(data?.data?.shippingAddress);
      updateHasAddress(true);
    }
  }, [data]);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<AddressFormData>({
    resolver: joiResolver(addressSchema),
    defaultValues: {
      address: "",
      city: "",
      state: "",
      zipCode: "",
    },
  });
  const { mutate, isPending: loading } = useMutation({
    mutationFn: updateShipmentAddressApi,
    onSuccess: () => {
      showToast("Addresss successfully updated", "success");
      setRefetch((prev) => !prev);
    },
    onError: () => {
      showToast("Something went wrong", "error");
    },
  });
  const onSubmit = (data: AddressFormData) => {
    mutate(data);
    updateHasAddress(true);
    setIsEditing(false);
    reset({});
  };

  return (
    <div className="p-5 border rounded-lg border-gray-2">
      <div className="flex justify-between mb-4">
        <h2
          className="font-bold cursor-pointer"
          onClick={() => setIsEditing(false)}
        >
          Shipping to
        </h2>
        {!isEditing && (
          <button
            type="button"
            className="text-orange-1"
            onClick={() => setIsEditing(true)}
          >
            {Object.keys(shippingAddress).length ? "Edit" : "Add"}+
          </button>
        )}
      </div>

      {isEditing && (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Input
              placeholder="Street Address"
              {...register("address")}
              className="w-full border border-gray-200 focus:border-orange-1 focus:outline-none"
            />
            {errors.address && (
              <p className="mt-1 text-xs text-red-500">
                {errors.address.message}
              </p>
            )}
          </div>

          <div>
            <Input
              placeholder="City"
              {...register("city")}
              className="w-full border border-gray-200 focus:border-orange-1 focus:outline-none"
            />
            {errors.city && (
              <p className="mt-1 text-xs text-red-500">{errors.city.message}</p>
            )}
          </div>

          <div>
            <Input
              placeholder="State"
              {...register("state")}
              className="w-full border border-gray-200 focus:border-orange-1 focus:outline-none"
            />
            {errors.state && (
              <p className="mt-1 text-xs text-red-500">
                {errors.state.message}
              </p>
            )}
          </div>

          <div>
            <Input
              placeholder="ZIP Code"
              {...register("zipCode")}
              className="w-full border border-gray-200 focus:border-orange-1 focus:outline-none"
            />
            {errors.zipCode && (
              <p className="mt-1 text-xs text-red-500">
                {errors.zipCode.message}
              </p>
            )}
          </div>

          <div className="flex justify-end">
            <Button
              disabled={loading}
              type="submit"
              className="px-8 text-white rounded-full bg-orange-1 hover:bg-orange-700"
            >
              {loading ? "updating.." : "Save"}
            </Button>
          </div>
        </form>
      )}
      {Object.keys(shippingAddress).length > 0 && !isEditing && (
        <h1>
          {shippingAddress?.address},{shippingAddress?.city},
          {shippingAddress?.state},{shippingAddress?.zipCode}
        </h1>
      )}
      {Object.keys(shippingAddress).length == 0 && !isEditing && (
        <h1 className="text-error">Please add a shipping address to proceed</h1>
      )}
    </div>
  );
}

export default ShipmentAddress;
