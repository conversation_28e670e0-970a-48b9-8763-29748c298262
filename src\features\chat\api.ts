import api from "@/lib/axios";
import {
  fetchConversationsApiResponse,
  fetchCustomerChatListApiResponse,
} from "./type";

export const fetchCustomerChatListApi = async (params: {
  page: number;
  category?: "seller" | "provider";
  search?: string;
}): Promise<fetchCustomerChatListApiResponse> => {
  const { data } = await api.get("/api/v1/chat/conversations", { params });
  return data?.data;
};

export const fetchConversationMessagesApi = async (
  conversationId: string,
  page: number
): Promise<fetchConversationsApiResponse> => {
  const { data } = await api.get(
    `/api/v1/chat/conversations/${conversationId}`,
    {
      params: { page },
    }
  );
  return data?.data;
};
export const fetchConversationsApi = async (
  receiverId: string,
  page: number
): Promise<fetchConversationsApiResponse> => {
  const { data } = await api.get(
    `/api/v1/chat/users/conversation/${receiverId}`,
    { params: { page } }
  );
  return data?.data;
};

export const sendMessageApi = async (payload: {
  conversationId?: string;
  content: string;
  receiverId: string;
}) => {
  const { data } = await api.post("/api/v1/chat", payload);
  return data;
};
