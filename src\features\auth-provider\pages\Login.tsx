import { Navigate } from "react-router-dom";
import AuthLayout from "../components/AuthLayout";
import LoginForm from "../components/LoginForm";
import { checkAuthentication } from "@/lib/utils";

function ProviderLogin() {
  const { isLoggedIn, href } = checkAuthentication();

  return isLoggedIn ? (
    <Navigate to={href} />
  ) : (
    <AuthLayout>
      <LoginForm />
    </AuthLayout>
  );
}

export default ProviderLogin;
