import { io } from "socket.io-client";

const URL =
  import.meta.env.VITE_API_ENV === "Development"
    ? import.meta.env.VITE_API_LOCAL_BASE_URL
    : import.meta.env.VITE_API_PROD_BASE_URL;

const CHAT_NAMESPACE = `${URL}/chat`;
const NOTIFICATION_NAMESPACE = `${URL}/notification`;

const socket = io(CHAT_NAMESPACE, {
  autoConnect: false,
  transports: ["websocket"],
});
const notificationSocket = io(NOTIFICATION_NAMESPACE, {
  autoConnect: false,
  transports: ["websocket"],
});

export const connectSocket = (accessToken: string) => {
  if (socket.connected) return socket;
  socket.auth = { accessToken };
  socket.connect();
  socket.on("connect", () => {
    console.log("Connected!", socket.id);
  });
  socket.on("connect_error", (err: Error) => {
    console.log("Connection error:", err.message);
  });
  return socket;
};

export const disconnectSocket = () => {
  socket.disconnect();
  socket.on("disconnect", () => {
    console.log("Disconnected!");
  });
  socket.off("connect");
  socket.off("disconnect");
  socket.off("connect_error");
};
export const connectNotificationSocket = (accessToken: string) => {
  if (notificationSocket.connected) return socket;
  notificationSocket.auth = { accessToken };
  notificationSocket.connect();
  notificationSocket.on("connect", () => {
    console.log("Notification Socket Connected!", notificationSocket.id);
  });
  notificationSocket.on("connect_error", (err: Error) => {
    console.log("Notification Socket Connection error:", err.message);
  });
  return notificationSocket;
};

export const disconnectNotificationSocket = () => {
  notificationSocket.disconnect();
  notificationSocket.on("disconnect", () => {
    console.log("Notification Socket Disconnected!");
  });
  notificationSocket.off("connect");
  notificationSocket.off("disconnect");
  notificationSocket.off("connect_error");
};
