import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON> } from "./button";
import <PERSON><PERSON>ornerArrow from "@/assets/right-corner-arrow.png";
import RightCornerOrangeArrow from "@/assets/arrow-up-orange-icon.png";

function Buttonwithicon({
  text,
  href = "#",
  variant,
  classname,
  icon,
  disabled,
  onClick,
}: {
  text?: string;
  href?: string;
  variant?: string;
  classname?: string;
  icon?: string;
  disabled?: boolean;
  onClick?: React.MouseEventHandler<HTMLButtonElement> | undefined;
}) {
  switch (variant) {
    case "default":
      return (
        <Link to={href}>
          <Button
            className={`${classname} flex items-center gap-x-2 rounded-full px-8 py-2  font-medium self-start md:self-auto`}
          >
            <h4 className="text-center grow"> {text}</h4>
            <img
              src={icon ? icon : RightCornerArrow}
              alt="arrow pointing to the right top corner"
              className="m-auto"
            />
          </Button>
        </Link>
      );
    case "white":
      return (
        <Link to={href} className="my-auto">
          <Button
            className={`${classname} bg-white  hover:bg-white flex items-center gap-x-2 rounded-full px-6 py-2  font-medium self-start md:self-auto`}
          >
            <span className="text-orange-1">{text}</span>
            <img
              src={icon ? icon : RightCornerOrangeArrow}
              alt="arrow pointing to the right top corner"
              className="m-auto"
            />
          </Button>
        </Link>
      );
    case "outline":
      return (
        <Link to={href} className="">
          <Button
            className={`${classname} bg-white border  border-input  hover:bg-white flex justify-center items-center gap-x-2 rounded-full px-6 py-2  font-medium self-start`}
          >
            <span className="text-orange-1">{text}</span>
            <img
              src={icon ? icon : RightCornerOrangeArrow}
              alt="arrow pointing to the right top corner"
              className=""
            />
          </Button>
        </Link>
      );
    case "button":
      return (
        <Button
          disabled={disabled}
          onClick={onClick}
          className={`${classname} flex items-center gap-x-2 rounded-full px-8 py-2  font-medium self-start md:self-auto`}
        >
          {text &&<h1 className="text-center"> {text}</h1>}
          <img
            src={icon ? icon : RightCornerArrow}
            alt="arrow pointing to the right top corner"
            className=""
          />
        </Button>
      );
    case "white-button":
      return (
        <Button
          disabled={disabled}
          onClick={onClick}
          className={`${classname} bg-white border  border-input gap-x-2  hover:bg-white flex justify-center items-center  rounded-full px-6 py-2  font-medium self-start`}
        >
          <h1 className="text-center text-orange-1">{text}</h1>
          <img
            src={icon ? icon : RightCornerOrangeArrow}
            alt="arrow pointing to the right top corner"
            className=""
          />
        </Button>
      );
    default:
      return (
        <Link to={href}>
          <Button
            className={`${classname}flex items-center gap-x-2 rounded-full px-8 py-2  font-medium self-start md:self-auto`}
          >
            <span> {text}</span>
            <img
              src={RightCornerArrow}
              alt="arrow pointing to the right top corner"
              className="m-auto"
            />
          </Button>
        </Link>
      );
  }
}

export default Buttonwithicon;
