import PageLayout from "../layout/PageLayout";
import PageNotFoundIcon from "@/assets/404.png";
import GoBackIcon from "@/assets/left-arrow-icon.png";

import { useNavigate } from "react-router-dom";
function NotFound() {
  const navigate = useNavigate();

  const handleGoBack = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate("/"); // fallback to home
    }
  };
  return (
    <PageLayout>
      <div className="w-11/12 mx-auto">
        <div
          onClick={handleGoBack}
          className="flex items-center gap-2 my-5 cursor-pointer"
        >
          <img src={GoBackIcon} alt="go back" className="w-3 h-3" />
          Go back
        </div>
        <div className="flex items-center justify-center ">
          <div className="flex flex-col items-center p-5 border rounded-md border-gray-2 gap-y-2">
            <img
              src={PageNotFoundIcon}
              alt="order success"
              className="w-16 h-16 mx-auto"
            />
            <h2 className="mt-3 text-lg font-semibold">Page Not Found!</h2>
            <p className="text-center text-neutral-300">
              The page you're looking for doesn't exist. It might <br /> have
              been deleted or under maintenance.
            </p>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}

export default NotFound;
