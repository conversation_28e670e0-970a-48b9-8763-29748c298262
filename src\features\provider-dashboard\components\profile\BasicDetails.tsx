import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import ProfileHeader from "./ProfileHeader";
import IntroductoryVideo from "./IntroductoryVideo";
import Certifications from "./Certifications";
import RefundPolicy from "./RefundPolicy";
import ServiceHighlights from "./ServiceHighlights";
import Photos from "./Photos";
import EditProfileDialog, { EditProfileFormData } from "./EditProfileDialog";
import { getProviderCompleteProfileApi, updateProviderBasicProfileApi, updateProviderAdditionalProfileApi } from "../../api";
import { showToast } from "@/lib/toast";

// Mock data for demonstration
const mockProvider = {
  name: "<PERSON>",
  rating: 4.5,
  description: "Specialist in New Mom Feeding",
  location: "Virtual",
  phone: "(*************",
  experience: "3 yrs exp",
  image: "https://randomuser.me/api/portraits/women/44.jpg",
  videoUrl: "https://www.w3schools.com/html/mov_bbb.mp4",
  refundPolicy: "Lorem ipsum dolor sit amet consectetur. Elit pulvinar sed lorem et justo sagittis. Habitasse molestiae tellus gravida dui. Pellentesque vel non bibendum et vel sit. Pharetra fuis odio turpis sagittis. Volutatis diam commodo. Lorem ipsum dolor sit amet consectetur. Elit pulvinar sed lorem et justo sagittis. Habitasse molestiae tellus gravida dui. Pellentesque vel non bibendum et vel sit. Pharetra fuis odio turpis sagittis. Volutatis diam commodo.",
  certifications: [
    { id: "1", name: "Certificate.pdf", file: "#" },
    { id: "2", name: "Certificate.pdf", file: "#" }
  ],
  serviceHighlights: [
    { id: "1", text: "Diapering, bathing, and dressing" },
    { id: "2", text: "Safe sleep practices and soothing techniques" },
    { id: "3", text: "Feeding support (breastfeeding/bottle-feeding)" },
    { id: "4", text: "Providing a supportive and nurturing environment" },
    { id: "5", text: "Nighttime support and meal preparation" },
    { id: "6", text: "Postpartum wound care" }
  ],
  photos: [
    { id: "1", url: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" },
    { id: "2", url: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" },
    { id: "3", url: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" },
    { id: "4", url: "https://images.unsplash.com/photo-1517841905240-472988babdf9?ixlib=rb-1.2.1&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80" }
  ]
};

function BasicDetails() {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  // Fetch provider profile data
  const {
    data: providerProfile,
    isPending: loading,
    isError,
    error
  } = useQuery({
    queryKey: ["provider-profile"],
    queryFn: getProviderCompleteProfileApi,
    retry: 2,
  });

  // Update basic profile mutation
  const { mutate: updateProfile, isPending: isUpdating } = useMutation({
    mutationFn: updateProviderBasicProfileApi,
    onSuccess: (response) => {
      if (response.status === "OK") {
        showToast("Profile updated successfully", "success");
        // Refresh the profile data
        queryClient.invalidateQueries({ queryKey: ["provider-profile"] });
        setIsEditDialogOpen(false);
      } else {
        showToast("Failed to update profile", "error");
      }
    },
    onError: (error) => {
      console.error("Profile update error:", error);
      showToast("Failed to update profile", "error");
    }
  });

  // Update additional profile mutation
  const { mutate: updateAdditionalProfile, isPending: isUpdatingAdditional } = useMutation({
    mutationFn: updateProviderAdditionalProfileApi,
    onSuccess: (response) => {
      if (response.status === "OK") {
        showToast("Profile updated successfully", "success");
        // Refresh the profile data
        queryClient.invalidateQueries({ queryKey: ["provider-profile"] });
      } else {
        showToast("Failed to update profile", "error");
      }
    },
    onError: (error) => {
      console.error("Additional profile update error:", error);
      showToast("Failed to update profile", "error");
    }
  });

  const handleEditProfile = () => {
    setIsEditDialogOpen(true);
  };

  const handleSaveProfile = (data: EditProfileFormData) => {
    // Create FormData for the API request
    const formData = new FormData();
    formData.append("firstName", data.firstName);
    formData.append("lastName", data.lastName);
    formData.append("description", data.description);
    formData.append("phone", data.phone);
    formData.append("specialty", data.specialty);
    formData.append("businessName", data.businessName);
    formData.append("taxId", data.taxId);
    formData.append("experience", data.yearsOfExperience.toString());

    // Handle service location
    if (data.serviceLocation && data.serviceLocation.length > 0) {
      formData.append("serviceLocation", data.serviceLocation[0]);
    }

    // Handle profile picture if uploaded
    if (data.photo) {
      formData.append("profilePicture", data.photo);
    }

    // Call the API
    updateProfile(formData);
  };

  // Handle video upload (with replacement if existing video)
  const handleVideoUpload = (file: File, existingVideoKey?: string) => {
    const formData = new FormData();
    formData.append("introductionVideo", file);

    // If there's an existing video, send the removal key to replace it
    if (existingVideoKey) {
      formData.append("introductionVideoRemovelKey", existingVideoKey);
    }

    updateAdditionalProfile(formData);
  };

  // Handle certification upload
  const handleCertificationUpload = (files: File[]) => {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append(`certifications`, file);
    });
    updateAdditionalProfile(formData);
  };

  // Handle certification delete
  const handleCertificationDelete = (key: string) => {
    const formData = new FormData();
    // Use index 0 for single deletion - backend will handle the specific key
    formData.append(`certificationsRemovelKey[0]`, key);
    updateAdditionalProfile(formData);
  };

  // Handle service highlights add
  const handleServiceHighlightsAdd = (newHighlights: string[]) => {
    const currentHighlights = providerProfile?.highlights || [];
    // Combine existing highlights with new ones
    const allHighlights = [...currentHighlights, ...newHighlights];

    const formData = new FormData();
    allHighlights.forEach((highlight, index) => {
      formData.append(`highlights[${index}]`, highlight);
    });
    updateAdditionalProfile(formData);
  };

  // Handle service highlight delete
  const handleServiceHighlightDelete = (index: number) => {
    const currentHighlights = providerProfile?.highlights || [];
    // Create new array without the deleted highlight
    const remainingHighlights = currentHighlights.filter((_, i) => i !== index);

    const formData = new FormData();

    // If there are remaining highlights, send them
    if (remainingHighlights.length > 0) {
      remainingHighlights.forEach((highlight, i) => {
        formData.append(`highlights[${i}]`, highlight);
      });
    } else {
      // If no highlights remain, send null string in array format
      // Backend expects ['null'] to clear all highlights
      formData.append('highlights[0]', 'null');
    }

    updateAdditionalProfile(formData);
  };

  // Handle photos upload
  const handlePhotosUpload = (files: File[]) => {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append(`photos`, file);
    });
    updateAdditionalProfile(formData);
  };

  // Handle photo delete
  const handlePhotoDelete = (key: string) => {
    const formData = new FormData();
    // Use index 0 for single deletion - backend will handle the specific key
    formData.append(`photosRemovelKey[0]`, key);
    updateAdditionalProfile(formData);
  };

  // Handle refund policy save
  const handleRefundPolicySave = (policy: string) => {
    const formData = new FormData();
    formData.append("refundPolicy", policy);
    updateAdditionalProfile(formData);
  };

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-8 py-8">
        <div className="animate-pulse">
          <div className="flex items-center space-x-4">
            <div className="w-24 h-24 bg-gray-200 rounded-full"></div>
            <div className="space-y-2">
              <div className="h-6 bg-gray-200 rounded w-48"></div>
              <div className="h-4 bg-gray-200 rounded w-32"></div>
              <div className="h-4 bg-gray-200 rounded w-40"></div>
            </div>
          </div>
        </div>
        <div className="space-y-4">
          <div className="h-4 bg-gray-200 rounded w-full"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  // Show error state
  if (isError) {
    console.error("Error fetching provider profile:", error);
    showToast("Failed to load profile data", "error");
    return (
      <div className="space-y-8 py-8">
        <div className="text-center py-8">
          <p className="text-red-600">Failed to load profile data. Please try again.</p>
        </div>
      </div>
    );
  }

  // If no data, show empty state
  if (!providerProfile) {
    return (
      <div className="space-y-8 py-8">
        <div className="text-center py-8">
          <p className="text-gray-600">No profile data available.</p>
        </div>
      </div>
    );
  }

  // Transform data for components
  const fullName = `${providerProfile.firstName} ${providerProfile.lastName}`;
  const profileImage = providerProfile.profilePicture?.[0]?.url || mockProvider.image;
  const serviceLocation = providerProfile.serviceLocation?.join(", ") || "Not specified";
  const experienceText = `${providerProfile.experience} yrs exp`;
  const videoUrl = providerProfile.introductionVideo?.url;
  const videoKey = providerProfile.introductionVideo?.key;

  // Debug logging to help troubleshoot
  console.log("Provider Profile:", providerProfile);
  console.log("Introduction Video:", providerProfile.introductionVideo);
  console.log("Video URL:", videoUrl);

  // Transform certifications for the component
  const transformedCertifications = providerProfile.certifications?.map((cert, index) => ({
    _id: cert._id || `cert-${index}`,
    url: cert.url,
    key: cert.key
  })) || [];

  // Debug logging for certifications
  console.log("Original certifications:", providerProfile.certifications);
  console.log("Transformed certifications:", transformedCertifications);

  return (
    <div className="space-y-8 py-8" >
      <ProfileHeader
        name={fullName}
        rating={mockProvider.rating} // TODO: Get rating from API when available
        specialty={providerProfile.specialty || "General Care"}
        location={serviceLocation}
        phone={providerProfile.phone}
        experience={experienceText}
        image={profileImage}
        onEditClick={handleEditProfile}
      />

      {/* Description Section */}
      {providerProfile.description && (
        <div className="mb-8">
          <p className="text-neutral-300 text-base leading-relaxed">
            {providerProfile.description}
          </p>
        </div>
      )}

      <IntroductoryVideo
        videoUrl={videoUrl}
        videoKey={videoKey}
        onVideoUpload={handleVideoUpload}
        isLoading={isUpdatingAdditional}
      />

      <Certifications
        certifications={transformedCertifications}
        onCertificationUpload={handleCertificationUpload}
        onCertificationDelete={handleCertificationDelete}
        isLoading={isUpdatingAdditional}
      />

      <RefundPolicy
        policy={providerProfile.refundPolicy}
        onSave={handleRefundPolicySave}
        isLoading={isUpdatingAdditional}
      />

      <ServiceHighlights
        highlights={providerProfile.highlights || []}
        onHighlightsAdd={handleServiceHighlightsAdd}
        onHighlightDelete={handleServiceHighlightDelete}
        isLoading={isUpdatingAdditional}
      />

      <Photos
        photos={providerProfile.photos || []}
        onPhotosUpload={handlePhotosUpload}
        onPhotoDelete={handlePhotoDelete}
        isLoading={isUpdatingAdditional}
      />

      {/* Edit Profile Dialog */}
      <EditProfileDialog
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSubmit={handleSaveProfile}
        initialData={providerProfile}
        isLoading={isUpdating}
      />
    </div>
  );
}

export default BasicDetails;
