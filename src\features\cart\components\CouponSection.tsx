import { Input } from "@/components/ui/input";
import { useEffect, useState } from "react";
import { applyCouponApi, removeCoupon<PERSON>pi } from "../api";
import { showToast } from "@/lib/toast";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

function CouponSection({
  handleRefetch,
  promoCode,
}: {
  handleRefetch: () => void;
  promoCode: string;
}) {
  const [coupon, setCoupon] = useState("");
  const [error, setError] = useState<string | null>();
  useEffect(() => {
    setCoupon(promoCode);
  }, [promoCode]);
  const applyCouponResult = useMutation({
    mutationFn: applyCouponApi,
    onSuccess: () => {
      setError(null);
      showToast("Coupon applied successfully", "success");
      handleRefetch();
    },
    onError: (error: AxiosError<Array<{ message?: string }>>) => {
      console.log(error.response, "response");
      if (error.response?.status === 400) {
        setError(error?.response?.data[0]?.message);
        return;
      }
      setError("something went wrong");
    },
  });
  const removeCouponResult = useMutation({
    mutationFn: removeCouponApi,
    onSuccess: () => {
      setError(null);
      showToast("Coupon removed  successfully", "success");
      handleRefetch();
    },
    onError: (error: AxiosError<Array<{ message?: string }>>) => {
      console.log(error.response, "response");
      if (error.response?.status === 400) {
        setError(error?.response?.data[0]?.message);
        return;
      }
      setError("something went wrong");
    },
  });
  const handleApplycoupon = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!coupon) {
      setError("Please enter a coupon code");
      return;
    }

    applyCouponResult.mutate(coupon);
  };
  const handleRemovecoupon = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    removeCouponResult.mutate(coupon);
  };
  return (
    <div className="p-5 border rounded-lg border-gray-2">
      <h1 className="font-bold">Promo code</h1>
      <form
        onSubmit={promoCode ? handleRemovecoupon : handleApplycoupon}
        className="flex mt-4 gap-x-2"
      >
        <div className="w-full">
          {error && <span className="text-sm text-error">{error}</span>}
          <Input
            type="text"
            disabled={!!promoCode || applyCouponResult.isPending}
            value={coupon}
            onChange={(e) => setCoupon(e.target.value)}
            placeholder="Enter code"
            className={`${error && "border-error"}`}
          />
        </div>
        {promoCode ? (
          <button
            disabled={applyCouponResult.isPending}
            className="mt-4 text-orange-1"
          >
            {applyCouponResult.isPending ? "  REMOVING..." : "REMOVE"}
          </button>
        ) : (
          <button
            disabled={applyCouponResult.isPending}
            className="mt-4 text-orange-1"
          >
            {applyCouponResult.isPending ? "APPLYING..." : "APPLY"}
          </button>
        )}
      </form>
    </div>
  );
}

export default CouponSection;
