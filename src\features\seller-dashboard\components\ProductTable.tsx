import React from "react";
import DataTable from "@/components/ui/data-table/DataTable";
import { Column } from "@/components/ui/data-table/types";
import { Product, ProductTableProps } from "../type";
import EditIcon from "@/assets/edit.svg";
import DeleteIcon from "@/assets/delete.svg";

const ProductTable: React.FC<ProductTableProps> = ({
  data,
  currentPage,
  totalPages,
  onPageChange,
  loading,
  editProduct,
  deleteProduct,
}) => {
  const columns: Column<Product>[] = [
    {
      header: "Product",
      accessorKey: "product",
      className: "",
      cell: (product) => (
        <div className="flex items-center gap-2">
          <div>
            <img
              className="object-cover max-w-[80px] max-h-[80px]"
              src={product?.images[0]?.image || ""}
              alt={product?.title}
            />
          </div>
          <div className="text-sm ">{product?.title}</div>
        </div>
      ),
    },
    {
      header: "Description",
      accessorKey: "description",
      cell: (product) => (
        <div className="text-sm truncate w-80">{product.description}</div>
      ),
    },
    {
      header: "Stock Availability",
      accessorKey: "quantity",
    },
    {
      header: "Price",
      accessorKey: "price",
      cell: (product) => (
        <div>
          <span className="">${product.price}</span>
        </div>
      ),
    },
    {
      header: "",
      accessorKey: "actions",
      className: "w-[50px]",
      cell: (product) => (
        <img
          src={EditIcon}
          className="cursor-pointer"
          onClick={() => editProduct(product._id)}
        />
      ),
    },
    {
      header: "",
      accessorKey: "actions",
      className: "w-[50px]",
      cell: (product) => (
        <img
          className="cursor-pointer"
          src={DeleteIcon}
          onClick={() => deleteProduct(product._id)}
        />
      ),
    },
  ];

  return (
    <div className="max-h-[90vh] overflow-y-scroll scrollbar-hide">
      <DataTable
        data={data}
        columns={columns}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
        loading={loading}
      />
    </div>
  );
};

export default ProductTable;
