import PageLayout from "@/components/layout/PageLayout";
import GoBack from "@/components/ui/go-back";
import { useQuery } from "@tanstack/react-query";
import { Link, useParams } from "react-router-dom";
import { fetchBlogDetailsApi, fetchBlogsApi } from "../api";
import { PortableText } from "@portabletext/react";
import { Blog } from "../type";
import { urlFor } from "@/lib/sanity-client";
import BlogCard from "../components/BlogCard";
import { dateFormatter } from "@/lib/utils";
import BlogDetailsSkeleton from "../components/BlogDetailsSkelton";
import BlogCardSkeleton from "../components/BlogCardSkelton";

function BlogDetails() {
  const { slug } = useParams();
  const blogDetails = useQuery({
    queryKey: ["blog-details", slug],
    queryFn: () => fetchBlogDetailsApi(slug as string),
    enabled: !!slug, // Only run the query if slug is defined
  });
  const blogs = useQuery({
    queryKey: ["blogs"],
    queryFn: () => fetchBlogsApi(null, 2),
  });

  return (
    <PageLayout>
      <div className="w-11/12 mx-auto">
        <GoBack />
        <div className="grid grid-cols-12 gap-x-6 gap-y-4 md:gap-y-0">
          <div className="col-span-12 md:col-span-8">
            {blogDetails.isPending ? (
              <BlogDetailsSkeleton />
            ) : (
              <>
                <img
                  src={urlFor(blogDetails?.data?.mainImage)}
                  alt="blog cover"
                  className="w-full h-fit max-h-[400px] object-cover rounded-t-lg "
                />
                <div className="mt-2 mb-3">
                  <h3 className="text-lg ">{blogDetails?.data?.title}</h3>
                  <h5 className="text-sm text-neutral-300">
                    {dateFormatter(blogDetails?.data?.publishedAt)}
                  </h5>
                </div>
                <PortableText value={blogDetails?.data?.content} />
              </>
            )}
          </div>
          <div className="flex-col hidden col-span-12 md:flex md:col-span-4 gap-y-4">
            <Link to="/resources" className="text-lg md:mx-3">
              Read More
            </Link>
            {blogs?.isPending ? (
              <div className="space-y-4">
                {[1, 2].map((_, i) => (
                  <BlogCardSkeleton key={i} />
                ))}
              </div>
            ) : (
              blogs?.data?.map((blog: Blog) => {
                return (
                  <div key={blog._id} className="md:p-3 ">
                    <BlogCard
                      coverImage={urlFor(blog.coverImage)}
                      title={blog.title}
                      redirectTo={`/resources/${blog.slug.current}`}
                      publishedAt={blog.publishedAt}
                    />
                  </div>
                );
              })
            )}
          </div>
        </div>
      </div>
    </PageLayout>
  );
}

export default BlogDetails;
