import { Modal } from "@/components/ui/modal";

function RefundModal({
  isOpen,
  onClose,
  refundPolicy,
}: {
  isOpen: boolean;
  onClose: () => void;
  refundPolicy: string;
}) {
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div>
        <h1 className="mb-4 text-xl ">Refund Policy</h1>
        <p className="text-neutral-300">{refundPolicy}</p>
      </div>
    </Modal>
  );
}

export default RefundModal;
