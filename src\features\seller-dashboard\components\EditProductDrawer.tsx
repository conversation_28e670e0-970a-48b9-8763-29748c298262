import Drawer from "@/components/ui/custom-drawer";
import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { showToast } from "@/lib/toast";
import { getSellerSingleProductApi, sellerUpdateProductApi } from "../api";
import { useEffect, useState } from "react";
import { joiResolver } from "@hookform/resolvers/joi";
import { Controller, useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { ProductCreationPayload } from "../type";
import { productCreationSchema } from "../validation";

function EditProductDrawer({
  refetch,
  prodcutId,
  openDrawer,
  onCloseDrawer,
}: {
  refetch: () => void;
  prodcutId: string;
  openDrawer: boolean;
  onCloseDrawer: () => void;
}) {
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [existingImages, setExistingImages] = useState<
    { image: string; key: string }[]
  >([]);
  const [removedImageKeys, setRemovedImageKeys] = useState<string[]>([]);
  const [imageError, setImageError] = useState<string>("");
  const currentProduct = useQuery({
    queryKey: ["seller-product"],
    queryFn: () => getSellerSingleProductApi(prodcutId),
  });

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm<ProductCreationPayload>({
    resolver: joiResolver(productCreationSchema),
    defaultValues: {
      title: "",
      description: "",
      price: undefined,
      quantity: undefined,
    },
  });
  // Reset form and load existing images when product data is available
  useEffect(() => {
    if (currentProduct.isSuccess) {
      // Reset form fields
      reset({
        title: currentProduct?.data?.data?.product?.title,
        description: currentProduct?.data?.data?.product?.description,
        price: currentProduct?.data?.data?.product?.price,
        quantity: currentProduct?.data?.data?.product?.quantity,
      });

      // Reset image states
      setSelectedImages([]);
      setRemovedImageKeys([]);

      // Load existing product images
      if (
        currentProduct?.data?.data?.product?.images &&
        currentProduct?.data?.data?.product?.images.length > 0
      ) {
        const productImages = currentProduct.data.data.product.images;
        setExistingImages(productImages);

        // Set image preview URLs from existing images
        const imageUrls = productImages.map(
          (img: { image: string; key: string }) => img.image
        );
        setImagePreviewUrls(imageUrls);
      } else {
        setExistingImages([]);
        setImagePreviewUrls([]);
      }
    }
  }, [reset, currentProduct.isSuccess, currentProduct.data]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImageError("");
      if (selectedImages.length >= 5) {
        setImageError("You can only upload up to 5 images");
        return;
      }
      const newFile = e.target.files[0];

      // Validate file type
      const validTypes = ["image/jpeg", "image/png", "image/jpg", "image/webp"];
      if (!validTypes.includes(newFile.type)) {
        setImageError("Please upload a valid image file (JPEG, PNG, WebP)");
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (newFile.size > maxSize) {
        setImageError("Image size should be less than 5MB");
        return;
      }

      setImageError("");
      setSelectedImages([...selectedImages, newFile]);

      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreviewUrls([...imagePreviewUrls, reader.result as string]);
      };
      reader.readAsDataURL(newFile);
    }
  };

  const removeImage = (index: number) => {
    const removedUrl = imagePreviewUrls[index];
    const updatedPreviews = [...imagePreviewUrls];
    updatedPreviews.splice(index, 1);
    setImagePreviewUrls(updatedPreviews);

    // Check if we're removing an existing image
    const existingImageIndex = existingImages.findIndex(
      (img) => img.image === removedUrl
    );

    if (existingImageIndex !== -1) {
      // We're removing an existing image, track its key for deletion
      const imageKey = existingImages[existingImageIndex].key;
      setRemovedImageKeys((prev) => [...prev, imageKey]);
    } else {
      // We're removing a newly added image
      // Calculate the index in the selectedImages array
      const newImageIndex =
        index -
        existingImages.filter((img) => imagePreviewUrls.includes(img.image))
          .length;
      if (newImageIndex >= 0) {
        const updatedImages = [...selectedImages];
        updatedImages.splice(newImageIndex, 1);
        setSelectedImages(updatedImages);
      }
    }
  };

  const { mutate, isPending: loading } = useMutation({
    mutationFn: sellerUpdateProductApi,
    onSuccess: () => {
      showToast("Product updated successfully!", "success");
      onCloseDrawer();
      refetch();
    },
    onError: (error: AxiosError) => {
      console.error(error.response?.data);
      showToast("Failed to update product. Please try again.", "error");
    },
  });
  const onSubmit = (data: ProductCreationPayload) => {
    // Validate that at least one image is selected or exists
    if (selectedImages.length === 0 && imagePreviewUrls.length === 0) {
      setImageError("Please upload at least one product image");
      return;
    }

    // Create FormData for API call
    const formData = new FormData();
    formData.append("title", data.title);
    formData.append("description", data.description);
    formData.append("price", data.price.toString());
    formData.append("quantity", data.quantity.toString());

    // Add removed image keys for deletion
    if (removedImageKeys.length > 0) {
      removedImageKeys.forEach((key) => {
        formData.append(`removedImages[]`, key);
      });
    }

    // Add new images
    selectedImages.forEach((image) => {
      formData.append(`images`, image);
    });

    mutate({ payload: formData, productId: prodcutId });
  };
  return (
    <Drawer
      onCloseDrawer={onCloseDrawer}
      openDrawer={openDrawer}
      title="Edit Product"
    >
      <>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-4">
            <p className="mb-2 text-gray-600">Product images</p>
            <div className="flex gap-2 overflow-x-auto">
              {/* Image upload button */}
              <label htmlFor="image-upload" className="cursor-pointer">
                <div className="w-[80px] h-[80px] border border-dashed border-red-200 rounded flex items-center justify-center bg-red-50">
                  <span className="text-2xl text-red-400">+</span>
                </div>
                <input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="hidden"
                />
              </label>

              {/* Image previews */}
              {imagePreviewUrls.map((url, index) => (
                <div
                  key={index}
                  className="relative w-[80px] h-[80px] border rounded"
                >
                  <img
                    src={url}
                    alt={`Product ${index}`}
                    className="object-cover w-full h-full"
                  />
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="absolute flex items-center justify-center w-5 h-5 bg-white rounded-full shadow-sm -top-2 -right-2"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
            {imageError && (
              <p className="mt-1 text-sm text-red-500">{imageError}</p>
            )}
          </div>

          <div className="space-y-4">
            <div>
              <Controller
                name="title"
                control={control}
                render={({ field }) => (
                  <Input placeholder="Product name" {...field} />
                )}
              />
              {errors.title && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.title.message}
                </p>
              )}
            </div>

            <div>
              <div className="relative">
                <Controller
                  name="price"
                  control={control}
                  render={({ field }) => (
                    <Input
                      type="number"
                      placeholder="Pricing"
                      {...field}
                      // onChange={(e) => field.onChange(e.target.value)}
                    />
                  )}
                />
                <span className="absolute text-gray-500 transform -translate-y-1/2 right-3 top-1/2">
                  $
                </span>
              </div>
              {errors.price && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.price.message}
                </p>
              )}
            </div>

            <div>
              <div className="relative">
                <Controller
                  name="quantity"
                  control={control}
                  render={({ field }) => (
                    <Input
                      type="number"
                      inputMode="numeric"
                      placeholder="Stock availability"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  )}
                />
                <span className="absolute text-gray-500 transform -translate-y-1/2 right-3 top-1/2">
                  no.s
                </span>
              </div>
              {errors.quantity && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.quantity.message}
                </p>
              )}
            </div>

            <div>
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <Textarea placeholder="Description" rows={5} {...field} />
                )}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.description.message}
                </p>
              )}
            </div>

            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Updating..." : "Update Product"}
            </Button>
          </div>
        </form>
      </>
    </Drawer>
  );
}

export default EditProductDrawer;
