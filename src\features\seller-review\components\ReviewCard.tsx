import RatingIcon from "@/assets/star.svg";
import { ReviewCardProps } from "../type";

function ReviewCard({ name, rating, content, image }: ReviewCardProps) {
  return (
    <div className="flex flex-col p-4 my-3 border rounded-lg gap-y-3 border-gray-2">
      <div className="flex gap-x-4">
        <div className="flex-shrink-0 w-16 h-16 overflow-hidden rounded-md">
          <img src={image} alt={name} className="object-cover w-full h-full-" />
        </div>
        <div className="flex flex-col justify-between">
          <div className="flex gap-x-3">
            <h3 className="md:text-lg">{name}</h3>
            <div className="flex gap-x-1">
              <img src={RatingIcon} className="w-5 h-5 my-auto" />
              <p className="my-auto text-neutral-300">{rating}</p>
            </div>
          </div>
          <p className="text-neutral-300">{content}</p>
        </div>
      </div>
    </div>
  );
}

export default ReviewCard;
