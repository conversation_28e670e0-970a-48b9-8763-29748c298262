import Layout from "@/components/layout/ProviderLayout";
import FailureIcon from "@/assets/failure.png";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
function SubscriptionFailurePage() {
  return (
    <Layout>
      <div className="w-11/12 mx-auto">
        <div className="flex items-center justify-center w-full ">
          <div className="flex flex-col items-center w-full max-w-xl p-5 border rounded-md border-gray-2 gap-y-2">
            <img
              src={FailureIcon}
              alt="order success"
              className="w-16 h-16 mx-auto"
            />
            <h2 className="mt-3 text-lg font-semibold">Payment Failed!</h2>
            <p className="text-center text-neutral-300">
              Your payment was not successful, go back and try <br />
              again.
            </p>
            <div className="flex mt-5 gap-x-4">
              <Link to={"/provider/profile/subscription"}>
                <Button variant={"outline"}>Choose other plans</Button>
              </Link>
              <Link to={"/provider/dashboard"}>
                <Button>Dashboard</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}

export default SubscriptionFailurePage;
