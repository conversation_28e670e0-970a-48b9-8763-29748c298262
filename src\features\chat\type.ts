export type ProfilePicture = {
  url: string;
  key: string;
};
export type ChatBase = {
  conversationId: string;
  lastMessage: string;
  unreadCount: number;
  profilePicture?: ProfilePicture;
};
type NameInfo =
  | { firstName: string; companyName?: string }
  | { firstName?: string; companyName: string };

type DomainInfo =
  | { speciality: string; category?: string }
  | { speciality?: string; category: string };

export type Chat = ChatBase & NameInfo & DomainInfo;
export type SelectedChat = {
  conversationId?: string;
  name: string;
  description: string;
  profilePicture?: ProfilePicture;
  receiverId: string;
  role: string;
};

export type fetchConversationsApiResponse={
  chats: Message[];
  totalPages: number;
}
export type Message = {
  _id: string;
  sentBy: string;
  content: string;
  createdAt: string;
  conversationId: string;
};

type Recipient = (NameInfo & DomainInfo) & {
  profilePicture?: ProfilePicture[];
  _id: string;
};

type LastMessage = {
  content: string;
  createdAt: string; // You can use `Date` instead if it's parsed
};

export type Conversation = {
  _id: string;
  lastMessage: LastMessage;
  recipient: Recipient;
  unreadCount: number;
};

export type fetchCustomerChatListApiResponse = {
  conversations: Conversation[];
  totalPages: number;
};
