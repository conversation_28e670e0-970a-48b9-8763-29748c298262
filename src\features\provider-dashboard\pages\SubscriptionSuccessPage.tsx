import Layout from "@/components/layout/ProviderLayout";
import SuccessIcon from "@/assets/success.png";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

function SubscriptionSuccessPage() {
  return (
    <Layout>
      <div className="w-11/12 mx-auto">
        <div className="flex items-center justify-center w-full my-20">
          <div className="flex flex-col items-center w-full max-w-2xl p-8 border rounded-lg border-gray-2 gap-y-6">
            {/* Success Icon */}
            <img
              src={SuccessIcon}
              alt="subscription success"
              className="w-20 h-20 mx-auto"
            />

            {/* Success Message */}
            <div className="text-center">
              <h2 className="mb-2 text-xl font-semibold text-brown-1">
                Subscription Activated!
              </h2>
              <p className="leading-relaxed text-neutral-300">
                Your payment was successful and your subscription is now active.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col w-full gap-3 mt-4">
              <Link to={"/provider/dashboard"} className="w-full">
                <Button className="w-full">Go to Dashboard</Button>
              </Link>

              <Link to={"/provider/profile/subscription"} className="w-full">
                <Button variant="outline" className="w-full">
                  View Subscription Details
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}

export default SubscriptionSuccessPage;
