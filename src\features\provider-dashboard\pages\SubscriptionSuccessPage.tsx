import Layout from "@/components/layout/ProviderLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { CheckCircle, Sparkles, ArrowRight, Calendar, Shield, Zap } from "lucide-react";

function SubscriptionSuccessPage() {
  return (
    <Layout>
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
        <div className="container px-4 py-12 mx-auto">
          {/* Success Animation Container */}
          <div className="max-w-2xl mx-auto">
            {/* Main Success Card */}
            <div className="overflow-hidden bg-white border border-gray-100 shadow-2xl rounded-3xl">
              {/* Header with gradient background */}
              <div className="relative px-8 py-12 overflow-hidden text-center bg-gradient-to-r from-green-500 to-emerald-600">
                {/* Background decoration */}
                <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
                <div className="absolute w-20 h-20 rounded-full top-4 left-4 bg-white/20 blur-xl"></div>
                <div className="absolute w-16 h-16 rounded-full bottom-4 right-4 bg-white/20 blur-lg"></div>

                {/* Success Icon */}
                <div className="relative z-10">
                  <div className="inline-flex items-center justify-center w-24 h-24 mb-6 bg-white rounded-full shadow-lg animate-bounce">
                    <CheckCircle className="w-12 h-12 text-green-500" />
                  </div>

                  {/* Success Message */}
                  <h1 className="mb-3 text-4xl font-bold text-white">
                    🎉 Subscription Activated!
                  </h1>
                  <p className="text-lg font-medium text-green-100">
                    Welcome to your enhanced provider experience
                  </p>
                </div>
              </div>

              {/* Content Section */}
              <div className="px-8 py-10">
                {/* Success Details */}
                <div className="mb-8 text-center">
                  <div className="inline-flex items-center gap-2 px-4 py-2 mb-4 text-sm font-medium text-green-700 rounded-full bg-green-50">
                    <Sparkles className="w-4 h-4" />
                    Payment processed successfully
                  </div>

                  <p className="max-w-md mx-auto text-lg leading-relaxed text-gray-600">
                    Your subscription is now active and you have full access to all premium features.
                    Start growing your practice today!
                  </p>
                </div>

                {/* Feature Highlights */}
                <div className="grid gap-4 mb-10 md:grid-cols-3">
                  <div className="p-4 text-center border border-blue-100 bg-blue-50 rounded-xl">
                    <div className="inline-flex items-center justify-center w-12 h-12 mb-3 bg-blue-500 rounded-lg">
                      <Calendar className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="mb-1 font-semibold text-gray-800">Advanced Scheduling</h3>
                    <p className="text-sm text-gray-600">Manage appointments with ease</p>
                  </div>

                  <div className="p-4 text-center border border-purple-100 bg-purple-50 rounded-xl">
                    <div className="inline-flex items-center justify-center w-12 h-12 mb-3 bg-purple-500 rounded-lg">
                      <Shield className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="mb-1 font-semibold text-gray-800">Premium Support</h3>
                    <p className="text-sm text-gray-600">Priority customer assistance</p>
                  </div>

                  <div className="p-4 text-center border border-orange-100 bg-orange-50 rounded-xl">
                    <div className="inline-flex items-center justify-center w-12 h-12 mb-3 bg-orange-500 rounded-lg">
                      <Zap className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="mb-1 font-semibold text-gray-800">Enhanced Analytics</h3>
                    <p className="text-sm text-gray-600">Detailed insights & reports</p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col justify-center gap-4 sm:flex-row">
                  <Link to={"/provider/dashboard"} className="flex-1 sm:flex-none">
                    <Button
                      size="lg"
                      className="w-full px-8 py-3 font-semibold text-white transition-all duration-200 transform shadow-lg sm:w-auto bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 rounded-xl hover:shadow-xl hover:scale-105"
                    >
                      Go to Dashboard
                      <ArrowRight className="w-5 h-5 ml-2" />
                    </Button>
                  </Link>

                  <Link to={"/provider/profile/subscription"} className="flex-1 sm:flex-none">
                    <Button
                      variant="outline"
                      size="lg"
                      className="w-full px-8 py-3 font-semibold text-gray-700 transition-all duration-200 border-2 border-gray-200 sm:w-auto hover:border-gray-300 rounded-xl hover:bg-gray-50"
                    >
                      View Subscription
                    </Button>
                  </Link>
                </div>

                {/* Additional Info */}
                <div className="pt-6 mt-8 text-center border-t border-gray-100">
                  <p className="text-sm text-gray-500">
                    Need help getting started?
                    <Link to="/provider/help" className="ml-1 font-medium text-green-600 hover:text-green-700">
                      Visit our help center
                    </Link>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}

export default SubscriptionSuccessPage;
