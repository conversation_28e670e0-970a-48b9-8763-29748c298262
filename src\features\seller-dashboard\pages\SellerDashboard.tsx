import Layout from "@/components/layout/SellerLayout";
import { useState } from "react";
import ProductTable from "../components/ProductTable";
import { getSellerProductsApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import AddProductDrawer from "../components/AddProductDrawer";
import DeleteModal from "../components/DeleteModal";
import Buttonwithicon from "@/components/ui/buttonwithicon";
import PlusIcon from "@/assets/white-plusicon.png";
import EditProductDrawer from "../components/EditProductDrawer";
import SellerProductCard from "../components/SellerProductCard";
import { Product } from "../type";
import SellerProductCardSkelton from "../components/SellerProductCardSkelton";

function Dashboard() {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [openDrawer, setOpenDrawer] = useState({ add: false, edit: false });
  const onCloseDrawer = (key: keyof typeof openDrawer) => {
    setOpenDrawer({ ...openDrawer, [key]: false });
  };
  const onOpen = () => setIsOpen(true);
  const onClose = () => setIsOpen(false);
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const {
    data,
    isPending: loading,
    isSuccess,
    refetch,
  } = useQuery({
    queryKey: ["products", currentPage],
    queryFn: () => getSellerProductsApi(currentPage),
  });
  const totalPages = isSuccess ? data?.data?.totalPages : 1;
  const handleDelete = (productId: string) => {
    onOpen();
    setSelectedProductId(productId);
  };
  const handleEdit = (productId: string) => {
    setOpenDrawer({ ...openDrawer, edit: true });
    setSelectedProductId(productId);
  };
  return (
    <>
      <Layout showFooter={true}>
        <div className="p-4 mx-auto my-5 border rounded-lg border-gray-2">
          <div className="flex justify-between">
            <div>
              <h1 className="text-lg font-bold">Dashboard</h1>
              <p className="text-neutral-300">Manage your inventory</p>
            </div>
            <Buttonwithicon
              onClick={() => setOpenDrawer({ ...openDrawer, add: true })}
              variant="button"
              icon={PlusIcon}
              text="Add Product"
            />
          </div>
          <div className="mt-5 hidden md:block max-h-[80vh] overflow-y-scroll scrollbar-hide">
            <ProductTable
              data={data?.data?.products || []}
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              loading={loading}
              editProduct={handleEdit}
              deleteProduct={handleDelete}
            />
          </div>
          <div className="flex flex-col mt-5 md:hidden gap-y-3">
            {loading ? (
              [1, 2, 3].map((_, i) => <SellerProductCardSkelton key={i} />)
            ) : data?.data?.products.length === 0 ? (
              <SellerProductCardSkelton />
            ) : (
              data?.data?.products.map((product: Product) => (
                <SellerProductCard key={product._id} product={product} />
              ))
            )}
          </div>
        </div>
      </Layout>
      <DeleteModal
        productId={selectedProductId}
        isOpen={isOpen}
        onClose={onClose}
      />
      {openDrawer.add && (
        <AddProductDrawer
          openDrawer={openDrawer.add}
          onCloseDrawer={() => onCloseDrawer("add")}
          prodcutId={selectedProductId}
          refetch={refetch}
        />
      )}
      {openDrawer.edit && (
        <EditProductDrawer
          openDrawer={openDrawer.edit}
          onCloseDrawer={() => onCloseDrawer("edit")}
          prodcutId={selectedProductId}
          refetch={refetch}
        />
      )}
    </>
  );
}

export default Dashboard;
