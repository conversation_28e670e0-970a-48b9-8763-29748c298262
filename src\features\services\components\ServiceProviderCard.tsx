import { ServiceProvider } from "../types";
import { IoLocationOutline } from "react-icons/io5";
import RatingStar from "@/assets/rating-star.png";
import CalendarIcon from "@/assets/calendar.png";
import { useNavigate } from "react-router-dom";

type ServiceProviderCardProps = {
  provider: ServiceProvider;
};

function ServiceProviderCard({ provider }: ServiceProviderCardProps) {
  const navigate = useNavigate();

  const handleCardClick = () => {
    navigate(`/services/${provider._id}`);
  };

  return (
    <div
      onClick={handleCardClick}
      className="border border-neutral-40 rounded-lg p-5 bg-white shadow-sm hover:shadow-md transition-shadow cursor-pointer"
    >
      <div className="flex min-h-[190px] gap-y-5 flex-col h-full">
        {/* Header with image, name, rating */}
        <div className="flex items-center gap-4">
          <div className="w-[72px] h-[72px] rounded-full overflow-hidden flex-shrink-0">
            <img
              src={provider.image}
              alt={provider.name}
              className="w-full h-full object-cover"
            />
          </div>
          <div className="flex-1">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="text-lg font-medium">
                  {provider.name}
                </h3>
                <p className="text-sm text-gray-500" >
                  {provider.specialty}
                </p>
              </div>
              <div className="flex items-center my-auto">
                <img src={RatingStar} className="text-orange-1 mr-1 h-5 text-lg" />
                <span className="font-medium text-base text-neutral-B300">
                  {provider.rating}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Location and Experience */}
        <div className="flex justify-between w-9/12 my-auto gap-6">
          <div className="flex items-center gap-1">
            <IoLocationOutline className="text-gray-500" />
            <span className="text-sm text-gray-500" style={{ fontSize: '14px' }}>
              {provider.location}
            </span>
          </div>
          <div className="flex items-center gap-1">
            <img src={CalendarIcon} className="text-gray-500 text-sm" />
            <span className="text-sm text-gray-500" style={{ fontSize: '14px' }}>
              {provider.experience}
            </span>
          </div>
        </div>

        {/* Description */}
        <div className="w-10/12">
          <p className="text-neutral-900" style={{ fontSize: '18px' }}>
            {provider.description}
          </p>
        </div>
      </div>
    </div>
  );
}

export default ServiceProviderCard;
