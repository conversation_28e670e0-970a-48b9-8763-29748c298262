import PageLayout from "@/components/layout/PageLayout";
import React from "react";
import AccountLayout from "./AccountLayout";
import DynamicBreadcrumb from "@/components/ui/dynamic-breadcrumb";

function AccountLayoutWrapper({ children }: { children: React.ReactNode }) {
  return (
    <PageLayout>
      <AccountLayout>
        <>
          <div className="mb-5 md:mb-0 md:hidden">
            <DynamicBreadcrumb />
          </div>
          <div className="w-full h-full">{children}</div>
        </>
      </AccountLayout>
    </PageLayout>
  );
}

export default AccountLayoutWrapper;
