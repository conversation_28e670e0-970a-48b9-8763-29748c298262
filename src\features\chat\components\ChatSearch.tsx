import SearchIcon from "@/assets/search.png";
import { Input } from "@/components/ui/input";
function ChatSearch({
  search,
  handleSearch,
}: {
  search: string;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
}) {
  return (
    <div className="flex items-center w-full px-2 border rounded-md gap-x-2 border-neutral-40">
      <img src={SearchIcon} alt="Search" className="my-auto " />
      <Input
        onChange={handleSearch}
        type="text"
        value={search}
        placeholder="Search"
        className="px-1 py-1 border-0 rounded-full"
      />
    </div>
  );
}

export default ChatSearch;
