import { Progress } from "@/components/ui/progress";
import { useQuestionnaireStore } from "../../../store/questionaireStore";
import { VerticalProgress } from "@/components/ui/verticalprogress";
import CheckedIcon from "@/assets/white-checked-icon.png";

import { phases } from "../utils";
import { useRef, useEffect } from "react";

function StepProgress() {
  const { currentPhaseIndex, currentProgress } = useQuestionnaireStore();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to active phase on mobile
  useEffect(() => {
    if (scrollContainerRef.current && typeof window !== "undefined") {
      const activePhaseElement = scrollContainerRef.current.children[
        currentPhaseIndex
      ] as HTMLElement;
      if (activePhaseElement) {
        const containerWidth = scrollContainerRef.current.offsetWidth;
        const scrollPosition =
          activePhaseElement.offsetLeft -
          containerWidth / 2 +
          activePhaseElement.offsetWidth / 2;
        scrollContainerRef.current.scrollTo({
          left: scrollPosition,
          behavior: "smooth",
        });
      }
    }
  }, [currentPhaseIndex]);

  const progressbarValue = (index: number) => {
    let result;

    if (currentPhaseIndex > index) {
      result = 100;
    } else if (currentPhaseIndex === index) {
      result = currentProgress[`${currentPhaseIndex}`];
    }
    return result;
  };
  const styleProgressbar = (index: number) => {
    return currentPhaseIndex >= index || currentProgress[`${index}`] > 98;
  };
  return (
    <>
      {/* Desktop/Tablet View - Vertical Layout */}
      <div className="hidden md:flex flex-col my-auto">
        <div className="flex flex-col items-start">
          {phases.map((phase, index) => (
            <div key={index} className="flex items-start gap-4">
              {/* Step Number and Vertical Line Container */}
              <div className="flex flex-col items-center">
                {/* Step Number */}
                <div
                  className={`flex items-center justify-center w-8 h-8 rounded-md ${
                    styleProgressbar(index)
                      ? "bg-orange-1 text-white font-bold"
                      : "bg-neutral-40 text-neutral-100"
                  }`}
                >
                  {currentPhaseIndex > index ? (
                    <img src={CheckedIcon} alt="" />
                  ) : (
                    index + 1
                  )}
                </div>
                {index < phases.length - 1 && (
                  <VerticalProgress
                    value={progressbarValue(index)}
                    className="h-28"
                  />
                )}
              </div>

              {/* Step Label */}
              <div className="flex flex-col">
                <span
                  className={`${
                    index === currentPhaseIndex
                      ? "text-white font-semibold leading-10"
                      : "text-gray-400"
                  }`}
                >
                  {phase}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Mobile View - Horizontal Layout */}
      <div className="md:hidden w-full">
        <div
          ref={scrollContainerRef}
          className="flex overflow-x-auto scrollbar-hide pb-4 pt-2"
        >
          {phases.map((phase, index) => (
            <div
              key={index}
              className={`flex-shrink-0 min-w-[180px] px-2 ${
                index === currentPhaseIndex ? "opacity-100" : "opacity-80"
              }`}
            >
              {/* Step Number and Label Row */}
              <div className="flex items-center mb-1">
                {/* Step Number */}
                <div
                  className={`flex items-center justify-center w-7 h-7 rounded-md mr-3 ${
                    styleProgressbar(index)
                      ? "bg-orange-1 text-white font-bold"
                      : "bg-neutral-40 text-neutral-100"
                  }`}
                >
                  {currentPhaseIndex > index ? (
                    <img src={CheckedIcon} alt="" />
                  ) : (
                    index + 1
                  )}
                </div>

                {/* Step Label */}
                <span
                  className={` whitespace-nowrap ${
                    index === currentPhaseIndex
                      ? "text-white font-semibold"
                      : "text-gray-400"
                  }`}
                >
                  {phase}
                </span>
              </div>

              {/* Progress Bar Row */}
              <div className="pr-4">
                <Progress
                  value={progressbarValue(index)}
                  className="h-1.5 bg-neutral-400 mt-4 md:mt-0 rounded-none w-full"
                />
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
}

export default StepProgress;
