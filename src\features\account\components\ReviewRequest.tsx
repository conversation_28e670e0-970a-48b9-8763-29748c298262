import { Button } from "@/components/ui/button";
import { useState } from "react";
import ReviewModal from "./ReviewModal";

function ReviewRequest({ productId }: { productId: string }) {
  const [isOpen, setIsOpen] = useState(false);
  const onOpen = () => setIsOpen(true);
  const onClose = () => setIsOpen(false);
  return (
    <>
      <div className="flex flex-col w-full p-5 rounded-lg md:flex-row gap-y-2 md:justify-between bg-tints-50">
        <h1 className="my-auto">
          The seller has requested for a review for this product.
        </h1>
        <Button onClick={onOpen}>Write a review</Button>
      </div>
      <ReviewModal productId={productId} isOpen={isOpen} onClose={onClose} />
    </>
  );
}

export default ReviewRequest;
