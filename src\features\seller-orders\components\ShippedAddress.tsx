import { dateFormatter } from "@/lib/utils";

function ShippedAddress({
  address,
  orderedOn,
  orderedBy,
}: {
  address: string;
  orderedOn: string;
  orderedBy: string;
}) {
  return (
    <div className="flex flex-col justify-evenly gap-y-2 p-5 md:min-h-[120px] border rounded-lg border-gray-2">
      <div className="flex justify-between w-11/12">
        <div className="flex flex-col gap-y-2">
          <h1 className="text-sm text-neutral-300">Ordered on</h1>
          <span className="text-neutral-600">{dateFormatter(orderedOn)}</span>
        </div>
        <div className="flex flex-col gap-y-2">
          <h1 className="text-sm text-neutral-300">Ordered by</h1>
          <span className="text-neutral-300">{orderedBy}</span>
        </div>
      </div>
      <div className="mt-5">
        <h1 className="font-bold ">Shipping to</h1>
        <h4>{address}</h4>
      </div>
    </div>
  );
}

export default ShippedAddress;
