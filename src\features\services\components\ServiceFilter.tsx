import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { X } from "lucide-react";
import CustomSlider from "@/components/ui/custom-slider";
import { ServiceFilterProps } from "../types";

// Mock data - replace with actual API calls when available
const specialties = [
  "Lactation Consultant",
  "Doula Services", 
  "Sleep Support",
  "Physical Recovery",
  "Infant Care Nursing",
  "Postpartum Care",
  "Mental Health Support"
];

const locations = ["Virtual", "In person"];

const availabilityOptions = ["This week", "This month", "Next month", "Date range"];

function ServiceFilter({
  selectedSpecialties,
  selectedLocations,
  priceRange,
  selectedAvailability,
  removeSpecialty,
  addSpecialty,
  addLocation,
  removeLocation,
  setPriceRange,
  addAvailability,
  removeAvailability,
}: ServiceFilterProps) {
  return (
    <div className="p-6 space-y-6 border rounded-lg border-neutral-40">
      <h2 className="font-medium">Filter by</h2>

      {/* Specialty Section */}
      <div className="space-y-3">
        <h3 className="font-medium">Specialty</h3>

        {/* Selected Specialties */}
        <div className="flex flex-wrap gap-2 mb-3">
          {selectedSpecialties?.map((specialty) => (
            <div
              key={specialty}
              className="flex items-center gap-1 px-3 py-1.5 rounded-full bg-tints-70"
            >
              <span className="text-sm">{specialty}</span>
              <button
                onClick={() => removeSpecialty(specialty)}
                className="text-neutral-600 hover:text-neutral-900"
              >
                <X size={14} />
              </button>
            </div>
          ))}
        </div>

        {/* Specialty Dropdown */}
        <Select onValueChange={addSpecialty}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            {specialties?.map((specialty: string) => (
              <SelectItem key={specialty} value={specialty}>
                {specialty}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Price Range Section */}
      <div className="space-y-3">
        <div className="flex justify-between">
          <h3 className="font-medium">Price range</h3>
          <span className="text-neutral-300">
            ${priceRange[0]}-${priceRange[1]}+
          </span>
        </div>

        <div className="pt-4 pb-2">
          <CustomSlider
            defaultValue={[priceRange[0], priceRange[1]]}
            max={5000}
            step={100}
            value={priceRange}
            onValueChange={setPriceRange}
            className="mt-2"
          />
        </div>
      </div>

      {/* Location Section */}
      <div className="space-y-3">
        <h3 className="font-medium">Location</h3>

        {/* Selected Locations */}
        <div className="flex flex-wrap gap-2 mb-3">
          {selectedLocations?.map((location) => (
            <div
              key={location}
              className="flex items-center gap-1 px-3 py-1.5 rounded-full bg-tints-70"
            >
              <span className="text-sm">{location}</span>
              <button
                onClick={() => removeLocation(location)}
                className="text-neutral-600 hover:text-neutral-900"
              >
                <X size={14} />
              </button>
            </div>
          ))}
        </div>

        {/* Location Dropdown */}
        <Select onValueChange={addLocation}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            {locations?.map((location: string) => (
              <SelectItem key={location} value={location}>
                {location}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Availability Section */}
      <div className="space-y-3">
        <h3 className="font-medium">Availability</h3>

        {/* Selected Availability */}
        <div className="flex flex-wrap gap-2 mb-3">
          {selectedAvailability?.map((availability) => (
            <div
              key={availability}
              className="flex items-center gap-1 px-3 py-1.5 rounded-full bg-tints-70"
            >
              <span className="text-sm">{availability}</span>
              <button
                onClick={() => removeAvailability(availability)}
                className="text-neutral-600 hover:text-neutral-900"
              >
                <X size={14} />
              </button>
            </div>
          ))}
        </div>

        {/* Availability Dropdown */}
        <Select onValueChange={addAvailability}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            {availabilityOptions?.map((availability: string) => (
              <SelectItem key={availability} value={availability}>
                {availability}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}

export default ServiceFilter;
