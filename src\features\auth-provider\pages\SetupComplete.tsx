import { Navigate } from "react-router-dom";
import { checkAuthentication } from "@/lib/utils";
import AuthLayout from "../components/AuthLayout";
import ProfileSetupStep5 from "../components/ProfileSetupStep5";

function SetupComplete() {
  const { isLoggedIn, href } = checkAuthentication();

  return isLoggedIn ? (
    <Navigate to={href} />
  ) : (
    <AuthLayout isStepComponent={false}>
      <div className="w-full max-w-md text-center space-y-6 p-8">
        <ProfileSetupStep5 />
      </div>
    </AuthLayout>
  );
}

export default SetupComplete;
