import { sanityClient } from "@/lib/sanity-client";

const PAGE_SIZE = 6;

export const fetchBlogsApi = async (
  lastId: string | null = null,
  limit?: number
) => {
  const baseQuery = lastId
    ? `*[_type == "blog" && _id > $lastId]|order(_id)`
    : `*[_type == "blog"]|order(_id)`;

  const fullQuery = `${baseQuery}[0...${limit || PAGE_SIZE}]`;

  const data = await sanityClient.fetch(
    `${fullQuery}{
      _id,
      title,
      slug,
      coverImage,
      publishedAt,
    }`,
    lastId ? { lastId } : {}
  );
  return data;
};
export const fetchBlogDetailsApi = async (slug: string) => {
  const data = await sanityClient.fetch(
    `*[_type == "blog" && slug.current == $slug][0]{
    _id,
    title,
    slug,
    mainImage,
    publishedAt,
    content,
  }`,
    { slug }
  );
  return data;
};

export const fetchBlogCountApi = async () => {
  const data = await sanityClient.fetch(`count(*[_type == "blog"])`);
  return Math.ceil(data / PAGE_SIZE);
};
