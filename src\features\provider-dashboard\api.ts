import api from "@/lib/axios";

// Types for API responses
export interface BasicProfileResponse {
  status: string;
  message: string;
  data: {
    provider: {
      _id: string;
      role: string;
      phone: string;
      firstName: string;
      lastName: string;
      specialty: string;
      businessName: string;
      taxId: string;
      description: string;
      experience: number;
      serviceLocation: string[];
      profilePicture: Array<{
        url: string;
        key: string;
      }>;
    };
  };
}

export interface AdditionalProfileResponse {
  status: string;
  message: string;
  data: {
    provider: {
      _id: string;
      role: string;
      phone: string;
      experience: number;
      serviceLocation: string[];
      introductionVideo: {
        url: string;
        key: string;
      };
      certifications: Array<{
        url: string;
        key: string;
        _id: string;
      }>;
      profilePicture: Array<{
        url: string;
        key: string;
      }>;
      refundPolicy: string;
      highlights: string[];
      photos: Array<{
        url: string;
        key: string;
      }>;
    };
  };
}

// Combined provider profile type for components
export interface ProviderProfile {
  _id: string;
  firstName: string;
  lastName: string;
  specialty: string;
  businessName: string;
  taxId: string;
  description: string;
  phone: string;
  experience: number;
  serviceLocation: string[];
  profilePicture: Array<{
    url: string;
    key: string;
  }>;
  introductionVideo?: {
    url: string;
    key: string;
  };
  certifications: Array<{
    url: string;
    key: string;
    _id: string;
  }>;
  refundPolicy: string;
  highlights: string[];
  photos: Array<{
    url: string;
    key: string;
  }>;
}

// API functions
export const getProviderBasicProfileApi =
  async (): Promise<BasicProfileResponse> => {
    const { data } = await api.get("/api/v1/users/provider/profile/basic");
    return data;
  };

export const getProviderAdditionalProfileApi =
  async (): Promise<AdditionalProfileResponse> => {
    const { data } = await api.get("/api/v1/users/provider/profile/additonal");
    return data;
  };

// Update provider basic profile
export const updateProviderBasicProfileApi = async (
  formData: FormData
): Promise<BasicProfileResponse> => {
  const { data } = await api.patch(
    "/api/v1/users/provider/profile/basic",
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return data;
};

// Update provider additional profile
export const updateProviderAdditionalProfileApi = async (
  formData: FormData
): Promise<AdditionalProfileResponse> => {
  const { data } = await api.patch(
    "/api/v1/users/provider/profile/additonal",
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return data;
};

// Combined API call to get both basic and additional profile data
export const getProviderCompleteProfileApi =
  async (): Promise<ProviderProfile> => {
    const [basicResponse, additionalResponse] = await Promise.all([
      getProviderBasicProfileApi(),
      getProviderAdditionalProfileApi(),
    ]);

    const basicData = basicResponse.data.provider;
    const additionalData = additionalResponse.data.provider;

    // Combine both responses into a single profile object
    return {
      _id: basicData._id,
      firstName: basicData.firstName,
      lastName: basicData.lastName,
      specialty: basicData.specialty,
      businessName: basicData.businessName,
      taxId: basicData.taxId,
      description: basicData.description,
      phone: basicData.phone,
      experience: basicData.experience,
      serviceLocation: basicData.serviceLocation,
      profilePicture: basicData.profilePicture,
      introductionVideo: additionalData.introductionVideo,
      certifications: additionalData.certifications || [],
      refundPolicy: additionalData.refundPolicy || "",
      highlights: additionalData.highlights || [],
      photos: additionalData.photos || [],
    };
  };

// Types for provider services
export interface ProviderService {
  _id: string;
  title: string;
  duration: number;
  price: number;
  description: string;
}

export interface ProviderServicesResponse {
  status: string;
  message: string;
  data: {
    services: ProviderService[];
    totalPages: number;
  };
}

// Get provider's own services
export const getProviderServicesApi = async (
  providerId: string,
  page: number = 1
): Promise<ProviderServicesResponse> => {
  const { data } = await api.get(
    `/api/v1/users/provider/${providerId}/service`,
    {
      params: { page },
    }
  );
  return data;
};

// Service creation types and API
export interface ServiceCreationPayload {
  title: string;
  providerId: string;
  duration: number;
  description: string;
  price: number;
  highlights?: string[];
}

export interface ServiceCreationResponse {
  status: string;
  message: string;
  data?: {
    services: {
      _id: string;
      title: string;
      duration: number;
      price: number;
      description: string;
      highlights?: string[];
      providerId: string;
      createdAt: string;
      updatedAt: string;
    };
  };
}

// Create a new service
export const createServiceApi = async (
  payload: ServiceCreationPayload
): Promise<ServiceCreationResponse> => {
  try {
    console.log("Creating service with API:", payload);
    console.log("Provider ID in payload:", payload.providerId);

    const response = await api.post("/api/v1/service", payload);
    console.log("Service creation API response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Service creation API error:", error);
    throw error;
  }
};

// Service details types and API
export interface ServiceDetails {
  _id: string;
  providerId: string;
  title: string;
  duration: number;
  highlights: string[];
  price: number;
  description: string;
  serviceLocation: string[];
  createdAt: string;
  __v: number;
}

export interface ServiceDetailsResponse {
  status: string;
  message: string;
  data: {
    service: ServiceDetails;
  };
}

// Get service details by ID
export const getServiceDetailsApi = async (
  serviceId: string
): Promise<ServiceDetailsResponse> => {
  try {
    const response = await api.get(`/api/v1/service/${serviceId}`);
    return response.data;
  } catch (error) {
    console.error("Service details API error:", error);
    throw error;
  }
};

// Update service types and API
export interface UpdateServicePayload {
  title: string;
  duration: number;
  highlights: string[];
  price: number;
  description: string;
}

export interface UpdateServiceResponse {
  status: string;
  message: string;
  data: {
    service: ServiceDetails;
  };
}

export interface DeleteServiceResponse {
  status: string;
  message: string;
  data: Record<string, never>;
}

// Update service by ID
export const updateServiceApi = async (
  serviceId: string,
  payload: UpdateServicePayload
): Promise<UpdateServiceResponse> => {
  try {
    const response = await api.patch(
      `/api/v1/users/provider/service/${serviceId}`,
      payload
    );
    return response.data;
  } catch (error) {
    console.error("Update service API error:", error);
    throw error;
  }
};

// Delete service by ID
export const deleteServiceApi = async (
  serviceId: string
): Promise<DeleteServiceResponse> => {
  try {
    const response = await api.delete(
      `/api/v1/users/provider/service/${serviceId}`
    );
    return response.data;
  } catch (error) {
    console.error("Delete service API error:", error);
    throw error;
  }
};

export const fetchPromotionalOffersApi = async () => {
  const { data } = await api.get("/api/v1/coupon");
  return data.data;
};

export const addPromotionalOfferApi = async (payload: {
  code: string;
  discount: number;
}) => {
  const { data } = await api.post("/api/v1/coupon", payload);
  return data;
};
export const DeletePromotionalOfferApi = async (couponId: string) => {
  const { data } = await api.delete(`/api/v1/coupon/${couponId}`);
  return data;
};

export const fetchProviderAvailabilityApi = async () => {
  const { data } = await api.get("/api/v1/users/provider/availability");
  return data.data;
};

export const updateProviderAvailabilityApi = async (payload: {
  availability: string[];
}) => {
  const { data } = await api.patch(
    "/api/v1/users/provider/availability",
    payload
  );
  return data;
};
