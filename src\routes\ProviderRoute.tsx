import { Route, Routes } from "react-router-dom";
import { lazy, Suspense } from "react";
import Loader from "@/components/ui/loader";
import ProtectedRoutes from "./ProtectedRoutes";
import ProviderRegister from "@/features/auth-provider/pages/Register";
import ProviderLogin from "@/features/auth-provider/pages/Login";
import ProviderOtp from "@/features/auth-provider/pages/Otp";
import Dashboard from "@/features/provider-dashboard/pages/Dashboard";
import ServicesPage from "@/features/provider-dashboard/pages/Services";
import ServiceDetails from "@/features/provider-dashboard/pages/ServiceDetails";
import Profile from "@/features/provider-dashboard/pages/Profile";
import ProviderChatPage from "@/features/chat/pages/ProviderChatPage";
import SubscriptionFailurePage from "@/features/provider-dashboard/pages/SubscriptionFailurePage";
import SubscriptionSuccessPage from "@/features/provider-dashboard/pages/SubscriptionSuccessPage";

// Lazy loaded components
const NotFound = lazy(() => import("@/components/ui/not-found"));
const ProviderProfileSetup = lazy(
  () => import("@/features/auth-provider/pages/ProfileSetup")
);
const PlanSelection = lazy(
  () => import("@/features/auth-provider/pages/PlanSelection")
);
const SetupComplete = lazy(
  () => import("@/features/auth-provider/pages/SetupComplete")
);
const ForgotPassword = lazy(
  () => import("@/features/auth-provider/pages/ForgotPassword")
);
const CheckEmail = lazy(
  () => import("@/features/auth-provider/pages/CheckEmail")
);
const ProviderResetPassword = lazy(
  () => import("@/features/auth-provider/pages/ResetPassword")
);
const SubscriptionFailure = lazy(
  () => import("@/features/auth-provider/pages/SubscriptionFailure")
);

function ProviderRoute() {
  return (
    <Suspense fallback={<Loader />}>
      <Routes>
        <Route element={<ProtectedRoutes role="provider" />}>
          <Route path={"/dashboard"} element={<Dashboard />} />
          <Route path={"/services"} element={<ServicesPage />} />
          <Route path={"/services/:serviceId"} element={<ServiceDetails />} />
          <Route path={"/reviews"} element={<div>Reviews Page</div>} />
          <Route path={"/profile/*"} element={<Profile />} />
          <Route path={"/messages"} element={<ProviderChatPage />} />
        </Route>

        <Route path={"/register"} element={<ProviderRegister />} />
        <Route path={"/login"} element={<ProviderLogin />} />
        <Route path={"/verify-otp"} element={<ProviderOtp />} />
        <Route path={"/setup-profile"} element={<ProviderProfileSetup />} />
        <Route
          path={"/setup-profile/plan-selection"}
          element={<PlanSelection />}
        />
        <Route
          path={"/setup-profile/pending-verification"}
          element={<SetupComplete />}
        />
        <Route
          path={"/setup-profile/subscription-failure"}
          element={<SubscriptionFailure />}
        />
        <Route
          path={"/subscription/failure"}
          element={<SubscriptionFailurePage />}
        />
        <Route
          path={"/subscription/success"}
          element={<SubscriptionSuccessPage />}
        />
        <Route path={"/forgot-password"} element={<ForgotPassword />} />
        <Route path={"/forgot-password/check-email"} element={<CheckEmail />} />
        <Route path={"/reset-password"} element={<ProviderResetPassword />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Suspense>
  );
}

export default ProviderRoute;
