import * as React from "react";

import { cn } from "@/lib/utils";

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        {...props}
        type={type}
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base shadow-none transition-colors placeholder:text-muted-foreground focus:outline-none focus:ring-0 focus:border-input",
          className
        )}
        ref={ref}
      />
    );
  }
);
Input.displayName = "Input";

export { Input };
