import { Skeleton } from "@/components/ui/skeleton";

function ProviderCardSkeleton() {
  return (
    <div className="h-full p-5 bg-white border rounded-lg shadow-sm border-neutral-40">
      <div className="flex bg-white min-h-[190px] gap-y-5 flex-col h-full">
        <div className="flex items-start gap-4">
          <Skeleton className="w-[72px] h-[72px] rounded-full flex-shrink-0" />

          <div className="flex-1">
            <div className="flex items-start justify-between">
              <div>
                <Skeleton className="w-[120px] h-[20px] mb-2 rounded" />
                <Skeleton className="w-[80px] h-[16px] rounded" />
              </div>
              <div className="flex items-center gap-1 my-auto">
                <Skeleton className="w-5 h-5 rounded-full" />
                <Skeleton className="w-[32px] h-[16px] rounded" />
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-between w-9/12 gap-6 my-auto">
          <div className="flex items-center gap-2">
            <Skeleton className="w-4 h-4 rounded" />
            <Skeleton className="w-[60px] h-[16px] rounded" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="w-4 h-4 rounded" />
            <Skeleton className="w-[60px] h-[16px] rounded" />
          </div>
        </div>

        <div className="flex flex-wrap w-10/12 gap-2">
          <Skeleton className="w-[80px] h-[24px] rounded" />
          <Skeleton className="w-[60px] h-[24px] rounded" />
          <Skeleton className="w-[100px] h-[24px] rounded" />
        </div>
      </div>
    </div>
  );
}

export default ProviderCardSkeleton;
