import { Modal } from "@/components/ui/modal";
import { CertificationItem } from "../type";
import { Link } from "react-router-dom";

function CertificationModal({
  isOpen,
  onClose,
  providerName,
  certifications,
}: {
  isOpen: boolean;
  onClose: () => void;
  providerName: string;
  certifications: CertificationItem[];
}) {
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div>
        <h1 className="text-lg font-semibold">
          {providerName}'s certifications
        </h1>
        <ul className="mt-2 list-decimal list-inside text-neutral-300">
          {certifications.map((cert) => (
            <li key={cert._id}>
              <Link to={cert.url}  target="_blank" rel="noopener noreferrer">
                {cert.key.split('/').pop()}
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </Modal>
  );
}

export default CertificationModal;
