import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { showToast } from "@/lib/toast";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { deleteSellerProductsApi } from "../api";
import { useNavigate } from "react-router-dom";

function DeleteModal({
  isOpen,
  onClose,
  productId,
  shoulNavigate = false,
  refetch,
}: {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  shoulNavigate?: boolean;
  refetch?: () => void;
}) {
  const navigate = useNavigate();
  const { mutate, isPending: loading } = useMutation({
    mutationFn: deleteSellerProductsApi,
    onSuccess: () => {
      showToast("Product deleted successfully!", "success");
      if (shoulNavigate) {
        navigate(-1);
      }
      if (refetch) {
        refetch();
      }
      onClose();
    },
    onError: (error: AxiosError) => {
      console.error(error.response?.data);
      showToast("Failed to delete product", "error");
    },
  });

  const handleDelete = () => {
    mutate(productId);
  };
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="">
        <h2 className="mb-2 text-lg font-semibold text-gray-900">
          Are you sure you want to delete?
        </h2>
        <div className="flex justify-end gap-4 mt-5">
          <Button variant={"outline"} onClick={onClose}>
            No
          </Button>
          <Button
            disabled={loading}
            className="rounded-full"
            variant={"destructive"}
            onClick={handleDelete}
          >
            Yes, Delete
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default DeleteModal;
