import { Button } from "@/components/ui/button";
import { Star } from "lucide-react";

export interface ProfileHeaderProps {
  name: string;
  rating: number;
  specialty: string;
  location: string;
  phone: string;
  experience: string;
  image: string;
  onEditClick?: () => void;
}

function ProfileHeader({
  name,
  rating,
  specialty,
  location,
  phone,
  experience,
  image,
  onEditClick,
}: ProfileHeaderProps) {
  const handleEditClick = () => {
    if (onEditClick) {
      onEditClick();
    }
  };

  return (
    <div className="flex flex-col md:flex-row gap-4 mb-8">
      {/* Profile Image */}
      <div className="w-24 h-24 rounded-full overflow-hidden flex-shrink-0">
        <img
          src={image}
          alt={name}
          className="w-full h-full object-cover"
        />
      </div>

      {/* Profile Info */}
      <div className="flex-grow ml-3">
        <div className="flex justify-between items-start">
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-normal">{name}</h1>
              <div className="flex items-center gap-1">
                <Star className="w-4 h-4 fill-orange-1 text-orange-1" />
                <span className="text-base mt-1 text-gray-600">{rating}</span>
              </div>
            </div>
            <div className="flex items-center">
              <p className="text-gray-600 whitespace-nowrap mt-1">Specialist in {specialty}</p>
              {/* <span className="text-2xl mt-2 mx-2">•</span>
              <button className="text-[#577DCF] text-sm mt-2 hover:underline">
                View certifications
              </button> */}
            </div>
          </div>
          <Button
            variant="outline"
            onClick={handleEditClick}
            className="text-orange-1 border-orange-1"
          >
            Edit profile
          </Button>
        </div>

        {/* Additional Info */}
        <div className="flex flex-wrap gap-4 mt-4 text-neutral-600 max-w-[500px]">
          <div className="flex items-center gap-2">
            <img src="/src/assets/location.png" alt="Location Icon" className="w-5 h-5" />
            <p>{location}</p>
          </div>
          <div className="flex items-center gap-2">
            <img src="/src/assets/phone.png" alt="Phone Icon" className="w-5 h-5" />
            <p>{phone}</p>
          </div>
          <div className="flex items-center gap-2">
            <img src="/src/assets/calender.png" alt="Calendar Icon" className="w-5 h-5" />
            <p>{experience}</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProfileHeader;
