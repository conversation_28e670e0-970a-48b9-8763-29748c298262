import api from "@/lib/axios";
import {
  forgotPasswordPayload,
  CustomerGoogleApiResponse,
  LoginPayload,
  OtpPayloadData,
  profileData,
  RegisterPayload,
  resetPasswordPayload,
} from "./type";

export const googleLoginApi = async (
  token: string
): Promise<{
  data: CustomerGoogleApiResponse;
}> => {
  const { data } = await api.post("/api/v1/auth/customer/google", { token });
  return data;
};

export const registerApi = async (payload: RegisterPayload) => {
  const { data } = await api.post("/api/v1/auth/signup", payload);
  return data;
};

export const verifyOtpApi = async (payload: OtpPayloadData) => {
  const { data } = await api.post("/api/v1/auth/verify-otp", payload);
  return data;
};

export const setupProfileApi = async (payload: profileData) => {
  const { data } = await api.post("/api/v1/users/customer", payload);
  return data;
};

export const loginApi = async (payload: LoginPayload) => {
  const { data } = await api.post("/api/v1/auth/login", payload);
  return data;
};

export const forgotPasswordApi = async (payload: forgotPasswordPayload) => {
  const { data } = await api.post("/api/v1/auth/forgot-password", payload);
  return data;
};

export const validateResetPasswordTokenApi = async (payload: string) => {
  const { data } = await api.get(`/api/v1/auth/validate-reset-token`, {
    params: {
      token: payload,
    },
  });
  return data;
};
export const resetPasswordApi = async (payload: resetPasswordPayload) => {
  const { data } = await api.post("/api/v1/auth/reset-password", payload);
  return data;
};
