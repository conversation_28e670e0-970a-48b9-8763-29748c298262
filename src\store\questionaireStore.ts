import { PhaseQuestions } from "@/features/questionaire/type";
import { create } from "zustand";

type QuestionnaireState = {
  currentPhaseIndex: number;
  currentQuestionIndex: number;
  answers: Record<string, Array<string>>;
  skipped: boolean;
  currentProgress: Record<string, number>;

  setAnswer: (key: string, value: Array<string>) => void;
  goToNextQuestion: (phases: PhaseQuestions[]) => void;
  goToPrevQuestion: (phases: PhaseQuestions[]) => void;
  updateCurrentProgess: (key: string, value: number) => void;
  skipAll: () => void;
};

export const useQuestionnaireStore = create<QuestionnaireState>((set, get) => ({
  currentPhaseIndex: 0,
  currentQuestionIndex: 0,
  answers: {},
  skipped: false,
  currentProgress: {},

  setAnswer: (key, value) =>
    set((state) => ({
      answers: {
        ...state.answers,
        [key]: value,
      },
    })),

  goToNextQuestion: (phases) => {
    const { currentPhaseIndex, currentQuestionIndex } = get();
    const currentPhase = phases[currentPhaseIndex];

    if (currentQuestionIndex < currentPhase.questions.length - 1) {
      set({ currentQuestionIndex: currentQuestionIndex + 1 });
    } else if (currentPhaseIndex < phases.length - 1) {
      set({
        currentPhaseIndex: currentPhaseIndex + 1,
        currentQuestionIndex: 0,
      });
    }
  },
  goToPrevQuestion: (phases) => {
    const { currentPhaseIndex, currentQuestionIndex } = get();
    const prevPhase = phases[currentPhaseIndex - 1];
    if (currentQuestionIndex > 0) {
      set({ currentQuestionIndex: currentQuestionIndex - 1 });
    } else if (currentQuestionIndex === 0 && currentPhaseIndex > 0) {
      set({
        currentPhaseIndex: currentPhaseIndex - 1,
        currentQuestionIndex: prevPhase.questions.length - 1,
      });
    }
  },
  updateCurrentProgess: (key, value) =>
    set((state) => ({
      currentProgress: {
        ...state.currentProgress,
        [key]: value > 100 ? 100 : value,
      },
    })),

  skipAll: () =>
    set({
      skipped: true,
      currentPhaseIndex: 0,
      currentQuestionIndex: 0,
      answers: {},
      currentProgress: {},
    }),
}));
