import AuthLayout from "../components/AuthLayout";
import FingerPrintImage from "@/assets/fingerprint.png";
import { checkAuthentication } from "@/lib/utils";
import { Link, Navigate } from "react-router-dom";

function VerifyAccount() {
  const { isLoggedIn, href } = checkAuthentication();

  if (isLoggedIn) {
    return <Navigate to={href} />;
  }
  return (
    <AuthLayout>
      <div className="w-full max-w-md text-center">
        <div className="flex flex-col ">
          <Link to={"/"}>
            <img
              src={FingerPrintImage}
              alt="Fingerprint"
              className="mx-auto object-cover"
            />
          </Link>
          <div className="w-full flex flex-col items-center gap-y-5 my-6">
            <h1 className="text-3xl mx-auto font-medium text-center flex-grow font-prettywise">
              Your account is being verified.
            </h1>
            <p className="text-gray-500  mb-6">
              Lorem ipsum dolor sit amet consectetur. Elit pulvinar sed lorem et
              justo sagittis Habitasse.
            </p>
          </div>
        </div>
      </div>
    </AuthLayout>
  );
}

export default VerifyAccount;
