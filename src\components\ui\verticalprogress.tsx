import * as React from "react";
import * as ProgressPrimitive from "@radix-ui/react-progress";

import { cn } from "@/lib/utils";

const VerticalProgress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>
>(({ className, value, ...props }, ref) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn(
      "relative w-1.5 h-full overflow-hidden  bg-neutral-400", // vertical orientation
      className
    )}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className="absolute top-0 left-0 w-full bg-orange-1 transition-all"
      style={{
        height: `${value || 0}%`, // vertical fill
      }}
    />
  </ProgressPrimitive.Root>
));
VerticalProgress.displayName = ProgressPrimitive.Root.displayName;

export { VerticalProgress };
