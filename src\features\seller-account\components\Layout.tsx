import SellerLayout from "@/components/layout/SellerLayout";
import DynamicBreadcrumb from "@/components/ui/dynamic-breadcrumb";
import React from "react";
import { Link, useLocation } from "react-router-dom";

function LayoutWrapper({
  children,
  hideOptions,
  hideBreadCrumb,
}: {
  children: React.ReactNode;
  hideOptions?: boolean;
  hideBreadCrumb?: boolean;
}) {
  const { pathname } = useLocation();

  const navItems = [
    {
      text: "Basic details",
      href: "/seller/account/basic-details",
    },
    {
      text: "Promotional offers",
      href: "/seller/account/promotional-offers",
    },
  ];
  return (
    <SellerLayout>
      <>
        <div
          className={`${hideBreadCrumb ? "hidden" : "block"} mb-5 md:mb-0 md:hidden`}
        >
          <DynamicBreadcrumb />
        </div>
        <h1 className="mt-5 text-lg font-semibold md:mt-10 md:text-xl">
          My account
        </h1>
        <div className="grid w-full h-full grid-cols-12 mt-5 md:mt-10 gap-x-5">
          <div
            className={`${hideOptions ? "hidden md:flex" : "flex"} md:min-h-[60vh] flex-col col-span-12 p-4 gap-y-3 md:col-span-3 md:border-r border-r-gray-2`}
          >
            {navItems.map((item) => (
              <Link
                to={item.href}
                key={item.text}
                className={`${
                  pathname === item.href ? "font-semibold" : "font-normal"
                } text-neutral-300`}
              >
                {item.text}
              </Link>
            ))}
          </div>
          <div className="col-span-12 md:col-span-9">{children}</div>
        </div>
      </>
    </SellerLayout>
  );
}

export default LayoutWrapper;
