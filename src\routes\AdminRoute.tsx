import { Route, Routes } from "react-router-dom";
import ProtectedRoutes from "./ProtectedRoutes";
import Login from "@/features/admin/pages/Login";
import Sellers from "@/features/admin/pages/Sellers";
import NotFound from "@/components/ui/not-found";
import Providers from "@/features/admin/pages/Providers";
import AdminChatPage from "@/features/chat/pages/AdminChatPage";

function AdminRoute() {
  return (
    <Routes>
      <Route element={<ProtectedRoutes role="admin" />}>
        <Route path={"/sellers"} element={<Sellers />} />
        <Route path={"/providers"} element={<Providers />} />
        <Route path={"/chat"} element={<AdminChatPage />} />
      </Route>
      <Route path={"/login"} element={<Login />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default AdminRoute;
