import LoginForm from "../components/LoginForm";
import AuthLayout from "../components/AuthLayout";
import { Navigate } from "react-router-dom";
import { checkAuthentication } from "@/lib/utils";
function Login() {
  const { isLoggedIn, href } = checkAuthentication();

  return isLoggedIn ? (
    <Navigate to={href} />
  ) : (
    <AuthLayout>
      <LoginForm />
    </AuthLayout>
  );
}

export default Login;
