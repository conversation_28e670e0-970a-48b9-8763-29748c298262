import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import FingerPrintImage from "@/assets/fingerprint.png";
import { Link } from "react-router-dom";
import { useForm } from "react-hook-form";
import { joi<PERSON><PERSON>olver } from "@hookform/resolvers/joi";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { LuEye } from "react-icons/lu";
import { PiEyeSlash } from "react-icons/pi";

import { resetPasswordPayload } from "../type";
import { resetPasswordApi, validateResetPasswordTokenApi } from "../api";
import { resetPasswordSchema } from "../validation";
import { useNavigate, useSearchParams } from "react-router-dom";
import { showToast } from "@/lib/toast";

function ResetPasswordForm() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Validate token when component mounts
  const { isError: isTokenInvalid } = useQuery({
    queryKey: ["validate-token", token],
    queryFn: () => validateResetPasswordTokenApi(token || ""),
    enabled: !!token,
    retry: false,
  });

  // Handle token validation error
  useEffect(() => {
    if (isTokenInvalid) {
      showToast("Invalid or expired token", "error");
      navigate("/provider/forgot-password");
    }
  }, [isTokenInvalid, navigate]);

  // Redirect if token is missing
  useEffect(() => {
    if (!token) {
      showToast("Token is required", "error");
      navigate("/provider/forgot-password");
    }
  }, [token, navigate]);


  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<resetPasswordPayload>({
    resolver: joiResolver(resetPasswordSchema),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: resetPasswordApi,
    onSuccess: () => {
      showToast("Password reset successfully", "success");
      navigate("/provider/login");
    },
    onError: () => {
      showToast("Something went wrong", "error");
    },
  });

  const onSubmit = (data: resetPasswordPayload) => {
    if (!token || isTokenInvalid) {
      showToast("Invalid token", "error");
      return;
    }
    mutate({ ...data, token });
  };

  return (
    <div className="w-full max-w-md space-y-6 text-center">
      <div className="space-y-2">
        <Link to={"/"}>
          <img
            src={FingerPrintImage}
            alt="Fingerprint"
            className="object-cover mx-auto"
          />
        </Link>
        <h1 className="text-3xl font-bold md:text-4xl font-prettywise">
          Reset Password
        </h1>
        <p className="text-gray-500">Enter your new password</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-4 text-left">
          {/* New Password Input */}
          <div>
            <div className="flex border rounded-sm border-input-border focus:border-slate-300">
              <Input
                className="border-none"
                placeholder="New Password"
                type={showPassword ? "text" : "password"}
                {...register("newPassword")}
              />
              {showPassword ? (
                <PiEyeSlash
                  onClick={() => setShowPassword(false)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              ) : (
                <LuEye
                  onClick={() => setShowPassword(true)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              )}
            </div>
            {errors.newPassword && (
              <p className="text-sm text-red-500">
                {errors.newPassword.message}
              </p>
            )}
          </div>

          {/* Confirm Password Input */}
          <div>
            <div className="flex border rounded-sm border-input-border focus:border-slate-300">
              <Input
                className="border-none"
                placeholder="Confirm Password"
                type={showConfirmPassword ? "text" : "password"}
                {...register("confirmPassword")}
              />
              {showConfirmPassword ? (
                <PiEyeSlash
                  onClick={() => setShowConfirmPassword(false)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              ) : (
                <LuEye
                  onClick={() => setShowConfirmPassword(true)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              )}
            </div>
            {errors.confirmPassword && (
              <p className="text-sm text-red-500">
                {errors.confirmPassword.message}
              </p>
            )}
          </div>
        </div>

        <div className="flex flex-col mt-6 space-y-2 md:mt-12 gap-y-3 md:gap-y-1">
          <Button
            type="submit"
            className="w-full p-2 hover:bg-[#c65a3c]"
            disabled={isPending}
          >
            {isPending ? "Resetting..." : "Reset Password"}
          </Button>
        </div>
      </form>
      <Link
        to="/provider/login"
        className="flex items-center justify-center gap-2 font-semibold"
      >
        <span>&larr;</span> Back to login
      </Link>
    </div>
  );
}

export default ResetPasswordForm;
