// API Response Types
export type ApiService = {
  id: string;
  title: string;
  price: number;
};

export type ApiProvider = {
  id: string;
  firstName: string;
  specialty: string;
  experience: number;
  serviceLocation: string[];
  profilePicture: Array<{
    image?: string;
    url?: string;
    key: string;
  }>;
};

export type ApiServiceProvider = {
  provider: ApiProvider;
  services: ApiService[];
};

export type ServicesApiResponse = {
  status: string;
  message: string;
  data: {
    services: ApiServiceProvider[];
    totalPages: number;
  };
};

// UI Component Types (for backward compatibility)
export type ServiceProvider = {
  _id: string;
  image: string;
  name: string;
  specialty: string;
  rating: number;
  experience: string;
  location: string;
  description: string;
  services: string[];
};

export type ServiceFilterProps = {
  selectedSpecialties: string[];
  selectedLocations: string[];
  priceRange: number[];
  selectedAvailability: string[];
  removeSpecialty: (specialty: string) => void;
  addSpecialty: (value: string) => void;
  addLocation: (value: string) => void;
  removeLocation: (location: string) => void;
  setPriceRange: (value: number[]) => void;
  addAvailability: (value: string) => void;
  removeAvailability: (availability: string) => void;
  clearFilter?: () => void;
};

export type ServiceQueryParams = {
  page?: number;
  search?: string;
  speciality?: string;
  minPrice?: number;
  maxPrice?: number;
  location?: string;
  availability?: string;
};
