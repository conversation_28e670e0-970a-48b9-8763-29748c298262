import api from "@/lib/axios";
import { CarePlanCardItem, ProductQueryParams, ServiceData } from "./type";

export const fetchProductssApi = async (params: ProductQueryParams) => {
  const { data } = await api.get("/api/v1/product", { params });
  return data;
};
export const fetchServicesApi = async (): Promise<ServiceData[]> => {
  return [
    {
      provider: {
        firstName: "Alice",
        specialty: "Psychology",
        experience: 10,
        serviceLocation: ["virtual"],
        rating: 4.8,
        id: "prov-001",
        profilePicture: [
          {
            url:
              "https://nurture-ye-s3.s3.us-east-1.amazonaws.com/public/nurture-images/1748078097495.png",
            key: "public/nurture-images/1748078097495.png",
          },
        ],
      },
      services: [
        {
          id: "srv-001",
          title: "Therapy Session",
          price: 100,
        },
        {
          id: "srv-002",
          title: "Initial Consultation",
          price: 50,
        },
        {
          id: "srv-003",
          title: "Initial Consultation",
          price: 50,
        },
        {
          id: "srv-004",
          title: "Initial Consultation",
          price: 50,
        },
      ],
    },
    {
      provider: {
        firstName: "Bob",
        specialty: "Nutrition",
        experience: 5,
        serviceLocation: ["virtual"],
        rating: 4.6,
        id: "prov-002",
        profilePicture: [
          {
            url:
              "https://nurture-ye-s3.s3.us-east-1.amazonaws.com/public/nurture-images/1748078097495.png",
            key: "public/nurture-images/1748078097495.png",
          },
        ],
      },
      services: [
        {
          id: "srv-003",
          title: "Meal Planning",
          price: 80,
        },
      ],
    },
    {
      provider: {
        firstName: "Charlie",
        specialty: "Fitness Coaching",
        experience: 8,
        serviceLocation: ["virtual"],
        rating: 4.9,
        id: "prov-003",
        profilePicture: [
          {
            url:
              "https://nurture-ye-s3.s3.us-east-1.amazonaws.com/public/nurture-images/1748078097495.png",
            key: "public/nurture-images/1748078097495.png",
          },
        ],
      },
      services: [
        {
          id: "srv-004",
          title: "Personal Training",
          price: 120,
        },
        {
          id: "srv-005",
          title: "Online Workout Plan",
          price: 40,
        },
      ],
    },
    {
      provider: {
        firstName: "Diana",
        specialty: "Life Coaching",
        experience: 12,
        serviceLocation: ["virtual"],
        rating: 4.7,
        id: "prov-004",
        profilePicture: [
          {
            url:
              "https://nurture-ye-s3.s3.us-east-1.amazonaws.com/public/nurture-images/1748078097495.png",
            key: "public/nurture-images/1748078097495.png",
          },
        ],
      },
      services: [
        {
          id: "srv-006",
          title: "1-on-1 Coaching",
          price: 90,
        },
      ],
    },
  ];

  const { data } = await api.get("/api/v1/service");
  return data?.data?.services;
};

export const fetchCarePlanApi = async (): Promise<CarePlanCardItem[]> => {
  const { data } = await api.get("/api/v1/users/customer/care-plan");
  return data?.data?.carePlan;
};
