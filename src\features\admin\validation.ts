import Joi from "joi";

export const LoginSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .lowercase()
    .trim()
    .messages({
      "any.required": "Email is a required field.",
      "string.base": "Email must be a string",
      "string.email": "Invalid email",
      "string.empty": "Email cannot be empty",
    }),

  password: Joi.string()
    .trim()
    .required()

    .messages({
      "string.base": "Password must be a string",
      "string.empty": "Password cannot be empty",
      "any.required": "Password is a required field",
      "string.pattern.base":
        "Password must be 8-32 characters with at least one uppercase, lowercase, and special character",
    }),
});