import { useState } from "react";
import ServiceCard from "../../provider-dashboard/components/ServiceCard";
import { Service, Provider } from "../../admin/type";
import { ServiceDetails } from "../../provider-dashboard/components/ServiceDetailsDialog";

interface ServiceTabsProps {
  services: Service[];
  provider: Provider;
}

type TabType = "services" | "reviews" | "photos";

function ServiceTabs({ services, provider }: ServiceTabsProps) {
  const [activeTab, setActiveTab] = useState<TabType>("services");

  // Transform Service to ServiceDetails format for the ServiceCard component
  const transformToServiceDetails = (service: Service): ServiceDetails & { id: string } => ({
    id: service._id, // Add the actual service ID
    serviceName: service.title,
    duration: `${service.duration} hour${service.duration > 1 ? 's' : ''}`,
    price: service.price.toString(),
    description: service.description,
    highlights: (service.highlights || []).map((text, index) => ({
      text,
      id: `${service._id}-highlight-${index}`
    }))
  });

  const tabs = [
    { id: "services" as TabType, label: "Services", count: services.length },
    { id: "reviews" as TabType, label: "Reviews & Ratings", count: 0 },
    { id: "photos" as TabType, label: "Photos", count: provider.photos?.length || 0 }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case "services":
        return (
          <div className="space-y-6">
            {services.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500">No services available</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {services.map((service) => (
                  <ServiceCard
                    key={service._id}
                    service={transformToServiceDetails(service)}
                  />
                ))}
              </div>
            )}
          </div>
        );

      case "reviews":
        return (
          <div className="space-y-6">
            <div className="text-center py-12">
              <p className="text-gray-500">Reviews & ratings coming soon</p>
              <p className="text-sm text-gray-400 mt-2">
                This feature will be available in a future update
              </p>
            </div>
          </div>
        );

      case "photos":
        return (
          <div className="space-y-6">
            {!provider.photos || provider.photos.length === 0 ? (
              <div className="text-center py-12">
                <p className="text-gray-500">No photos available</p>
              </div>
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {provider.photos.map((photo, index) => (
                  <div key={photo.key || index} className="aspect-square rounded-lg overflow-hidden">
                    <img
                      src={photo.url}
                      alt={`Photo ${index + 1}`}
                      className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                activeTab === tab.id
                  ? "border-orange-1 text-orange-1"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              {tab.label}
              {tab.count > 0 && (
                <span className="ml-2 bg-gray-100 text-gray-600 py-0.5 px-2 rounded-full text-xs">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        {renderTabContent()}
      </div>
    </div>
  );
}

export default ServiceTabs;
