import React from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Link, useLocation } from "react-router-dom";
import { useBreadcrumbStore } from "@/store/breadcrumStore";

function DynamicBreadcrumb() {
  const { getCrumbByHref } = useBreadcrumbStore();
  const location = useLocation();
  const segments = location.pathname.split("/").filter(Boolean);

  let currentLink = "";
  const navigationList = segments.map((item) => {
    currentLink += `/${item}`;
    const currentCrumb = getCrumbByHref(currentLink);
    if (!currentCrumb) return null;
    return {
      name: currentCrumb?.name || "home",
      href: currentCrumb?.href || "/",
    };
  });
  return (
    <Breadcrumb>
      <BreadcrumbList>
        {navigationList?.map(
          (item, index) =>
            item?.href && (
              <React.Fragment key={index}>
                <BreadcrumbItem>
                  <BreadcrumbLink asChild>
                    <Link to={item?.href} className="truncate max-w-36">
                      {item?.name}
                    </Link>
                  </BreadcrumbLink>
                </BreadcrumbItem>
                {index < navigationList.length - 1 && <BreadcrumbSeparator />}
              </React.Fragment>
            )
        )}
      </BreadcrumbList>
    </Breadcrumb>
  );
}

export default DynamicBreadcrumb;
