// import { <PERSON><PERSON> } from "@/components/ui/button";
// import { useNavigate } from "react-router-dom";
import FingerPrintImage from "@/assets/fingerprint.png";

export default function ProfileSetupStep5() {
  // const navigate = useNavigate();

  // const handleComplete = () => {
  //   // Navigate to the provider dashboard or home page
  //   navigate("/provider/dashboard");
  // };

  return (
    <div className="w-full flex flex-col items-center text-center">
      <img
        src={FingerPrintImage}
        alt="Fingerprint"
        className="mx-auto object-cover mb-6"
      />

      <h1 className="text-3xl font-prettywise font-bold mb-4">
        Your account is being verified.
      </h1>

      <p className="text-gray-600 mb-8 max-w-md">
        We're reviewing your information to ensure everything is in order.
        This typically takes 1-2 business days. You'll receive an email once your account is verified.
      </p>
{/* 
      <Button
        onClick={handleComplete}
        className="bg-orange-1 hover:bg-orange-1/90 text-white px-8 py-2 rounded-full"
      >
        Go to Dashboard
      </Button> */}
    </div>
  );
}
