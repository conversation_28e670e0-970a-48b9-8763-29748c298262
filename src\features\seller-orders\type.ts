export type OrderTableItems = {
  _id: string;
  productImage: string;
  productTitle: string;
  firstName: string;
  shippingAddress: string;
  status: string;
  quantity: number;
  amountPaid: number;
  createdAt: string;
};

export type OrderTableProps = {
  data: OrderTableItems[];
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;

  loading: boolean;
};

export type OrderStatusType =
  | "item shipped"
  | "item delivered"
  | "order confirmed"
  | "exchange requested"
  | "return requested"
  | "return approved"
  | "exchange approved";

export type Orderstatus =
  | "order confirmed"
  | "item shipped"
  | "item delivered"
  | "item cancelled"
  | "exchange requested"
  | "request approved"
  | "request rejected"
  | "return rejected"
  | "exchange rejected"
  | "shipment pending"
  | "delivery pending"
  | "return requested"
  | "exchange approved"
  | "return approved"
  | "item returned";
export type Image = {
  image: string;
  key: string;
};

export type OrderItems = {
  _id: string;
  product: {
    images: Image[];
    title: string;
  };
  status: string;
  quantity: number;
};

export type RequestedItems = {
  _id: string;
  reasons: string[];
  images: { image: string; key: string }[];
};
