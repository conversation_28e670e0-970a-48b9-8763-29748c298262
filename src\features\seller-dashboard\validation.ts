import Joi from "joi";

export const productCreationSchema = Joi.object({
  title: Joi.string().trim().required().min(5).max(70).messages({
    "any.required": "Title is a required field",
    "string.base": "Title must be a string",
    "string.min": "Title atleast need 5 characters",
    "string.max": "Title cannot exceed 70 characters",
    "string.empty": "Title cannot be empty",
  }),

  description: Joi.string().trim().required().min(10).max(3000).messages({
    "any.required": "Description is a required field",
    "string.base": "Description must be a string",
    "string.min": "Description atleast need 10 characters",
    "string.max": "Description cannot exceed 3000 characters",
    "string.empty": "Description cannot be empty",
  }),

  quantity: Joi.number().integer().required().min(1).max(5000).messages({
    "number.base": "Quantity must be a number",
    "any.required": "Quantity is a required field",
    "number.integer": "Quantity must be an integer value",
    "number.min": `"quantity" must be at least {#limit}`,
    "number.max": `"quantity" must be less than or equal to {#limit}`,
  }),

  price: Joi.number().integer().required().min(1).messages({
    "number.base": "price must be a number",
    "any.required": "price is a required field",
    "number.integar": "price must be a integar value",
    "number.min": `"price" must be at least {#limit}`,
  }),
  // images: Joi.array()
  //   .items(Joi.string().required().trim())
  //   .required()
  //   .min(1)
  //   .messages({
  //     "array.base": "Images values must be array",
  //     "array.min": "Atleast one image is required",
  //     "any.required": "Images is a required field",
  //     "string.base": "Each image must be string",
  //   }),
});
