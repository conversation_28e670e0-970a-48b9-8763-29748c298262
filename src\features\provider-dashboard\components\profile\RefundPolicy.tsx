import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";

interface RefundPolicyProps {
  policy: string;
  onSave?: (policy: string) => void;
  isLoading?: boolean;
}

function RefundPolicy({ policy, onSave, isLoading = false }: RefundPolicyProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [policyText, setPolicyText] = useState(policy);

  const handleSave = () => {
    if (onSave) {
      onSave(policyText);
    }
    setIsEditing(false);
  };

  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-base ">Refund policy</h2>
        {!isEditing && (
          <Button
            variant="outline"
            onClick={() => setIsEditing(true)}
            className="text-orange-1 border-orange-1"
            disabled={isLoading}
          >
            Edit
          </Button>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-4">
          <Textarea
            value={policyText}
            onChange={(e) => setPolicyText(e.target.value)}
            className="min-h-[150px]"
            placeholder="Enter your refund policy details here..."
          />
          <div className="flex gap-2 justify-end">
            <Button
              variant="outline"
              onClick={() => {
                setPolicyText(policy);
                setIsEditing(false);
              }}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={isLoading}
            >
              {isLoading ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>
      ) : (
        <div className="p-4 border border-gray-200 rounded-lg">
          <p className="text-neutral-300 whitespace-pre-wrap">{policyText || "No refund policy set."}</p>
        </div>
      )}
    </div>
  );
}

export default RefundPolicy;
