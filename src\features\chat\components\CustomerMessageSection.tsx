import { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { Input } from "@/components/ui/input";
import InfiniteScroll from "react-infinite-scroll-component";
import { useMutation, useQuery } from "@tanstack/react-query";

import { dateFormatter, timeFormatter } from "@/lib/utils";
import GoBackIcon from "@/assets/go-back.svg";
import SendIcon from "@/assets/send.svg";

import Buttonwithicon from "@/components/ui/buttonwithicon";
import EmptyConversation from "./EmptyConversation";
import {
  fetchConversationMessagesApi,
  fetchConversationsApi,
  sendMessageApi,
} from "../api";
import { Message } from "../type";
import { useAuthStore } from "@/store/authStore";

function CustomerMessageSection({
  name,
  description,
  image,
  handleHideMessageSection,
  receiverId,
  conversationId,
}: {
  name: string;
  description: string;
  image?: string | undefined;
  receiverId: string;
  conversationId?: string;
  handleHideMessageSection: () => void;
}) {
  const currentUser = useAuthStore((state) => state.user);
  const socket = currentUser?.socket?.chat;
  const user = localStorage.getItem("nurtureUser") as string;
  const [message, setMessage] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [allMessages, setAllMessages] = useState<Message[]>([]);
  const prevReceiverIdRef = useRef<string>(receiverId);
  const scrollableRef = useRef<HTMLDivElement>(null);
  const roomIdRef = useRef<string | null>(null);

  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const { data: conversationsWithConversationId } = useQuery({
    queryKey: ["conversation-messages", conversationId, currentPage],
    queryFn: () =>
      fetchConversationMessagesApi(conversationId as string, currentPage),
    enabled: !!conversationId,
  });

  const { data: conversationsWithReceiverId } = useQuery({
    queryKey: ["conversation-messages", receiverId, currentPage],
    queryFn: () => fetchConversationsApi(receiverId as string, currentPage),
    enabled: !conversationId && !!receiverId,
  });
  useEffect(() => {
    if (!socket || !receiverId) return;
    socket.emit("join:room", receiverId, (roomId: string) => {
      roomIdRef.current = roomId;
    });
  }, [receiverId]);

  useEffect(() => {
    if (!socket) return;
    const handleMessages = (
      data: {
        message: Message;
        roomId: string;
        senderId: string;
        receiverId: string;
      },
      ack: (isLiveChat: boolean) => void
    ) => {
      if (data.roomId === roomIdRef.current) {
        // Ensure the message is for the current receiver
        setAllMessages((prev) => [...prev, data?.message]);
      }
      ack(receiverId === data.senderId); // acknowledge the message as live chat if the receiverId matches the senderId
      if (user === data.message.sentBy) {
        // If the curret user is the sender, we dont need to emit seen message
        return;
      }

      if (receiverId === data.senderId) {
        socket.emit("message:seen", {
          chatId: data.message._id,
          conversationId: data.message.conversationId,
        });
      }
    };
    socket.on("chat", handleMessages);

    return () => {
      socket.off("chat", handleMessages);
    };
  }, [socket, receiverId]);

  //reset when receiverId changes
  useEffect(() => {
    if (prevReceiverIdRef.current === receiverId) return;
    if (scrollableRef.current) {
      (scrollableRef.current as HTMLElement)?.scrollIntoView({
        behavior: "smooth",
      });
    }
    setCurrentPage(1);
    setAllMessages([]);
    prevReceiverIdRef.current = receiverId;
  }, [receiverId]);

  const conversations = useMemo(() => {
    if (conversationsWithConversationId) {
      return conversationsWithConversationId;
    }
    if (conversationsWithReceiverId) {
      return conversationsWithReceiverId;
    }
    return null;
  }, [conversationsWithConversationId, conversationsWithReceiverId]);

  useEffect(() => {
    if (!conversations) return;
    const reversedChat = [...conversations.chats].reverse();
    if (currentPage === 1) {
      setAllMessages(reversedChat);
    } else {
      setAllMessages((prev) => [...reversedChat, ...prev]);
    }
  }, [conversations, currentPage]);

  // send message
  const { mutate, isPending: sendMessageLoading } = useMutation({
    mutationFn: sendMessageApi,
  });
  const handleSendMessage = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!message.trim() || sendMessageLoading) return;
    let mutationPayload = { receiverId, content: message };
    if (conversationId) {
      mutationPayload = {
        ...mutationPayload,
        ...{ conversationId: conversationId },
      };
    }
    mutate(mutationPayload);
    setMessage("");
  };

  if (!receiverId) {
    return (
      <EmptyConversation handleHideMessageSection={handleHideMessageSection} />
    );
  }

  return (
    <div className="flex flex-col w-full">
      <div className="flex p-4 border-b gap-x-3 md:gap-0 border-nueutral-40">
        <img
          onClick={handleHideMessageSection}
          src={GoBackIcon}
          alt="go back"
          className="w-4 h-4 my-auto cursor-pointer md:hidden"
        />
        <div className={`${image ? "flex gap-x-2" : ""} `}>
          {image && (
            <div className="flex-shrink-0 w-12 h-12 overflow-hidden rounded-full">
              <img
                src={image}
                alt={"profile"}
                className="object-cover w-full h-full"
              />
            </div>
          )}
          <div>
            <h1>{name}</h1>
            <h5 className="text-sm text-neutral-300">{description}</h5>
          </div>
        </div>
      </div>

      <InfiniteScroll
        style={{ display: "flex", flexDirection: "column-reverse" }}
        inverse={true}
        dataLength={allMessages.length}
        next={() => handlePageChange(currentPage + 1)}
        hasMore={((conversations?.totalPages as number) || 0) > currentPage}
        loader={
          <div className="flex justify-center py-4">
            <div className="flex items-center gap-2 text-sm text-neutral-500">
              <div className="w-4 h-4 border-2 rounded-full border-coral border-t-transparent animate-spin"></div>
              Loading older messages...
            </div>
          </div>
        }
        endMessage={
          currentPage === conversations?.totalPages && (
            <div className="flex justify-center py-4">
              <div className="text-sm text-neutral-400">
                No more messages to load
              </div>
            </div>
          )
        }
        height={"65vh"}
        className="scrollbar-hide"
      >
        <div className="flex flex-col w-full p-5">
          {allMessages.map((conversation, index) => {
            return (
              <div
                key={conversation?.createdAt + index}
                className="flex flex-col"
              >
                <div className="flex justify-center">
                  <p className="text-xs text-neutral-400">
                    {index % 6 === 0 && dateFormatter(conversation?.createdAt)}
                  </p>
                </div>
                <div
                  key={conversation?.createdAt + index}
                  ref={index === allMessages.length - 1 ? scrollableRef : null}
                  className={`my-3 p-3 max-w-[80%] md:max-w-[49%] border ${
                    conversation?.sentBy == user
                      ? "bg-coral text-white self-end rounded-tr-none rounded-xl"
                      : "self-start rounded-tl-none rounded-xl border-neutral-50"
                  }`}
                >
                  <p className="break-words">{conversation?.content}</p>
                  <p
                    className={`${conversation?.sentBy == user ? "text-tints-60" : "text-neutral-300"} text-sm text-end mt-1`}
                  >
                    {timeFormatter(conversation?.createdAt)}
                  </p>
                </div>
              </div>
            );
          })}
        </div>
      </InfiniteScroll>
      {/* Input */}
      <form
        onSubmit={handleSendMessage}
        className="flex p-4 border-t gap-x-2 border-neutral-40"
      >
        <Input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type message here..."
          className="w-full p-2 border rounded-md grow border-neutral-40"
        />
        <Buttonwithicon
          disabled={message.trim().length === 0}
          icon={SendIcon}
          variant="button"
        />
      </form>
    </div>
  );
}

export default CustomerMessageSection;
