import { Skeleton } from "@/components/ui/skeleton";

function ScheduledCallsCardSkelton() {
  return (
    <div className="container p-4 border border-gray-200 rounded-lg">
      <div className="flex gap-x-3">
        <Skeleton className="w-16 h-16 rounded-full md:w-20 md:h-20" />
        <div className="flex-1 space-y-2">
          <div className="flex items-center gap-x-2">
            <Skeleton className="w-24 h-4 md:w-32" />
            <div className="flex items-center gap-x-1">
              <Skeleton className="w-5 h-5" />
              <Skeleton className="w-8 h-4" />
            </div>
          </div>
          <div className="flex flex-col gap-2 mt-1 md:flex-row">
            <Skeleton className="h-4 w-36" />
            <Skeleton className="w-32 h-4" />
          </div>
          <div className="flex mt-3 gap-x-4">
            <div className="flex items-center gap-x-1">
              <Skeleton className="w-4 h-4" />
              <Skeleton className="w-24 h-4" />
            </div>
            <div className="flex items-center gap-x-1">
              <Skeleton className="w-4 h-4" />
              <Skeleton className="w-20 h-4" />
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-col justify-between gap-2 mt-3 md:flex-row md:items-center">
        <Skeleton className="w-40 h-6" />
        <Skeleton className="self-end w-16 h-4 md:self-auto" />
      </div>
    </div>
  );
}

export default ScheduledCallsCardSkelton;
