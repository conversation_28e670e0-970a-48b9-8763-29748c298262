import { But<PERSON> } from "@/components/ui/button";
import { Lu<PERSON>he<PERSON>, LuX } from "react-icons/lu";

interface BookingItemProps {
  name: string;
  date: string;
  time: string;
  onAccept?: () => void;
  onReject?: () => void;
  status?: "pending" | "upcoming" | "completed";
}

function BookingItem({ name, date, time, onAccept, onReject, status = "pending" }: BookingItemProps) {
  return (
    <div className="border rounded-md mb-2 p-4">
      {/* Mobile Layout */}
      <div className="md:hidden">
        <div className="mb-3">
          <p className="font-medium text-base mb-1">{name}</p>
          <p className="text-gray-600 text-sm">{date}, {time}</p>
        </div>

        {status === "pending" && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="flex-1 flex items-center justify-center gap-1 rounded-full border-gray-300 hover:bg-gray-50 h-10"
              onClick={onReject}
            >
              <LuX className="w-4 h-4" />
              <span>Reject</span>
            </Button>
            <Button
              className="flex-1 flex items-center justify-center gap-1 rounded-full bg-orange-1 hover:bg-orange-1/90 h-10"
              onClick={onAccept}
            >
              <LuCheck className="w-4 h-4" />
              <span>Accept</span>
            </Button>
          </div>
        )}
      </div>

      {/* Desktop Layout */}
      <div className="hidden md:flex items-center justify-between min-h-[54px]">
        <div>
          <p className="font-medium">{name}</p>
        </div>
        <div className="text-gray-600">
          {date}
        </div>
        <div className="text-gray-600">
          {time}
        </div>

        {status === "pending" && (
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="flex items-center gap-1 rounded-full border-gray-300 hover:bg-gray-50"
              onClick={onReject}
            >
              <LuX className="w-4 h-4" />
              <span>Reject</span>
            </Button>
            <Button
              size="sm"
              className="flex items-center gap-1 rounded-full bg-orange-1 hover:bg-orange-1/90"
              onClick={onAccept}
            >
              <LuCheck className="w-4 h-4" />
              <span>Accept</span>
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}

export default BookingItem;
