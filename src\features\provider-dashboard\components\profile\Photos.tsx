import { But<PERSON> } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import PhotoUploadDialog from "./PhotoUploadDialog";

interface Photo {
  url: string;
  key: string;
}

interface PhotosProps {
  photos: Photo[];
  onPhotosUpload?: (files: File[]) => void;
  onPhotoDelete?: (key: string) => void;
  isLoading?: boolean;
}

function Photos({
  photos = [],
  onPhotosUpload,
  onPhotoDelete,
  isLoading = false
}: PhotosProps) {
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);

  const handlePhotosUpload = (files: File[]) => {
    if (onPhotosUpload) {
      onPhotosUpload(files);
    }
    // Close dialog after submission - the dialog will reset its state
    setIsUploadDialogOpen(false);
  };

  const handlePhotoDelete = (key: string) => {
    if (onPhotoDelete) {
      onPhotoDelete(key);
    }
  };

  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-base">Photos</h2>
        <Button
          variant="outline"
          onClick={() => setIsUploadDialogOpen(true)}
          className="flex items-center gap-2 text-orange-1 border-orange-1"
          disabled={isLoading}
        >
          Add <Plus className="w-4 h-4" />
        </Button>
      </div>

      {photos.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
          {photos.map((photo, index) => (
            <div
              key={`${photo.key}-${index}`}
              className="relative aspect-square rounded-lg overflow-hidden group"
            >
              <img
                src={photo.url}
                alt="Provider photo"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handlePhotoDelete(photo.key)}
                  className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-white hover:text-red-300 bg-black bg-opacity-50 hover:bg-opacity-70"
                  disabled={isLoading}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-8 max-w-2xl aspect-video">
          <p className="text-gray-500">No photos uploaded yet.</p>
        </div>
      )}

      {/* Photo Upload Dialog */}
      <PhotoUploadDialog
        open={isUploadDialogOpen}
        onOpenChange={setIsUploadDialogOpen}
        onSubmit={handlePhotosUpload}
        isLoading={isLoading}
      />
    </div>
  );
}

export default Photos;
