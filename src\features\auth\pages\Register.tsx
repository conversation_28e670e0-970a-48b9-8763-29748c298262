import { RegisterForm } from "../components/RegisterForm";
import AuthLayout from "../components/AuthLayout";
import { Navigate } from "react-router-dom";
import { checkAuthentication } from "@/lib/utils";
function Register() {
  const { isLoggedIn, href } = checkAuthentication();

  return isLoggedIn ? (
    <Navigate to={href} />
  ) :(
    <AuthLayout>
      <RegisterForm />
    </AuthLayout>
  );
}

export default Register;
