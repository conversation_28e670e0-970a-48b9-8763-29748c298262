import AuthLayout from "../components/AuthLayout";
import ResetPasswordForm from "../components/ResetPasswordForm";
import { Navigate, useNavigate, useSearchParams } from "react-router-dom";
import { useMutation } from "@tanstack/react-query";
import { validateResetPasswordTokenApi } from "../api";
import { useEffect } from "react";
import { checkAuthentication } from "@/lib/utils";
function ResetPassoword() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const token = searchParams.get("token");

  const { mutate } = useMutation({
    mutationFn: validateResetPasswordTokenApi,
    onSuccess: () => {},
    onError: () => {
      navigate("/login");
    },
  });
  useEffect(() => {
    mutate(token as string);
  }, []);
  const { isLoggedIn, href } = checkAuthentication();

  return isLoggedIn ? (
    <Navigate to={href} />
  ) : (
    <AuthLayout>
      <ResetPasswordForm />
    </AuthLayout>
  );
}

export default ResetPassoword;
