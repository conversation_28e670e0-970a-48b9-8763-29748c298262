import { Skeleton } from "@/components/ui/skeleton";

function ReviewCardSkelton() {
  return (
    <div className="flex flex-col p-4 my-3 border border-gray-200 rounded-lg gap-y-3">
      <div className="flex gap-x-4">
        <div className="flex-shrink-0 w-16 h-16 overflow-hidden rounded-md">
          <Skeleton className="w-full h-full rounded-md" />
        </div>
        <div className="flex flex-col justify-between flex-1 space-y-2">
          <div className="flex items-center gap-x-3">
            <Skeleton className="w-24 h-5" /> {/* Name */}
            <div className="flex items-center gap-x-1">
              <Skeleton className="w-5 h-5 rounded-full" /> {/* Rating icon */}
              <Skeleton className="w-10 h-4" /> {/* Rating text */}
            </div>
          </div>
          <Skeleton className="w-full h-4" /> {/* Content line 1 */}
          <Skeleton className="w-3/4 h-4" /> {/* Content line 2 */}
        </div>
      </div>
    </div>
  );
}

export default ReviewCardSkelton;
