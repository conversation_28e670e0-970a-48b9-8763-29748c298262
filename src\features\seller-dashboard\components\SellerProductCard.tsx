import { Link } from "react-router-dom";
import { SellerProductCardProps } from "../type";
import NextIcon from "@/assets/next.svg";
function SellerProductCard({
  product,
  showDescription = false,
}: SellerProductCardProps) {
  if (!product) return null;
  return (
    <div className="border rounded-lg border-gray-2">
      <div className="flex items-center justify-between p-4">
        <img
          className="object-cover max-w-[50px] max-h-[50px]"
          src={product?.images[0]?.image}
          alt={product.title}
        />
        <h1 className="mx-2 font-semibold ">{product.title}</h1>
        <Link to={`/seller/products/${product._id}`}>
          <img src={NextIcon} alt="go to next page" />
        </Link>
      </div>
      {/* content */}
      <div className="p-4 border-t border-gray-2">
        <div className="flex justify-between w-3/4 ">
          <div className="flex flex-col justify-between gap-y-2">
            <h4 className="text-neutral-300">Stock availability</h4>
            <span>{product.quantity}</span>
          </div>
          <div className="flex flex-col justify-between">
            <h4 className="text-neutral-300">Price</h4>
            <span>{product.price}</span>
          </div>
        </div>
      </div>
      {/* description */}
      {showDescription && (
        <div className="flex flex-col p-4 border-t gap-y-3 border-gray-2">
          <h4 className="text-neutral-300">Description</h4>
          <p className="text-neutral-600 line-clamp-3">{product.description}</p>
        </div>
      )}
    </div>
  );
}

export default SellerProductCard;
