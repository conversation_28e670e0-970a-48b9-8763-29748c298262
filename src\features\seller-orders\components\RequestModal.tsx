import Buttonwithicon from "@/components/ui/buttonwithicon";
import { Modal } from "@/components/ui/modal";
import { useParams } from "react-router-dom";
import ApproveIcon from "@/assets/approve.svg";
import RejectIcon from "@/assets/reject.svg";
import { showToast } from "@/lib/toast";
import { approveOrRejectRequestApi, fetchRequestDetailsApi } from "../api";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Image } from "../type";
import { Skeleton } from "@/components/ui/skeleton";

function RequestModal({
  isOpen,
  onClose,
  currentOrderStatus,
  onOpenEmail,
}: {
  isOpen: boolean;
  onClose: () => void;
  onOpenEmail: () => void;
  currentOrderStatus: string;
}) {
  const { id } = useParams();
  const type =
    currentOrderStatus === "return requested" ? "return" : "exchange";

  const requestData = useQuery({
    queryKey: ["request-data", id],
    queryFn: () => fetchRequestDetailsApi(id as string),
  });
  console.log(requestData.data, "requestData");
  const { mutate, isPending: loading } = useMutation({
    mutationFn: approveOrRejectRequestApi,
    onSuccess: () => {
      showToast("Request approved", "success");
      onClose();
      onOpenEmail();
    },
    onError: () => {
      showToast("Failed to approve request", "error");
    },
  });
  const handleSubmit = (status: string) => {
    mutate({ orderId: id as string, status, type });
  };
  return (
    <Modal isOpen={isOpen} onClose={onClose} className="lg:min-w-[500px]">
      <div className="">
        <div className="container max-h-[480px] scrollbar-hide overflow-y-scroll">
          <header className="flex flex-col gap-y-1">
            <h1 className="text-lg font-semibold">Return requested</h1>
            <h5 className="text-neutral-300">
              Please review the return request for order {id}
            </h5>
          </header>
          <div className="mt-8">
            <h1 className="font-semibold ">Reasons</h1>
            <ol className="list-decimal list-inside">
              {requestData.isPending
                ? [1, 2, 3].map((_, index) => (
                    <li key={index} className="my-2">
                      <Skeleton className="w-3/4 h-4" />
                    </li>
                  ))
                : requestData?.data?.data?.item?.reasons?.map(
                    (reason: string) => (
                      <li key={reason} className="my-2 text-neutral-300">
                        {reason}
                      </li>
                    )
                  )}
            </ol>
          </div>
          <div className="mt-8">
            <h1 className="font-semibold ">Images</h1>
            <div className="flex mt-4 gap-x-3">
              {requestData.isPending
                ? Array.from({ length: 4 }).map((_, index) => (
                    <Skeleton key={index} className="w-20 h-20 rounded-md" />
                  ))
                : requestData?.data?.data?.item?.images?.map((image: Image) => (
                    <img
                      key={image.key}
                      src={image.image}
                      alt="product"
                      className="object-cover w-20 h-20"
                    />
                  ))}
            </div>
          </div>
        </div>
        <div className="flex justify-between mx-2 mt-8 gap-x-2">
          <Buttonwithicon
            onClick={() => handleSubmit("reject")}
            disabled={loading}
            variant="white-button"
            text={loading ? "Rejecting..." : "Reject"}
            classname="text-center md:w-full"
            icon={RejectIcon}
          />
          <Buttonwithicon
            onClick={() => handleSubmit("approve")}
            disabled={loading}
            variant="button"
            text={loading ? "Approving..." : "Approve"}
            classname="text-center md:w-full"
            icon={ApproveIcon}
          />
        </div>
      </div>
    </Modal>
  );
}

export default RequestModal;
