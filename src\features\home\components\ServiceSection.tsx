import ServiceBanner from "@/assets/service-banner.png";
import ServiceBannerMobile from "@/assets/service-banner-mobile.png";
import Buttonwithicon from "@/components/ui/buttonwithicon";
import ServiceCard from "./ServiceCard";
import { fetchServicesApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import ServiceCardSkelton from "@/features/admin/components/ServiceCardSkelton";

function ServiceSection() {
  // Sample service provider data - replace with actual data from API or CMS

  const { data, isPending: loading } = useQuery({
    queryKey: ["services-home"],
    queryFn: fetchServicesApi,
  });

  return (
    <section className="rounded-lg bg-tints-40">
      <div className="flex flex-col md:flex-row gap-x-3">
        <div className="h-60 md:h-auto md:w-[45%] ">
          <img
            src={ServiceBanner}
            alt="service"
            className="hidden object-cover w-full h-full md:block"
          />
          <img
            src={ServiceBannerMobile}
            alt="service"
            className="object-cover w-full h-full md:hidden"
          />
        </div>
        {/* Header section */}
        <div className="p-4 md:p-8 lg:p-16 md:w-2/3 lg:w-3/4">
          <div className="mb-6 md:mb-8">
            <div className="mb-2 text-xs font-medium tracking-wider uppercase text-orange-1 md:text-base font-prettywise">
              RECOMMENDED SERVICES
            </div>

            <div className="flex flex-col md:gap-y-5">
              <h2 className="max-w-xl mb-4 text-xl font-medium md:text-2xl lg:text-4xl md:mb-0">
                We know your needs during <br /> different post partum stages
              </h2>

              <div className="self-start md:self-auto">
                <Buttonwithicon
                  variant="default"
                  text="
                See all"
                  href="/services"
                />
              </div>
            </div>
          </div>

          {/* Service Providers - stacked on mobile, grid on larger screens */}
          <div className="pb-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {loading ? (
                [1, 2, 3, 4].map((_, i) => (
                  <ServiceCardSkelton key={i} />
                ))
              ) : (
                data?.map((service) => {
                  return (
                    <div
                      key={service.provider.id}
                      className="h-full p-5 bg-white border rounded-lg shadow-sm border-neutral-40"
                    >
                      <ServiceCard
                        id={service.provider.id}
                        image={service.provider.profilePicture[0]?.url || ""}
                        name={service.provider.firstName}
                        specialty={service.provider.specialty}
                        rating={service.provider.rating}
                        experience={service.provider.experience}
                        services={{ services: service.services }}
                      />
                    </div>
                  );
                })
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default ServiceSection;
