import { Modal } from "@/components/ui/modal";
import CloseIcon from "@/assets/close-icon.png";
import { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { productCreationSchema } from "../validation";
import { joiResolver } from "@hookform/resolvers/joi";
import { ProductCreationPayload } from "../type";
import { useForm, Controller } from "react-hook-form";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { showToast } from "@/lib/toast";
import { sellerAddProductApi } from "../api";

function AddProductModal({
  isOpen,
  onClose,
  setRefetch,
}: {
  isOpen: boolean;
  onClose: () => void;
  setRefetch: React.Dispatch<React.SetStateAction<boolean>>;
}) {
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [imageError, setImageError] = useState<string>("");

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
  } = useForm<ProductCreationPayload>({
    resolver: joiResolver(productCreationSchema),
    defaultValues: {
      title: "",
      description: "",
      price: undefined,
      quantity: undefined,
    },
  });
  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      reset();
      setSelectedImages([]);
      setImagePreviewUrls([]);
      setImageError("");
    }
  }, [isOpen, reset]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      if (selectedImages.length >= 5) {
        setImageError("You can only upload up to 5 images");
        return;
      }
      const newFile = e.target.files[0];

      // Validate file type
      const validTypes = ["image/jpeg", "image/png", "image/jpg", "image/webp"];
      if (!validTypes.includes(newFile.type)) {
        setImageError("Please upload a valid image file (JPEG, PNG, WebP)");
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (newFile.size > maxSize) {
        setImageError("Image size should be less than 5MB");
        return;
      }

      setImageError("");
      setSelectedImages([...selectedImages, newFile]);

      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreviewUrls([...imagePreviewUrls, reader.result as string]);
      };
      reader.readAsDataURL(newFile);
    }
  };

  const removeImage = (index: number) => {
    const updatedImages = [...selectedImages];
    const updatedPreviews = [...imagePreviewUrls];
    updatedImages.splice(index, 1);
    updatedPreviews.splice(index, 1);
    setSelectedImages(updatedImages);
    setImagePreviewUrls(updatedPreviews);
  };

  const { mutate, isPending: loading } = useMutation({
    mutationFn: sellerAddProductApi,
    onSuccess: () => {
      showToast("Product added successfully!", "success");
      onClose();
      setRefetch((prev) => !prev);
    },
    onError: (error: AxiosError) => {
      console.error(error.response?.data);
      showToast("Failed to add product. Please try again.", "error");
    },
  });

  const onSubmit = (data: ProductCreationPayload) => {
    // Validate that at least one image is selected
    if (selectedImages.length === 0) {
      setImageError("Please upload at least one product image");
      return;
    }

    // Create FormData for API call
    const formData = new FormData();
    formData.append("title", data.title);
    formData.append("description", data.description);
    formData.append("price", data.price.toString());
    formData.append("quantity", data.quantity.toString());
    formData.append("sellerId", localStorage.getItem("sellerId") as string);

    // Append all images
    selectedImages.forEach((image) => {
      formData.append(`images`, image);
    });
    mutate(formData);
  };

  return (
    <Modal position="center" isOpen={isOpen} onClose={onClose}>
      <div className="w-full">
        <div className="flex justify-end">
          <img
            onClick={onClose}
            className="cursor-pointer"
            src={CloseIcon}
            alt="close"
          />
        </div>

        <h1 className="mb-4 text-xl font-medium">Add product</h1>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-4">
            <p className="mb-2 text-gray-600">Product images</p>
            <div className="flex gap-2 overflow-x-auto">
              {/* Image upload button */}
              <label htmlFor="image-upload" className="cursor-pointer">
                <div className="w-[80px] h-[80px] border border-dashed border-red-200 rounded flex items-center justify-center bg-red-50">
                  <span className="text-2xl text-red-400">+</span>
                </div>
                <input
                  id="image-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="hidden"
                />
              </label>

              {/* Image previews */}
              {imagePreviewUrls.map((url, index) => (
                <div
                  key={index}
                  className="relative w-[80px] h-[80px] border rounded"
                >
                  <img
                    src={url}
                    alt={`Product ${index}`}
                    className="object-cover w-full h-full"
                  />
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="absolute flex items-center justify-center w-5 h-5 bg-white rounded-full shadow-sm -top-2 -right-2"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
            {imageError && (
              <p className="mt-1 text-sm text-red-500">{imageError}</p>
            )}
          </div>

          <div className="space-y-4">
            <div>
              <Controller
                name="title"
                control={control}
                render={({ field }) => (
                  <Input placeholder="Product name" {...field} />
                )}
              />
              {errors.title && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.title.message}
                </p>
              )}
            </div>

            <div>
              <div className="relative">
                <Controller
                  name="price"
                  control={control}
                  render={({ field }) => (
                    <Input
                      type="number"
                      placeholder="Pricing"
                      {...field}
                      // onChange={(e) => field.onChange(e.target.value)}
                    />
                  )}
                />
                <span className="absolute text-gray-500 transform -translate-y-1/2 right-3 top-1/2">
                  $
                </span>
              </div>
              {errors.price && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.price.message}
                </p>
              )}
            </div>

            <div>
              <div className="relative">
                <Controller
                  name="quantity"
                  control={control}
                  render={({ field }) => (
                    <Input
                      type="number"
                      inputMode="numeric"
                      placeholder="Stock availability"
                      {...field}
                      onChange={(e) => field.onChange(Number(e.target.value))}
                    />
                  )}
                />
                <span className="absolute text-gray-500 transform -translate-y-1/2 right-3 top-1/2">
                  no.s
                </span>
              </div>
              {errors.quantity && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.quantity.message}
                </p>
              )}
            </div>

            <div>
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <Textarea placeholder="Description" rows={5} {...field} />
                )}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.description.message}
                </p>
              )}
            </div>

            <Button type="submit" className="w-full" disabled={loading}>
              Submit
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}

export default AddProductModal;
