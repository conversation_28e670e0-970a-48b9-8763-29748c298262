import { useState } from "react";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import Jo<PERSON> from "joi";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { X, Upload } from "lucide-react";
import { showToast } from "@/lib/toast";

interface VideoUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (file: File) => void;
  isLoading?: boolean;
  isReplacing?: boolean;
}

interface VideoFormData {
  video: File;
}

// Validation schema for video upload
const videoUploadSchema = Joi.object({
  video: Joi.any().required().messages({
    "any.required": "Video file is required",
  }),
});

// Allowed video file types and size limit
const allowedVideoTypes = ["video/mp4", "video/avi", "video/mov", "video/wmv", "video/webm"];
const maxVideoSize = 50 * 1024 * 1024; // 50MB

export default function VideoUploadDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
  isReplacing = false,
}: VideoUploadDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const {
    handleSubmit,
    setValue,
    trigger,
    formState: { errors },
  } = useForm<VideoFormData>({
    resolver: joiResolver(videoUploadSchema),
  });

  // Handle file selection
  const handleFileSelect = (file: File) => {
    // Validate file type
    if (!allowedVideoTypes.includes(file.type)) {
      showToast("Please select a valid video file (MP4, AVI, MOV, WMV, WebM)", "error");
      return;
    }

    // Validate file size
    if (file.size > maxVideoSize) {
      showToast("Video file size must be less than 50MB", "error");
      return;
    }

    setSelectedFile(file);
    setValue("video", file);
    trigger("video");
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFileSelect(e.target.files[0]);
    }
  };

  // Handle drag and drop
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileSelect(e.dataTransfer.files[0]);
    }
  };

  // Handle form submission
  const handleFormSubmit = (data: VideoFormData) => {
    onSubmit(data.video);
  };

  // Reset form when dialog closes
  const handleDialogClose = () => {
    setSelectedFile(null);
    setValue("video", undefined as any);
    onOpenChange(false);
  };

  // Reset form when dialog opens
  const handleDialogOpen = (open: boolean) => {
    if (open) {
      // Reset state when opening
      setSelectedFile(null);
      setValue("video", undefined as any);
    }
    onOpenChange(open);
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogOpen}>
      <DialogContent className="max-w-md mx-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-lg font-semibold">
            {isReplacing ? "Replace Introductory Video" : "Upload Introductory Video"}
          </DialogTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDialogClose}
            className="h-6 w-6"
            disabled={isLoading}
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              dragActive
                ? "border-orange-500 bg-orange-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            {selectedFile ? (
              <div className="space-y-2">
                <div className="flex items-center justify-center w-12 h-12 mx-auto bg-green-100 rounded-full">
                  <Upload className="w-6 h-6 text-green-600" />
                </div>
                <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                <p className="text-xs text-gray-500">{formatFileSize(selectedFile.size)}</p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById('video-upload')?.click()}
                  disabled={isLoading}
                >
                  Choose Different File
                </Button>
              </div>
            ) : (
              <div className="space-y-2">
                <div className="flex items-center justify-center w-12 h-12 mx-auto bg-gray-100 rounded-full">
                  <Upload className="w-6 h-6 text-gray-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">
                    Drag and drop your video here, or{" "}
                    <button
                      type="button"
                      className="text-orange-600 hover:text-orange-700 font-medium"
                      onClick={() => document.getElementById('video-upload')?.click()}
                      disabled={isLoading}
                    >
                      browse
                    </button>
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    MP4, AVI, MOV, WMV, WebM up to 50MB
                  </p>
                </div>
              </div>
            )}

            <input
              id="video-upload"
              type="file"
              accept={allowedVideoTypes.join(',')}
              className="hidden"
              onChange={handleFileInputChange}
              disabled={isLoading}
            />
          </div>

          {errors.video && (
            <p className="text-sm text-red-500">{errors.video.message}</p>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleDialogClose}
              className="flex-1"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1"
              disabled={!selectedFile || isLoading}
            >
              {isLoading
                ? (isReplacing ? "Replacing..." : "Uploading...")
                : (isReplacing ? "Replace Video" : "Upload Video")
              }
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
