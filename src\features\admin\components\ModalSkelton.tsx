import { Skeleton } from "@/components/ui/skeleton";

function ModalSkelton() {
  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-4">
        <Skeleton className="w-16 h-16 rounded-full" />
        <div className="space-y-2">
          <Skeleton className="w-48 h-6" />
          <Skeleton className="w-64 h-4" />
        </div>
      </div>
      {[...Array(5)].map((_, i) => (
        <div key={i} className="flex justify-between">
          <Skeleton className="w-24 h-4" />
          <Skeleton className="w-32 h-4" />
        </div>
      ))}
    </div>
  );
}

export default ModalSkelton;
