import { Button } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";
import { useState } from "react";
import ServiceHighlightDialog from "./ServiceHighlightDialog";

interface ServiceHighlightsProps {
  highlights: string[];
  onHighlightsAdd?: (highlights: string[]) => void;
  onHighlightDelete?: (index: number) => void;
  isLoading?: boolean;
}

function ServiceHighlights({
  highlights = [],
  onHighlightsAdd,
  onHighlightDelete,
  isLoading = false
}: ServiceHighlightsProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleHighlightsAdd = (newHighlights: string[]) => {
    if (onHighlightsAdd) {
      onHighlightsAdd(newHighlights);
    }
    setIsDialogOpen(false);
  };

  const handleHighlightDelete = (index: number) => {
    if (onHighlightDelete) {
      onHighlightDelete(index);
    }
  };

  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-base">Service highlights</h2>
        <Button
          variant="outline"
          onClick={() => setIsDialogOpen(true)}
          className="flex items-center gap-2 text-orange-1 border-orange-1"
          disabled={isLoading}
        >
          Add <Plus className="w-4 h-4" />
        </Button>
      </div>

      {highlights.length > 0 ? (
        <div className="space-y-3">
          {highlights.map((highlight, index) => (
            <div
              key={index}
              className="flex justify-between items-center p-4 border border-gray-200 rounded-lg"
            >
              <span className="text-base text-gray-700">{highlight}</span>
              <Button
                variant="ghost"
                size="sm"
                className="text-gray-500 hover:text-red-500"
                onClick={() => handleHighlightDelete(index)}
                disabled={isLoading}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      ) : (
        <p className="text-gray-500">No service highlights added yet.</p>
      )}

      {/* Service Highlight Dialog */}
      <ServiceHighlightDialog
        open={isDialogOpen}
        onOpenChange={setIsDialogOpen}
        onSubmit={handleHighlightsAdd}
        isLoading={isLoading}
      />
    </div>
  );
}

export default ServiceHighlights;
