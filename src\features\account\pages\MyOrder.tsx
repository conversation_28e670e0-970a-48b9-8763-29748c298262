import { useBreadcrumbStore } from "@/store/breadcrumStore";
import { useEffect } from "react";
import { Link } from "react-router-dom";
import AccountLayoutWrapper from "../components/AccountLayoutWrapper";
import { useQuery } from "@tanstack/react-query";
import { fetchOrdersApi } from "../api";
import OrderCardSkelton from "../components/OrderCardSkelton";
import OrderCard from "../components/OrderCard";
import { OrderItems } from "../type";

function MyOrder() {
  const { setCrumbs } = useBreadcrumbStore();

  const { data, isPending: loading } = useQuery({
    queryKey: ["orders"],
    queryFn: fetchOrdersApi,
  });
  useEffect(() => {
    if (!data?.data?.orders) return;
    const orders = data.data.orders.map((order: OrderItems) => ({
      name: order?.product?.title,
      href: `/account/orders/${order._id}`,
    }));
    setCrumbs(orders);
  }, [data?.data?.orders, setCrumbs]);

  return (
    <AccountLayoutWrapper>
      <div className="space-y-4">
        {loading ? (
          <div className="space-y-4">
            {[1, 2].map((_, index) => (
              <OrderCardSkelton key={index} />
            ))}
          </div>
        ) : (
          <div className="space-y-4 max-h-[480px] scrollbar-hide  overflow-y-scroll">
            {data?.data?.orders.map((order: OrderItems) => (
              <OrderCard order={order} key={order._id} />
            ))}
          </div>
        )}

        {!loading && data?.data?.orders.length === 0 && (
          <div className="py-8 text-center">
            <p className="text-gray-500">You haven't placed any orders yet.</p>
            <Link
              to="/shop"
              className="inline-block mt-2 text-orange-1 hover:underline"
            >
              Browse products
            </Link>
          </div>
        )}
      </div>
    </AccountLayoutWrapper>
  );
}

export default MyOrder;
