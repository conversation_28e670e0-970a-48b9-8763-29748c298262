import { Input } from "@/components/ui/input";
import { Modal } from "@/components/ui/modal";
import { Button } from "@/components/ui/button";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { joi<PERSON>esolver } from "@hookform/resolvers/joi";
import { couponSchema } from "../validation";
import { useMutation } from "@tanstack/react-query";
import { showToast } from "@/lib/toast";
import { AxiosError } from "axios";
import { addPromotionalOfferApi } from "../api";
import { CouponFormData } from "../type";

function CouponAddModal({
  isOpen,
  onClose,
  refetch,
}: {
  isOpen: boolean;
  onClose: () => void;
  refetch: () => void;
}) {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<CouponFormData>({
    resolver: joi<PERSON>esolver(couponSchema),
    defaultValues: {
      code: "",
      discount: undefined,
    },
  });

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      reset();
    }
  }, [isOpen, reset]);

  const { mutate, isPending } = useMutation({
    mutationFn: addPromotionalOfferApi,
    onSuccess: () => {
      showToast("Coupon added successfully!", "success");
      refetch();
      onClose();
    },
    onError: (error: AxiosError) => {
      console.error(error.response?.data);
      showToast("Failed to add coupon", "error");
    },
  });

  const onSubmit = (data: CouponFormData) => {
    mutate({ ...data, code: data.code.toUpperCase() });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="p-2">
        <h1 className="mb-4 text-lg font-semibold">Add new coupon</h1>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            {errors.code && (
              <span className="text-sm text-error">{errors.code.message}</span>
            )}
            <Input
              placeholder="Coupon code"
              {...register("code")}
              className={`${errors.code ? "border-error" : ""}`}
            />
          </div>
          <div>
            {errors.discount && (
              <span className="text-sm text-error">
                {errors.discount.message}
              </span>
            )}
            <Input
              placeholder="Discount percentage"
              type="number"
              {...register("discount", { valueAsNumber: true })}
              className={`${errors.discount ? "border-error" : ""}`}
            />
          </div>
          <div className="flex justify-end gap-2 mt-4">
            <Button variant="outline" onClick={onClose} disabled={isPending}>
              Cancel
            </Button>
            <Button disabled={isPending}>
              {isPending ? "Adding..." : "Add Coupon"}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}

export default CouponAddModal;
