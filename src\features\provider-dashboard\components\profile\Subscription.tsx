import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  period: string;
  features: { title: string; description: string }[];
}

const subscriptionPlans: SubscriptionPlan[] = [
  {
    id: "growth-50",
    name: "Growth Provider Tier",
    price: 50,
    period: "month",
    features: [
      {
        title: "Enhanced Marketplace Listing:",
        description: "Bio, service offerings, and introductory video to showcase expertise."
      },
      {
        title: "Priority Search Visibility:",
        description: " Appears above Core providers in relevant search results."
      },
      {
        title: "Highlighted Client Reviews:",
        description: "Ability to pin up to two testimonials for credibility."
      }
    ]
  },
  {
    id: "growth-200",
    name: "Growth Provider Tier",
    price: 200,
    period: "month",
    features: [
      {
        title: "Premier Marketplace Listing:",
        description: "Customizable profile with a \"Verified Premier Provider\" badge and expanded content options."
      },
      {
        title: "Top Search Priority:",
        description: "Always listed at the top of search results within relevant categories."
      },
      {
        title: "Unlimited Review Pinning",
        description: "Unlimited Review Pinning: Showcase multiple positive client testimonials."
      }
    ]
  }
];

function Subscription() {
  const currentPlan = {
    name: "Growth Provider Tier",
    price: 50,
    expiryDate: "1 Feb, 2025"
  };

  const handleCancelPlan = () => {
    // Handle plan cancellation
    console.log("Cancel plan clicked");
  };

  const handleChoosePlan = (planId: string) => {
    // Handle plan selection
    console.log("Choose plan:", planId);
  };

  return (
    <div className="w-full space-y-6 py-8">
      {/* Active Plan Section */}
      <div className="border  p-6 rounded-lg">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-lg font-semibold mb-2">
              Active plan <span className="text-gray-500 text-base font-normal">(expires on {currentPlan.expiryDate})</span>
            </h2>
            <div className="text-3xl font-bold">${currentPlan.price}</div>
          </div>
          <Button 
            variant="outline" 
            onClick={handleCancelPlan}
            className="text-red-600 border-red-600 hover:bg-red-50"
          >
            Cancel plan
          </Button>
        </div>
      </div>

      {/* Subscription Plans */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {subscriptionPlans.map((plan) => (
          <Card key={plan.id} className="border border-gray-200 rounded-lg">
            <CardHeader className="text-center pb-4">
              <CardTitle className="text-lg font-normal mb-3">{plan.name}</CardTitle>
              <div className="text-3xl font-bold">
                ${plan.price}
                <span className="text-base font-normal">/{plan.period}</span>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <Button 
                variant="outline" 
                className="w-full text-orange-1 border-orange-1 hover:bg-orange-50"
                onClick={() => handleChoosePlan(plan.id)}
              >
                Choose
              </Button>
              
              <div>
                <h4 className="font-normal text-sm mb-3">Included</h4>
                <ul className="space-y-3 text-sm text-gray-700">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="text-orange-1 mr-2 mt-1">•</span>
                      <span>
                        {feature.title && <span className="font-semibold">{feature.title}</span>}{" "}
                        {feature.description}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

export default Subscription;
