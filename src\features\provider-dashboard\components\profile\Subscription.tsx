import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { PlanKey, PlanName } from "@/features/auth-provider/type";
import { subscriptionPlans } from "@/lib/constant";
import {
  cancelCurrentSubscriptionApi,
  fetchCurrentSubscriptionApi,
  updateCurrentSubscriptionApi,
} from "../../api";
import { useMutation, useQuery } from "@tanstack/react-query";
import { dateFormatter } from "@/lib/utils";
import { showToast } from "@/lib/toast";
import ClipLoader from "react-spinners/ClipLoader";
import { loadStripe } from "@stripe/stripe-js";
import { useState } from "react";

function Subscription() {
  const [selectedPlan, setSelectedPlan] = useState<PlanName | null>(null);
  const planMapping: Record<PlanKey, PlanName> = {
    coreProvider: "Core Provider (Free)",
    growthProvider: "Growth Provider",
    premierProvider: "Premier Provider Plan",
  };
  const { data, refetch: refetchSubscription } = useQuery({
    queryKey: ["current-subscription"],
    queryFn: fetchCurrentSubscriptionApi,
  });
  const cancelPlan = useMutation({
    mutationFn: cancelCurrentSubscriptionApi,
    onSuccess: () => {
      showToast("Plan cancelled successfully", "success");
      refetchSubscription();
    },
    onError: () => {
      showToast("Failed to cancel plan", "error");
    },
  });

  const updatePlan = useMutation({
    mutationFn: updateCurrentSubscriptionApi,
    onSuccess: (data) => {
      showToast("Plan updated successfully", "success");
      if (data?.sessionId) {
        handleStripe(data.sessionId);
      }
    },
    onError: () => {
      showToast("Failed to update plan", "error");
    },
  });

  const handleCancelPlan = () => {
    cancelPlan.mutate();
  };

  const handleChoosePlan = (plan: PlanKey, title: PlanName) => {
    setSelectedPlan(title);
    updatePlan.mutate(plan);
  };

  const handleStripe = async (sessionId: string) => {
    const stripePromise = loadStripe(
      import.meta.env.VITE_API_STRIPE_SECRET_KEY
    );

    const stripe = await stripePromise;
    if (!stripe) {
      return;
    }
    await stripe.redirectToCheckout({ sessionId });
  };
  return (
    <div className="w-full py-8 space-y-6">
      {/* Active Plan Section */}
      <div className="p-6 border rounded-lg">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="mb-2 text-lg font-semibold">
              {planMapping[data?.subscripiton.subscriptionType as PlanKey]}
              <span className="mx-1 text-base font-normal text-gray-500">
                {data?.subscripiton.endDate &&
                  `(expires on ${dateFormatter(data?.subscripiton.endDate)})`}
              </span>
            </h2>
            <div className="text-3xl font-bold">${data?.price || 0}</div>
          </div>
          <div>
            {data?.subscripiton.endDate && (
              <Button
                disabled={
                  cancelPlan.isPending || data?.subscripiton.isCancelled
                }
                variant="outline"
                onClick={handleCancelPlan}
                className={`${cancelPlan.isPending ? "cursor-not-allowed bg-red-50 px-10" : ""} text-sm text-red-600 border-red-600 md:text-base hover:bg-red-50`}
              >
                {cancelPlan.isPending ? (
                  <ClipLoader size={20} color="#dc2626" />
                ) : (
                  "Cancel plan"
                )}
              </Button>
            )}
            {data?.subscripiton.isCancelled && (
              <p className="mt-2 text-sm italic text-center text-orange-1">
                Plan cancelled{" "}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Subscription Plans */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {subscriptionPlans.map((plan) => {
          return plan.title ===
            planMapping[
              data?.subscripiton.subscriptionType as PlanKey
            ] ? null : (
            <Card key={plan.id} className="border border-gray-200 rounded-lg">
              <CardHeader className="pb-4 text-center">
                <CardTitle className="mb-3 text-lg font-normal">
                  {plan.title}
                </CardTitle>
                <div className="text-3xl font-bold">
                  ${plan.price}
                  <span className="text-base font-normal">/{plan.period}</span>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                <Button
                  disabled={updatePlan.isPending}
                  variant="outline"
                  className="w-full text-orange-1 border-orange-1 hover:border-white hover:bg-orange-1 hover:text-white"
                  onClick={() =>
                    handleChoosePlan(
                      data?.subscripiton.subscriptionType as PlanKey,
                      plan.title
                    )
                  }
                >
                  {updatePlan.isPending && selectedPlan === plan.title ? (
                    <ClipLoader size={20} color="#dc2626" />
                  ) : (
                    "Choose Plan"
                  )}
                </Button>

                <div>
                  <h4 className="mb-3 text-sm font-normal">Included</h4>
                  <ul className="space-y-3 text-sm text-gray-700">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <span className="mt-1 mr-2 text-orange-1">•</span>
                        <span>
                          {feature.title && (
                            <span className="font-semibold">
                              {feature.title}
                            </span>
                          )}{" "}
                          {feature.description}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
}

export default Subscription;
