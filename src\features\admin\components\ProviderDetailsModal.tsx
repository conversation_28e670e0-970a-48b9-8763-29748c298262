import { Modal } from "@/components/ui/modal";
import { fetchProviderApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import CloseIcon from "@/assets/close-icon.svg";
import NextIcon from "@/assets/next.svg";
import DetailRow from "./DetailRow";
import ModalSkelton from "./ModalSkelton";

function ProviderDetailsModal({
  isOpen,
  onClose,
  onOpenServices,
  selectedProviderId,
}: {
  isOpen: boolean;
  onClose: () => void;
  onOpenServices: () => void;
  selectedProviderId: string;
}) {
  const {
    data,
    isPending: loading,
    isSuccess,
  } = useQuery({
    queryKey: ["provider-details", selectedProviderId],
    queryFn: () => fetchProviderApi(selectedProviderId),
    enabled: !!selectedProviderId && isOpen,
  });
  const handleOpenServices = () => {
    onOpenServices();
    onClose();
  };
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      className="md:min-w-[600px] max-h-[85vh] overflow-y-scroll scrollbar-hide"
    >
      <div>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-lg font-semibold text-gray-900">All details</h1>
          <img
            onClick={onClose}
            className="transition-opacity cursor-pointer hover:opacity-70"
            src={CloseIcon}
            alt="close"
          />
        </div>

        {loading ? (
          <ModalSkelton />
        ) : isSuccess && data ? (
          <div className="">
            {/* Profile Section */}
            <div className="flex items-center mb-6 space-x-4">
              <div>
                <img
                  src={data.provider?.profilePicture?.[0]?.url}
                  alt="provider profile"
                  className="w-12 h-12 rounded-full"
                />
              </div>
              <div>
                <h2 className="text-base font-semibold text-gray-900">
                  {`${data.provider?.firstName || "N/A"} ${data.provider?.lastName || ""}`}
                </h2>
                <p className="text-sm text-gray-600">
                  {data.provider?.authId?.email || "N/A"}
                </p>
              </div>
            </div>

            {/* Services Section */}
            <div
              className="flex items-center justify-between py-4 transition-colors border-gray-200 cursor-pointer border-y hover:bg-gray-50"
              onClick={handleOpenServices}
            >
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-900">
                  {data.totalServices > 1
                    ? `${data.totalServices} services listed`
                    : `${data.totalServices} service listed`}
                </span>
              </div>
              <img src={NextIcon} alt="view services" className="" />
            </div>

            {/* Details Section */}
            <div className="space-y-1">
              <DetailRow
                label="Phone number"
                value={data.provider?.phone || "N/A"}
              />
              <DetailRow
                label="Business name"
                value={data.provider?.businessName || "N/A"}
              />
              <DetailRow
                label="Years of Experience"
                value={data.provider?.experience || "N/A"}
              />
              <DetailRow
                label="Preferred location"
                value={data.provider?.serviceLocation[0] || "N/A"}
              />
              <DetailRow label="Tax ID" value={data.provider?.taxId || "N/A"} />
              <DetailRow
                label="specialty"
                value={data.provider.specialty || "N/A"}
              />
              <DetailRow
                label="certifications"
                
                value={data.provider.certifications.length || "N/A"}
              />

              <DetailRow
                label="Introduction video"
                link={data.provider?.introductionVideo?.url}
                value={
                  data.provider?.introductionVideo
                    ? data.provider.introductionVideo.url?.split("/").pop() ||
                      "N/A"
                    : "No video uploaded"
                }
              />
            </div>

            {/* Refund Policy */}
            {data.provider?.refundPolicy && (
              <div className="mt-6">
                <h3 className="mb-3 text-sm text-neutral-300">Refund Policy</h3>
                <div className="text-sm leading-relaxed text-gray-900">
                  <p>{data?.provider?.refundPolicy}</p>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="py-8 text-center">
            <p className="text-gray-500">Failed to load provider details</p>
          </div>
        )}
      </div>
    </Modal>
  );
}

export default ProviderDetailsModal;
