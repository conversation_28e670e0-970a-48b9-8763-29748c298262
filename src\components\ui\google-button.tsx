import { Auth<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, signInWithPopup } from "firebase/auth";
import { Button } from "@/components/ui/button";
import GoogleIcon from "@/assets/google-icon.png";
import { showToast } from "@/lib/toast";
import auth from "@/lib/firebase";
type GoogleButtonProps = {
  text: string;
  disabled: boolean;
  onGoogleLogin: (token: string) => void;
};
export default function GoogleButton({
  text,
  disabled,
  onGoogleLogin,
}: GoogleButtonProps) {
  const handleGoogleLogin = () => {
    const provider = new GoogleAuthProvider();
    signInWithPopup(auth, provider)
      .then((result) => {
        if (result) {
          result.user
            .getIdToken()
            .then((token) => {
              onGoogleLogin(token);
            })
            .catch((error) => {
              throw error;
            });
        }
      })
      .catch((error: AuthError) => {
        const excludedErrorCodes = [
          "auth/popup-closed-by-user",
          "auth/cancelled-popup-request",
        ];
        if (!excludedErrorCodes.includes(error.code)) {
          showToast("failed to login with google", "error");
        }

      });
  };
  return (
    <Button
      variant="outline"
      className="flex items-center justify-center w-full gap-2 p-3 text-black rounded-sm"
      onClick={handleGoogleLogin}
      disabled={disabled}
    >
      <div className="flex gap-x-2">
        <img src={GoogleIcon} alt="Google Icon" className="my-auto w-7 h-7" />
        <p className="text-xl">{text}</p>
      </div>
    </Button>
  );
}
