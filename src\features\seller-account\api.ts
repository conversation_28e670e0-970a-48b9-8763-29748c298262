import api from "@/lib/axios";
import { SellerDetailsApiResponse } from "./type";

export const updateSellerProfileApi = async ({
  payload,
  sellerId,
}: {
  payload: FormData;
  sellerId: string;
}) => {
  const { data } = await api.patch(
    `/api/v1/users/seller/${sellerId}`,
    payload,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return data;
};

export const fetchSellerDetailssApi =
  async (): Promise<SellerDetailsApiResponse> => {
    const { data } = await api.get("/api/v1/users/seller/profile");

    return data?.data;
  };
export const fetchPromotionalOffersApi = async () => {
  const { data } = await api.get("/api/v1/coupon");
  return data.data;
};
export const DeletePromotionalOfferApi = async (couponId: string) => {
  const { data } = await api.delete(`/api/v1/coupon/${couponId}`);
  return data;
};

export const addPromotionalOfferApi = async (payload: {
  code: string;
  discount: number;
}) => {
  const { data } = await api.post("/api/v1/coupon", payload);
  return data;
};
