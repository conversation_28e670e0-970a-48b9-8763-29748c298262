import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import Layout from "@/components/layout/ProviderLayout";
import BookingSection from "../components/BookingSection";
import { But<PERSON> } from "@/components/ui/button";
import {  Check, ChevronRight, Pencil, Trash2 } from "lucide-react";
import { LuCalendarClock, LuCalendarCheck, LuCalendarPlus } from "react-icons/lu";
import { getServiceDetailsApi, updateServiceApi, deleteServiceApi, type ServiceDetails } from "../api";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import EditServiceDialog, { EditServiceFormData } from "../components/EditServiceDialog";
import DeleteServiceDialog from "../components/DeleteServiceDialog";
import { showToast } from "@/lib/toast";

// Mock booking data
const mockBookings = {
  newBookings: [
    { id: "1", name: "<PERSON>", date: "1 Jan 2025", time: "2:00pm - 3:00pm" },
    { id: "2", name: "<PERSON>", date: "1 <PERSON> 2025", time: "2:00pm - 3:00pm" }
  ],
  upcomingSessions: [
    { id: "3", name: "Amy Keen", date: "1 Jan 2025", time: "2:00pm - 3:00pm" }
  ],
  completedSessions: [
    { id: "4", name: "<PERSON> Keen", date: "1 Jan 2025", time: "2:00pm - 3:00pm" },
    { id: "5", name: "Amy Keen", date: "1 Jan 2025", time: "2:00pm - 3:00pm" }
  ]
};

function ServiceDetails() {
  const { serviceId } = useParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Dialog states
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Fetch service details using useQuery
  const {
    data: serviceData,
    isPending: loading,
    error,
  } = useQuery({
    queryKey: ["serviceDetails", serviceId],
    queryFn: () => getServiceDetailsApi(serviceId!),
    enabled: !!serviceId,
  });

  const service = serviceData?.data?.service || null;

  // Update service mutation
  const updateServiceMutation = useMutation({
    mutationFn: (data: EditServiceFormData) => updateServiceApi(serviceId!, data),
    onSuccess: () => {
      showToast("Service updated successfully!", "success");
      setEditDialogOpen(false);
      // Invalidate and refetch service details
      queryClient.invalidateQueries({ queryKey: ["serviceDetails", serviceId] });
    },
    onError: (error) => {
      console.error("Update service error:", error);
      showToast("Failed to update service. Please try again.", "error");
    },
  });

  // Delete service mutation
  const deleteServiceMutation = useMutation({
    mutationFn: () => deleteServiceApi(serviceId!),
    onSuccess: () => {
      showToast("Service deleted successfully!", "success");
      setDeleteDialogOpen(false);
      // Navigate back to services page
      navigate("/provider/services");
    },
    onError: (error) => {
      console.error("Delete service error:", error);
      showToast("Failed to delete service. Please try again.", "error");
    },
  });

  const handleBackToServices = () => {
    navigate("/provider/services");
  };

  const handleEdit = () => {
    setEditDialogOpen(true);
  };

  const handleDelete = () => {
    setDeleteDialogOpen(true);
  };

  const handleEditSubmit = (data: EditServiceFormData) => {
    updateServiceMutation.mutate(data);
  };

  const handleDeleteConfirm = () => {
    deleteServiceMutation.mutate();
  };

  if (loading) {
    return (
      <Layout showFooter={true}>
        <div className="flex items-center justify-center h-64 mt-4">
          <div className="text-lg">Loading service details...</div>
        </div>
      </Layout>
    );
  }

  if (error || !service) {
    return (
      <Layout showFooter={true}>
        <div className="flex flex-col items-center justify-center h-64 gap-4 mt-4">
          <div className="text-lg text-red-500">
            {error ? "Failed to load service details. Please try again." : "Service not found"}
          </div>
          <Button onClick={handleBackToServices} variant="outline">
            Back to Services
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout showFooter={true}>
      <div className="w-full mt-4">
        {/* Header with breadcrumb and actions */}
        <div className="flex flex-col justify-between gap-4 mb-6 md:flex-row md:items-center">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToServices}
              className="h-auto p-0 text-base text-gray-600 hover:bg-transparent hover:text-gray-800"
            >
              Services
            </Button>
            <span className="text-gray-400"> <ChevronRight className="w-4 h-4" />
            </span>
            <span className="text-lg font-medium text-gray-900">{service.title}</span>
          </div>

          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleEdit}
              className="flex items-center gap-1 border-gray-300 hover:bg-gray-50"
            >
              <Pencil className="w-5 h-5" />

            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDelete}
              className="flex items-center gap-1 text-red-600 border-red-200 hover:bg-red-50"
            >
              <Trash2 className="w-5 h-5" />

            </Button>
          </div>
        </div>

        {/* Service Details Card */}
        <div className="p-6 mb-6 border border-gray-200 rounded-lg">
          <div className="flex items-start justify-between mb-4">
            <div>
              <h1 className="mb-1 text-xl font-semibold">{service.title}</h1>
              <p className="text-gray-600">{service.duration > 1 ? `${service.duration} hours` : `${service.duration} hour`}</p>
            </div>
            <div>
              <span className="text-xl font-semibold">${service.price}</span>
            </div>
          </div>

          <div className="mb-6">
            <p className="leading-relaxed text-gray-700">{service.description}</p>
          </div>

          {service.highlights && service.highlights.length > 0 && (
            <div className="p-6 mb-8 border rounded-lg border-tints-50 bg-tints-30">
              <h2 className="mb-4 text-xs text-gray-700">SERVICE HIGHLIGHTS</h2>
              <div className="grid grid-cols-1 gap-3 md:grid-cols-3">
                {service.highlights.map((highlight: string, index: number) => (
                  <div key={index} className="flex items-start gap-2">
                    <Check className="flex-shrink-0 w-4 h-4 "/>
                    <span className="text-sm text-gray-700">{highlight}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

        </div>

        {/* Service Highlights */}

        {/* Bookings Section */}
        <div className="mb-10">
          <BookingSection
            title="New bookings"
            icon={<LuCalendarPlus className="w-5 h-5" />}
            count={mockBookings.newBookings.length}
            bookings={mockBookings.newBookings}
            type="pending"
          />

          <BookingSection
            title="Upcoming sessions"
            icon={<LuCalendarClock className="w-5 h-5" />}
            count={mockBookings.upcomingSessions.length}
            bookings={mockBookings.upcomingSessions}
            type="upcoming"
          />

          <BookingSection
            title="Completed sessions"
            icon={<LuCalendarCheck className="w-5 h-5" />}
            count={mockBookings.completedSessions.length}
            bookings={mockBookings.completedSessions}
            type="completed"
          />
        </div>
      </div>

      {/* Edit Service Dialog */}
      <EditServiceDialog
        open={editDialogOpen}
        onOpenChange={setEditDialogOpen}
        onSubmit={handleEditSubmit}
        initialData={service}
        isLoading={updateServiceMutation.isPending}
      />

      {/* Delete Service Dialog */}
      <DeleteServiceDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        serviceName={service?.title}
        isLoading={deleteServiceMutation.isPending}
      />
    </Layout>
  );
}

export default ServiceDetails;
