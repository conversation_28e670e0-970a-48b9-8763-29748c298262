import "react-quill/dist/quill.snow.css";
import ReactQuill from "react-quill";
import { useEffect, useRef } from "react";

function TextEditor({
  value,
  onChange,
  className,
}: {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}) {
  const modules = {
    toolbar: [
      [{ header: [1, 2, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ align: [] }],
      [{ color: [] }, { background: [] }],
    ],
  };

  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "list",
    "bullet",
    "link",
    "image",
    "align",
    "color",
    "background",
  ];
  const inputRef = useRef<HTMLInputElement>(null);
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);
  return (
    <ReactQuill
      theme="snow"
      onChange={(val) => onChange(val)}
      value={value}
      modules={modules}
      formats={formats}
      className={`${className} h-[280px]`}
    />
  );
}

export default TextEditor;
