import { Link } from "react-router-dom";
import { OrderStatusType, OrderTableItems } from "../type";
import NextIcon from "@/assets/next.svg";
import OrderStatusBadge from "@/components/ui/order-status-badge";
function OrderProductCard({ order }: { order: OrderTableItems }) {
  if (!order) return null;
  return (
    <div className="border rounded-lg border-gray-2">
      <div className="flex items-center p-4 gap-x-3 justify-evenly">
        <img
          className="object-cover max-w-[70px] max-h-[70px]"
          src={order?.productImage}
          alt={order?.productTitle}
        />
        <div className="grow">
          <h1 className="mb-2 text-sm font-semibold text-start">
            {order?.productTitle}
          </h1>
          <OrderStatusBadge
            className="text-sm"
            status={order.status as OrderStatusType}
          />
        </div>
        <Link to={`/seller/orders/${order?._id}`}>
          <img src={NextIcon} alt="go to next page" />
        </Link>
      </div>
      {/* content */}
      <div className="p-4 border-t border-gray-2">
        <div className="flex justify-between w-3/4 gap-x-3">
          <div className="flex flex-col justify-between gap-y-2">
            <h4 className="text-sm text-neutral-300">Ordered by</h4>
            <span className="text-sm text-neutral-600">
              {order?.firstName}
            </span>
          </div>
          <div className="flex flex-col justify-between">
            <h4 className="text-sm text-neutral-300">Order ID</h4>
            <span className="text-sm text-neutral-600">{order?._id}</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default OrderProductCard;
