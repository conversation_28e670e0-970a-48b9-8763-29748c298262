import { Navigate } from "react-router-dom";
import AuthLayout from "../components/AuthLayout";
import ForgotPasswordForm from "../components/ForgotPasswordForm";
import { checkAuthentication } from "@/lib/utils";

function ForgotPassword() {
  const { isLoggedIn, href } = checkAuthentication();

  return isLoggedIn ? (
    <Navigate to={href} />
  ) : (
    <AuthLayout>
      <ForgotPasswordForm />
    </AuthLayout>
  );
}

export default ForgotPassword;
