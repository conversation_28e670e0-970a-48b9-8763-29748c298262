export type RegisterPayload = {
  email: string | undefined;
  password: string | undefined;
};

export type LoginPayload = {
  email: string;
  password: string;
};

export type FormData = {
  email: string;
  password: string;
  confirmPassword: string;
};

export type OtpPayloadData = {
  otp: string;
  email: string;
};

export type profileData = {
  firstName: string;
  lastName: string;
  phone: string;
  specialty: string;
  businessName: string;
  taxId: string;
  yearsOfExperience: string;
  serviceLocation: string[];
  introductoryVideo?: FileList;
  certifications?: FileList;
  refundPolicy: string;
  photo?: File;
  authId?: string;
};

export type forgotPasswordPayload = {
  email: string;
};

export type resetPasswordPayload = {
  newPassword: string;
  confirmPassword: string;
  token: string;
};

export type AuthData = {
  _id: string;
  email: string;
  isValid: boolean;
  hasCompletedProfile: boolean;
  passwordChangedAt: string;
  createdAt: string;
  __v: number;
  otpExpiresAt: string | null;
  otpRequestInterval: string | null;
};

export type ProviderData = {
  _id: string;
  authId: string;
  role: string;
  firstName: string;
  lastName: string;
  phone: string;
  specialty: string;
  businessName: string;
  taxId: string;
  isValid: boolean;
  isApproved: boolean;
  isDeleted: boolean;
  experience: number;
  serviceLocation: string[];
  hasAddedServices: boolean;
  planChoosen: boolean;
  hasSetAvailability: boolean;
  profilePicture?: Array<{
    url: string;
    key: string;
  }>;
  introductionVideo?: {
    video: string;
    key: string;
  };
  certifications?: Array<{
    url: string;
    key: string;
    _id: string;
  }>;
  refundPolicy?: string;
  createdAt: string;
  __v: number;
};

export type LoginResponse = {
  status: string;
  message: string;
  accessToken?: string;
  data?: {
    auth?: AuthData;
    provider?: ProviderData;
    accessToken?: string;
  };
};

export type ProviderGoogleApiResponse = {
  auth: {
    _id: string;
    hasCompletedProfile: boolean;
  };
  provider?: {
    _id: string;
    isApproved: boolean;
    hasSetAvailability: boolean;
    hasAddedServices: boolean;
    planChoosen: boolean;
  };
  accessToken?: string;
};

export type PlanId = "core" | "growth" | "premier";
export type PlanKey = "coreProvider" | "growthProvider" | "premierProvider";
export type PlanName =
  | "Core Provider (Free)"
  | "Growth Provider"
  | "Premier Provider Plan";
export type Plan = {
  id: PlanId;
  title: PlanName;
  price: string;
  period: string;
  description: string;
  features: string[];
  readMoreText: string;
};
