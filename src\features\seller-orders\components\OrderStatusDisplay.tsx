import DeliverIcon from "@/assets/deliver.svg";
import PendingIcon from "@/assets/pending.svg";
import { dateFormatter } from "@/lib/utils";

function OrderStatusDisplay({
  status,
  date,
  completed,
}: {
  status: string;
  date: string | null;
  completed: boolean;
}) {

  return (
    <div>
      <div className="flex gap-x-1">
        <img
          className="mt-0.5 mb-auto"
          src={completed ? DeliverIcon : PendingIcon}
          alt="completed"
        />
        <div className="flex flex-col justify-between gap-x-1">
          <span className="font-semibold">{status}</span>
          {date && (
            <p className="text-sm text-neutral-300">{dateFormatter(date)}</p>
          )}
        </div>
      </div>
    </div>
  );
}

export default OrderStatusDisplay;
