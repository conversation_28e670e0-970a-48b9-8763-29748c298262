import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import { showToast } from "@/lib/toast";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { DeletePromotionalOfferApi } from "../api";
function CouponDeleteModal({
  isOpen,
  onClose,
  couponId,
  refetch,
}: {
  isOpen: boolean;
  onClose: () => void;
  refetch: () => void;
  couponId: string;
}) {
  const { mutate, isPending: loading } = useMutation({
    mutationFn: DeletePromotionalOfferApi,
    onSuccess: () => {
      showToast("Coupon deleted successfully!", "success");
      refetch();
      onClose();
    },
    onError: (error: AxiosError) => {
      console.error(error.response?.data);
      showToast("Failed to delete coupon", "error");
    },
  });
  const handleDelete = () => {
    mutate(couponId);
  };
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className="">
        <h2 className="mb-2 text-lg font-semibold text-gray-900">
          Are you sure you want to delete?
        </h2>
        <div className="flex justify-end gap-4 mt-5">
          <Button variant={"outline"} onClick={onClose}>
            No
          </Button>
          <Button
            disabled={loading}
            className="rounded-full"
            variant={"destructive"}
            onClick={handleDelete}
          >
            Yes, Delete
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default CouponDeleteModal;
