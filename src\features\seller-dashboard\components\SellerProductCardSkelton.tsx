import { Skeleton } from "@/components/ui/skeleton";

function SellerProductCardSkelton() {
  return (
    <div className="border border-gray-200 rounded-lg">
      <div className="flex items-center justify-between p-4">
        <Skeleton className="w-[50px] h-[50px] rounded" />
        <Skeleton className="w-1/2 h-6 mx-2" />
        <Skeleton className="w-5 h-5" />
      </div>

      {/* Content */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex justify-between w-3/4">
          <div className="flex flex-col gap-y-2">
            <Skeleton className="w-32 h-4" />
            <Skeleton className="w-16 h-5" />
          </div>
          <div className="flex flex-col gap-y-2">
            <Skeleton className="w-20 h-4" />
            <Skeleton className="w-16 h-5" />
          </div>
        </div>
      </div>

      {/* Description */}
      <div className="flex flex-col p-4 border-t border-gray-200 gap-y-3">
        <Skeleton className="w-32 h-4" />
        <Skeleton className="w-full h-4" />
        <Skeleton className="w-11/12 h-4" />
        <Skeleton className="w-2/3 h-4" />
      </div>
    </div>
  );
}

export default SellerProductCardSkelton;
