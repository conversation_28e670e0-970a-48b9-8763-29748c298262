import { FilterProps } from "../type";
import { useQuery } from "@tanstack/react-query";
import { fetchBrandsApi, fetchCategoriesApi } from "../api";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import FilterIcon from "@/assets/settings.png";
import CloseLogo from "@/assets/close-icon.png";
import { useState } from "react";
import FilterSection from "./FilterSection";
import { Button } from "@/components/ui/button";
function MobileFilterSection({
  selectedCategories,
  selectedBrands,
  priceRange,
  removeCategory,
  addCategory,
  addBrand,
  removeBrand,
  setPriceRange,
  clearFilter,
}: FilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);
  const categoriesData = useQuery({
    queryKey: ["category"],
    queryFn: fetchCategoriesApi,
  });
  const brandsData = useQuery({
    queryKey: ["brands"],
    queryFn: fetchBrandsApi,
  });

  const categories = categoriesData?.isSuccess
    ? categoriesData.data.data.categories
    : [];
  const brands = brandsData?.isSuccess ? brandsData.data.data.brands : [];
  return (
    <>
      <div className="flex justify-evenly gap-x-3">
        <Select onValueChange={addCategory}>
          <SelectTrigger className="w-full rounded-full">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            {categories?.map((category: string) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select onValueChange={addBrand}>
          <SelectTrigger className="w-full rounded-full">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            {brands?.map((brand: string) => (
              <SelectItem key={brand} value={brand}>
                {brand}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <div
          onClick={handleOpen}
          className="flex justify-center px-8 border rounded-full border-input"
        >
          <img src={FilterIcon} alt="filter" className="my-auto " />
        </div>
      </div>
      {isOpen && (
        <div className="absolute  top-0 right-0 min-h-[200%] min-w-[100%] z-10 bg-white  md:hidden">
          <div className="relative ">
            <div className="flex justify-between w-11/12 mx-auto mt-5 ">
              <h3 className="ml-2">All Filters</h3>
              <img
                onClick={handleClose}
                src={CloseLogo}
                alt="close"
                className="my-auto"
              />
            </div>
            <div className="p-4 py-8 mt-4 border rounded-lg border-input">
              <FilterSection
                addBrand={addBrand}
                addCategory={addCategory}
                removeCategory={removeCategory}
                removeBrand={removeBrand}
                selectedBrands={selectedBrands}
                selectedCategories={selectedCategories}
                priceRange={priceRange}
                setPriceRange={setPriceRange}
              />
            </div>
            <div className="flex justify-between w-11/12 mx-auto mt-5">
              <button onClick={clearFilter} className="text-error">
                Clear filter
              </button>
              <Button className="px-8" onClick={handleClose}>Apply</Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default MobileFilterSection;
