import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { addressSchema } from "@/features/cart/validation";
import { ShippingAddressData } from "../type";
import { fetchShipmentAddressApi, updateShipmentAddressApi } from "@/features/cart/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { showToast } from "@/lib/toast";

function AccountShippingAddress() {
  const [isEditing, setIsEditing] = useState(false);
  const queryClient = useQueryClient();

  const { data, isLoading } = useQuery({
    queryKey: ["shipment-address"],
    queryFn: fetchShipmentAddressApi,
  });

  const shippingAddress = data?.data?.shippingAddress || {};
  const hasAddress = Object.keys(shippingAddress).length > 0;

  const formattedAddress = hasAddress
    ? `${shippingAddress.address}, ${shippingAddress.city}, ${shippingAddress.state}, ${shippingAddress.zipCode}`
    : "";

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ShippingAddressData>({
    resolver: joiResolver(addressSchema),
    defaultValues: {
      address: "",
      city: "",
      state: "",
      zipCode: "",
    },
  });

  // Set form values when data is loaded
  useEffect(() => {
    if (hasAddress && data?.data?.shippingAddress) {
      reset(data.data.shippingAddress);
    }
  }, [data, hasAddress, reset]);

  const { mutate, isPending: loading } = useMutation({
    mutationFn: updateShipmentAddressApi,
    onSuccess: () => {
      showToast("Address successfully updated", "success");
      setIsEditing(false);
      queryClient.invalidateQueries({ queryKey: ["shipment-address"] });
    },
    onError: () => {
      showToast("Something went wrong", "error");
    },
  });

  const onSubmit = (data: ShippingAddressData) => {
    mutate(data);
  };

  if (isLoading) {
    return (
      <div className="mt-8 animate-pulse">
        <div className="h-5 w-32 bg-gray-200 rounded mb-2"></div>
        <div className="h-4 w-64 bg-gray-200 rounded"></div>
      </div>
    );
  }

  return (
    <div className="mt-8">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-base font-medium">Shipping address</h3>
        {!isEditing && (
          <button
            type="button"
            className="text-orange-1 text-sm"
            onClick={() => setIsEditing(true)}
          >
            {hasAddress ? "edit" : "add"}
          </button>
        )}
      </div>

      {isEditing ? (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <Input
              placeholder="Street Address"
              {...register("address")}
              className="w-full border border-gray-200 focus:border-orange-1 focus:outline-none"
            />
            {errors.address && (
              <p className="mt-1 text-xs text-red-500">
                {errors.address.message}
              </p>
            )}
          </div>

          <div>
            <Input
              placeholder="City"
              {...register("city")}
              className="w-full border border-gray-200 focus:border-orange-1 focus:outline-none"
            />
            {errors.city && (
              <p className="mt-1 text-xs text-red-500">{errors.city.message}</p>
            )}
          </div>

          <div>
            <Input
              placeholder="State"
              {...register("state")}
              className="w-full border border-gray-200 focus:border-orange-1 focus:outline-none"
            />
            {errors.state && (
              <p className="mt-1 text-xs text-red-500">
                {errors.state.message}
              </p>
            )}
          </div>

          <div>
            <Input
              placeholder="ZIP Code"
              {...register("zipCode")}
              className="w-full border border-gray-200 focus:border-orange-1 focus:outline-none"
            />
            {errors.zipCode && (
              <p className="mt-1 text-xs text-red-500">
                {errors.zipCode.message}
              </p>
            )}
          </div>

          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsEditing(false);
                if (hasAddress && data?.data?.shippingAddress) {
                  reset(data.data.shippingAddress);
                }
              }}
              className="px-4"
            >
              Cancel
            </Button>
            <Button
              disabled={loading}
              type="submit"
              className="px-4"
            >
              {loading ? "Saving..." : "Save"}
            </Button>
          </div>
        </form>
      ) : (
        <p className="text-gray-700">
          {formattedAddress || "No shipping address added yet."}
        </p>
      )}
    </div>
  );
}

export default AccountShippingAddress;
