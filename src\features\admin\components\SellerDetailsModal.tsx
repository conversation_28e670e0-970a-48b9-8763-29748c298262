import { Modal } from "@/components/ui/modal";
import { fetchSellerApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import CloseIcon from "@/assets/close-icon.svg";
import NextIcon from "@/assets/next.svg";
import DetailRow from "./DetailRow";
import ModalSkelton from "./ModalSkelton";

function SellerDetailsModal({
  isOpen,
  onClose,
  onOpenProducts,
  selectedSellerId,
}: {
  isOpen: boolean;
  onClose: () => void;
  onOpenProducts: () => void;
  selectedSellerId: string;
}) {
  const {
    data,
    isPending: loading,
    isSuccess,
  } = useQuery({
    queryKey: ["seller-details", selectedSellerId],
    queryFn: () => fetchSellerApi(selectedSellerId),
    enabled: !!selectedSellerId && isOpen,
  });
  const handleOpenProducts = () => {
    onOpenProducts();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      className="md:min-w-[600px] max-h-[90vh] overflow-y-scroll scrollbar-hide"
    >
      <div>
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-lg font-semibold text-gray-900">All details</h1>
          <img
            onClick={onClose}
            className="transition-opacity cursor-pointer hover:opacity-70"
            src={CloseIcon}
            alt="close"
          />
        </div>

        {loading ? (
          <ModalSkelton />
        ) : isSuccess && data ? (
          <div className="">
            {/* Profile Section */}
            <div className="flex items-center mb-6 space-x-4">
              <div>
                <h2 className="text-base font-semibold text-gray-900">
                  {data.seller?.companyName || "N/A"}
                </h2>
                <p className="text-sm text-gray-600">
                  {data.seller?.authId?.email || "N/A"}
                </p>
              </div>
            </div>

            {/* Services Section */}
            <div
              className="flex items-center justify-between py-4 transition-colors border-gray-200 cursor-pointer border-y hover:bg-gray-50"
              onClick={handleOpenProducts}
            >
              <div className="flex items-center space-x-3">
                <span className="text-sm text-gray-900">
                  {data.productCount > 1
                    ? `${data.productCount} products listed`
                    : `${data.productCount} product listed`}
                </span>
              </div>
              <img src={NextIcon} alt="view services" className="" />
            </div>

            {/* Details Section */}
            <div className="space-y-1">
              <DetailRow
                label="Phone number"
                value={data.seller?.phone || "N/A"}
              />
              <DetailRow
                label="Business name"
                value={data.seller?.companyName || "N/A"}
              />
              <DetailRow label="Tax ID" value={data.seller?.taxId || "N/A"} />
              <DetailRow
                label="Category"
                value={data.seller?.category || "N/A"}
              />

              <DetailRow
                label="Introduction video"
                link={data.seller?.introductionVideo.url}
                value={
                  data.seller?.introductionVideo
                    ? data.seller.introductionVideo.url.split("/").pop() ||
                      "N/A"
                    : "No video uploaded"
                }
              />
            </div>

            {/* Refund Policy */}
            {data.seller?.refundPolicy && (
              <div className="mt-6">
                <h3 className="mb-3 text-sm text-neutral-300">Refund Policy</h3>
                <div className="text-sm leading-relaxed text-gray-900">
                  <p>{data?.seller?.refundPolicy}</p>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="py-8 text-center">
            <p className="text-gray-500">Failed to load seller details</p>
          </div>
        )}
      </div>
    </Modal>
  );
}

export default SellerDetailsModal;
