import Joi from "joi";

export const couponSchema = Joi.object({
  code: Joi.string()
    .required()
    .min(3)
    .max(20)
    .pattern(/^[A-Za-z0-9]+$/)
    .trim()
    .messages({
      "any.required": "Coupon code is required",
      "string.base": "Coupon code must be a string",
      "string.min": "Coupon code must be at least {#limit} characters",
      "string.max": "Coupon code must be at most {#limit} characters",
      "string.empty": "Coupon code cannot be empty",
      "string.pattern.base":
        "Coupon code must contain only uppercase letters and numbers",
    }),
  discount: Joi.number().required().min(1).max(100).messages({
    "number.base": "Discount must be a number",
    "any.required": "Discount is required",
    "number.min": "Discount must be at least {#limit}%",
    "number.max": "Discount cannot exceed {#limit}%",
  }),
});

export const ProfileSchema = Joi.object({
  companyName: Joi.string().required().min(2).max(100).trim().messages({
    "any.required": "Company name is a required field",
    "string.base": "Company name must be a string",
    "string.min": "Company name should have at least {#limit} characters",
    "string.max": "Company name should have at most {#limit} characters",
    "string.empty": "Company name cannot be empty",
  }),

  category: Joi.string().required().min(1).max(100).trim().messages({
    "any.required": "Category is a required field",
    "string.base": "Category must be a string",
    "string.min": "Category should have at least {#limit} characters",
    "string.max": "Category should have at most {#limit} characters",
    "string.empty": "Category cannot be empty",
  }),

  refundPolicy: Joi.string().required().trim().min(10).max(400).messages({
    "string.base": "Refund policy must be a string",
    "any.required": "Refund policy is a required field",
    "string.empty": "Refund policy cannot be empty",
    "string.min": `Refund policy must be at least {#limit} characters`,
    "string.max": `Refund policy must be at most {#limit} characters`,
  }),
  phone: Joi.string().pattern(/^\d+$/).required().messages({
    "string.pattern.base": "Phone number must contain digits only.",
    "string.empty": "Phone number cannot be empty.",
    "any.required": "Phone number is required.",
  }),
  taxId: Joi.string().alphanum().min(6).max(20).required().messages({
    "string.base": `Tax id should be a text`,
    "string.alphanum": `Tax id must contain only letters and numbers`,
    "string.empty": `Tax id cannot be empty`,
    "string.min": `Tax id must be at least {#limit} characters`,
    "string.max": `Tax id must be at most {#limit} characters`,
    "any.required": `Tax id is a required field`,
  }),
  video: Joi.any()
    .optional()
    .custom((value, helpers) => {
      const file: File | undefined = value?.[0];
      if (!file) return value;

      const allowedTypes = ["video/mp4", "video/webm", "video/ogg"];
      if (!allowedTypes.includes(file.type)) {
        return helpers.error("any.invalid", {
          message: "Only MP4, WebM, and OGG formats are allowed.",
        });
      }

      const maxSizeMB = 100;
      if (file.size > maxSizeMB * 1024 * 1024) {
        return helpers.error("any.custom", {
          message: `Video must be smaller than ${maxSizeMB}MB.`,
        });
      }

      return value;
    }),
});
