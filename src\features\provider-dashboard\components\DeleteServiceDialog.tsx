import { Dialog, DialogContent, DialogTitle, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { X, AlertTriangle } from "lucide-react";

type DeleteServiceDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  serviceName?: string;
  isLoading?: boolean;
};

export default function DeleteServiceDialog({
  open,
  onOpenChange,
  onConfirm,
  serviceName = "this service",
  isLoading = false,
}: DeleteServiceDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] p-0 gap-0 overflow-hidden">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <DialogTitle className="text-lg font-semibold">
              Delete Service
            </DialogTitle>
            <DialogClose className="rounded-full h-6 w-6 flex items-center justify-center">
              <X className="h-4 w-4" />
            </DialogClose>
          </div>

          <div className="space-y-4">
            {/* Warning Icon and Message */}
            <div className="flex items-center gap-3 p-4 bg-red-50 rounded-lg border border-red-200">
              <AlertTriangle className="h-6 w-6 text-red-600 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-red-900">Are you sure?</h3>
                <p className="text-sm text-red-700 mt-1">
                  This action cannot be undone. This will permanently delete "{serviceName}" and remove all associated data.
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-2">
              <Button
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="flex-1"
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={onConfirm}
                className="flex-1"
                disabled={isLoading}
              >
                {isLoading ? "Deleting..." : "Delete Service"}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
