import { Button } from "@/components/ui/button";
import { useCartStore } from "@/store/cartStore";
import { loadStripe } from "@stripe/stripe-js";
import { checkoutApi } from "../api";
import { showToast } from "@/lib/toast";
import { useState } from "react";

const stripePromise = loadStripe(import.meta.env.VITE_API_STRIPE_SECRET_KEY);

const CheckoutButton = ({
  isDisabled,
  payableAmount,
}: {
  isDisabled: boolean;
  payableAmount: number;
}) => {
  const { fetchCart } = useCartStore();
  const cartItems = fetchCart();
  const [loading, setLoading] = useState(false);
  const handleClick = async () => {
    try {
      if (loading) return;
      setLoading(true);
      const stripe = await stripePromise;

      const { data } = await checkoutApi({
        cart: cartItems,
        total: payableAmount,
      });

      const { id: sessionId } = data;

      if (stripe) {
        await stripe.redirectToCheckout({ sessionId });
      }
    } catch (err) {
      console.log(err, "err");
      showToast("something went wrong", "error");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      type="submit"
      className="w-full"
      disabled={isDisabled}
      onClick={handleClick}
    >
      {loading ? "Processing..." : "Proceed to Payment"}
    </Button>
  );
};

export default CheckoutButton;
