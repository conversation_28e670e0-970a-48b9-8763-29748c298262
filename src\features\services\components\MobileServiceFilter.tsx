import { ServiceFilterProps } from "../types";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import FilterIcon from "@/assets/settings.png";
import CloseLogo from "@/assets/close-icon.png";
import { useState } from "react";
import ServiceFilter from "./ServiceFilter";
import { Button } from "@/components/ui/button";

// Mock data - replace with actual API calls when available
const specialties = [
  "Lactation Consultant",
  "Doula Services", 
  "Sleep Support",
  "Physical Recovery",
  "Infant Care Nursing",
  "Postpartum Care",
  "Mental Health Support"
];

const locations = ["Virtual", "In person"];

function MobileServiceFilter({
  selectedSpecialties,
  selectedLocations,
  priceRange,
  selectedAvailability,
  removeSpecialty,
  addSpecialty,
  addLocation,
  removeLocation,
  setPriceRange,
  addAvailability,
  removeAvailability,
  clearFilter,
}: ServiceFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);

  return (
    <>
      <div className="flex justify-evenly gap-x-3">
        <Select onValueChange={addSpecialty}>
          <SelectTrigger className="w-full rounded-full">
            <SelectValue placeholder="Specialty" />
          </SelectTrigger>
          <SelectContent>
            {specialties?.map((specialty: string) => (
              <SelectItem key={specialty} value={specialty}>
                {specialty}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select onValueChange={addLocation}>
          <SelectTrigger className="w-full rounded-full">
            <SelectValue placeholder="Location" />
          </SelectTrigger>
          <SelectContent>
            {locations?.map((location: string) => (
              <SelectItem key={location} value={location}>
                {location}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <div
          onClick={handleOpen}
          className="flex justify-center px-8 border rounded-full border-input"
        >
          <img src={FilterIcon} alt="filter" className="my-auto" />
        </div>
      </div>
      {isOpen && (
        <div className="absolute top-0 right-0 min-h-[200%] min-w-[100%] z-10 bg-white md:hidden">
          <div className="relative">
            <div className="flex justify-between w-11/12 mx-auto mt-5">
              <h3 className="ml-2">All Filters</h3>
              <img
                onClick={handleClose}
                src={CloseLogo}
                alt="close"
                className="my-auto"
              />
            </div>
            <div className="p-4 py-8 mt-4 border rounded-lg border-input">
              <ServiceFilter
                selectedSpecialties={selectedSpecialties}
                selectedLocations={selectedLocations}
                priceRange={priceRange}
                selectedAvailability={selectedAvailability}
                removeSpecialty={removeSpecialty}
                addSpecialty={addSpecialty}
                addLocation={addLocation}
                removeLocation={removeLocation}
                setPriceRange={setPriceRange}
                addAvailability={addAvailability}
                removeAvailability={removeAvailability}
              />
            </div>
            <div className="flex justify-between w-11/12 mx-auto mt-5">
              <button onClick={clearFilter} className="text-error">
                Clear filter
              </button>
              <Button className="px-8" onClick={handleClose}>Apply</Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default MobileServiceFilter;
