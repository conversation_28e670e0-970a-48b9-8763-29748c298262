import React from "react";
import SignupImage from "@/assets/seller-auth.png";
function AuthLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <div className="flex flex-col mb-2 md:mb-0 md:flex-row">
        <div className="md:w-1/2 mb-14 md:mb-0">
          <img
            src={SignupImage} 
            alt="Mom and baby"
            className="h-[280px] w-full md:w-auto block md:h-screen "
          />
        
        </div>
        <div className="w-full md:w-1/2 flex items-center justify-center">
          <div className="w-11/12 mx-auto">{children}</div>
        </div>
      </div>
    </>
  );
}

export default AuthLayout;
