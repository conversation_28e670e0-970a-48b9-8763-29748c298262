import { useParams, useNavigate } from "react-router-dom";
import PageLayout from "@/components/layout/PageLayout";
import ServiceProviderInfo from "../components/ServiceProviderInfo";
import ServiceVideoPlayer from "../components/ServiceVideoPlayer";
import ServiceTabs from "../components/ServiceTabs";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { fetchProviderApi, fetchProviderServicesApi } from "../../admin/api";
import { useQuery } from "@tanstack/react-query";

function ServiceDetail() {
  const { providerId } = useParams<{ providerId: string }>();
  const navigate = useNavigate();

  // Fetch provider data using useQuery
  const {
    data: providerData,
    isPending: providerLoading,
    error: providerError,
  } = useQuery({
    queryKey: ["provider", providerId],
    queryFn: () => fetchProviderApi(providerId!),
    enabled: !!providerId,
  });

  // Fetch provider services using useQuery
  const {
    data: servicesData,
    isPending: servicesLoading,
    error: servicesError,
  } = useQuery({
    queryKey: ["providerServices", providerId],
    queryFn: () => fetchProviderServicesApi(providerId!),
    enabled: !!providerId,
  });

  // Derived state
  const loading = providerLoading || servicesLoading;
  const error = providerError || servicesError;
  const provider = providerData || null;
  const services = servicesData?.services || [];

  const handleGoBack = () => {
    navigate("/services");
  };

  if (loading) {
    return (
      <PageLayout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading provider information...</div>
        </div>
      </PageLayout>
    );
  }

  if (error || !provider) {
    return (
      <PageLayout>
        <div className="flex flex-col justify-center items-center h-64 gap-4">
          <div className="text-red-500 text-lg">
            {error ? "Failed to load provider information. Please try again." : "Provider not found"}
          </div>
          <Button onClick={handleGoBack} variant="outline">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Services
          </Button>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <div className="w-11/12 mx-auto mt-5 md:mt-2">
        {/* Go Back Button */}
        <Button
          onClick={handleGoBack}
          variant="ghost"
          className="mb-2 p-0 h-auto hover:text-gray-900"
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          Go back
        </Button>

        {/* Main Content Section */}
        <div className="flex flex-col lg:flex-row gap-8 mb-12">
          {/* Left Side - Provider Information */}
          <div className="w-full lg:w-2/3">
            <ServiceProviderInfo provider={provider.provider} />
          </div>

          {/* Right Side - Introductory Video */}
          <div className="w-full lg:w-1/3">
            <ServiceVideoPlayer
              videoUrl={provider.provider.introductionVideo?.url}
              providerName={`${provider.provider.firstName} ${provider.provider.lastName}`}
            />
          </div>
        </div>

        {/* Tabs Section */}
        <ServiceTabs
          services={services}
          provider={provider.provider}
        />
      </div>
    </PageLayout>
  );
}

export default ServiceDetail;
