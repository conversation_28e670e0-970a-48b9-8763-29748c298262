import React from "react";
import DataTable from "@/components/ui/data-table/DataTable";
import { Column } from "@/components/ui/data-table/types";
import { OrderStatusType, OrderTableItems, OrderTableProps } from "../type";
import NextIcon from "@/assets/next.svg";
import { Link } from "react-router-dom";
import { dateFormatter } from "@/lib/utils";
import OrderStatusBadge from "@/components/ui/order-status-badge";

const OrderTable: React.FC<OrderTableProps> = ({
  data,
  currentPage,
  totalPages,
  onPageChange,
  loading,
}) => {
  const columns: Column<OrderTableItems>[] = [
    {
      header: "Order ID",
      accessorKey: "",
      cell: (order) => <span className="text-neutral-600">{order._id}</span>,
    },
    {
      header: "Product",
      accessorKey: "",
      cell: (order) => (
        <div className="flex items-center gap-2">
          <div>
            <img
              className="object-cover max-w-[80px] max-h-[80px]"
              src={order?.productImage || ""}
              alt={order?.productTitle}
            />
          </div>
          <div className="text-sm ">{order?.productTitle}</div>
        </div>
      ),
    },
    {
      header: "Qty",
      accessorKey: "quantity",
      cell: (order) => (
        <div>
          <span className="text-neutral-600">{order.quantity}</span>
        </div>
      ),
    },
    {
      header: "Amount",
      accessorKey: "price",
      cell: (order) => (
        <div>
          <span className="text-neutral-600">${order.amountPaid}</span>
        </div>
      ),
    },
    {
      header: "Ordered on",
      accessorKey: "",
      cell: (order) => (
        <div>
          <span className="text-neutral-600">
            {dateFormatter(order.createdAt)}
          </span>
        </div>
      ),
    },
    {
      header: "Ordered by",
      accessorKey: "",
      cell: (order) => (
        <div>
          <span className="text-neutral-600">{order.firstName}</span>
        </div>
      ),
    },
    {
      header: "Address",
      accessorKey: "",
      cell: (order) => (
        <div>
          <span className="text-neutral-300">{order.shippingAddress}</span>
        </div>
      ),
    },
    {
      header: "Status",
      accessorKey: "status",
      cell: (order) => (
        <div>
          <span className="text-neutral-600">
            <OrderStatusBadge
              className="text-xs"
              status={order.status as OrderStatusType}
            />
          </span>
        </div>
      ),
    },
    {
      header: "",
      accessorKey: "actions",
      className: "w-[50px]",
      cell: (order) => (
        <Link to={`/seller/orders/${order._id}`}>
          <img
            className="cursor-pointer"
            alt="go to next page"
            src={NextIcon}
          />
        </Link>
      ),
    },
  ];

  return (
    <div className="max-h-[90vh] overflow-y-scroll scrollbar-hide">
      <DataTable
        data={data}
        columns={columns}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
        loading={loading}
      />
    </div>
  );
};

export default OrderTable;
