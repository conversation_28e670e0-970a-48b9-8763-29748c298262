import React, { useEffect, useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { useQuestionnaireStore } from "@/store/questionaireStore";
import { formatQuestionAire, questionnaireData } from "../utils";
import { QuestionAnswer } from "../types";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import CheckedIcon from "@/assets/checked-icon.png";
import { useNavigate } from "react-router-dom";
import { useMutation } from "@tanstack/react-query";
import { submitQuestionaireApi } from "../api";
import { showToast } from "@/lib/toast";

function QuestionForm() {
  const navigate = useNavigate();
  const {
    currentPhaseIndex,
    currentQuestionIndex,
    answers,
    currentProgress,
    setAnswer,
    goToNextQuestion,
    updateCurrentProgess,
  } = useQuestionnaireStore();
  const currentPhase = questionnaireData[currentPhaseIndex];
  const question = currentPhase.questions[currentQuestionIndex];
  const key = `${currentPhaseIndex}-${currentQuestionIndex}`;
  const increment = Math.round(100 / currentPhase.questions.length);
  const [tempData, setTempData] = useState<QuestionAnswer>(null);
  const [description, setDescription] = useState<string>("");
  const [multiSelectDescription, setMultiSelectDescription] =
    useState<string>("");

  const { mutate, isPending: loading } = useMutation({
    mutationFn: submitQuestionaireApi,
    onSuccess: () => {
      localStorage.setItem("showCarePlan", "true");
      navigate("/");
    },
    onError: () => {
      showToast("failed to submit questionaire", "error");
    },
  });

  // form logic
  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (
      currentPhaseIndex === questionnaireData.length - 1 &&
      currentQuestionIndex === currentPhase.questions.length - 1
    ) {
      if (Array.isArray(tempData) && tempData.includes("No")) {
        navigate("/");
        return;
      }
      const result = formatQuestionAire(answers);
      mutate(result); // api call
      return;
    }
    if (tempData !== null && Array.isArray(tempData) && tempData.length > 0) {
      setAnswer(key, tempData);
      setTempData(null);
      setMultiSelectDescription("");
      setDescription("");
      goToNextQuestion(questionnaireData);
      const updatedProgress = currentProgress[currentPhaseIndex]
        ? currentProgress[currentPhaseIndex] + increment
        : increment;
      updateCurrentProgess(`${currentPhaseIndex}`, updatedProgress);
    } else if (answers[key]) {
      goToNextQuestion(questionnaireData);
    }
  };

  useEffect(() => {
    const currentAnswer = answers[key];

    // Reset description fields when changing questions
    setDescription("");
    setMultiSelectDescription("");

    // Initialize tempData with the existing answer when navigating to a question
    if (currentAnswer) {
      setTempData(currentAnswer);
    } else {
      setTempData(null);
    }

    if (question.type === "text_description" && currentAnswer) {
      // Extract the first item from the array for display in the text field
      if (Array.isArray(currentAnswer) && currentAnswer.length > 0) {
        setDescription(currentAnswer[0]);
      }
    }

    if (
      question.type === "multiselect_or_description" &&
      Array.isArray(currentAnswer) &&
      currentAnswer.length > 0
    ) {
      // Find any description values (those not in options)
      const descriptionValues = currentAnswer.filter(
        (item) => question.options && !question.options.includes(item)
      );

      // If there's a description value, set it in the description field
      if (descriptionValues.length > 0) {
        setMultiSelectDescription(descriptionValues[0]);
      }
    }
  }, [answers, key, question]);

  const handleSelect = (type: string, value: string, description?: boolean) => {
    // Don't reset the answer immediately - we'll update it when the form is submitted
    if (type === "text_description") {
      // Store text description as an array with a single value
      setTempData([value]);
      setDescription(value);
    } else if (type === "single_select") {
      // Store single select as an array with a single value
      setTempData([value]);
    } else if (type === "multiselect") {
      // For regular multiselect options
      setTempData((prev) => {
        if (prev === null) {
          return [value];
        } else if (Array.isArray(prev)) {
          if (prev.includes(value)) {
            // Remove the option if already selected
            return prev.filter((item) => item !== value);
          } else {
            // Add the option to existing selections
            return [...prev, value];
          }
        }
        return [value];
      });
    } else if (type === "multiselect_or_description") {
      if (description) {
        // Handle the description field
        setMultiSelectDescription(value);

        if (value.trim() === "") {
          // If description is empty, keep only the multiselect options
          setTempData((prev) => {
            if (prev === null) {
              return [];
            } else if (Array.isArray(prev)) {
              // Keep only options that are in the question options list
              return prev.filter((item) => question.options?.includes(item));
            }
            return [];
          });
        } else {
          // If there's a description, add it to the array or replace any existing description
          setTempData((prev) => {
            if (prev === null) {
              return [value];
            } else if (Array.isArray(prev)) {
              // First, remove any existing description (items not in options)
              const optionValues = prev.filter((item) =>
                question.options?.includes(item)
              );
              // Then add the new description
              return [...optionValues, value];
            }
            return [value];
          });
        }
      } else {
        // Handle multiselect options in multiselect_or_description
        setTempData((prev) => {
          if (prev === null) {
            return [value];
          } else if (Array.isArray(prev)) {
            // Find any description values (those not in options)
            const descriptionValues = prev.filter(
              (item) => !question.options?.includes(item)
            );

            if (prev.includes(value)) {
              // If option is already selected, remove it but keep description
              const filteredOptions = prev.filter((item) => item !== value);
              return filteredOptions;
            } else {
              // If option is not selected, add it and keep description
              return [
                ...descriptionValues,
                value,
                ...prev.filter(
                  (item) =>
                    item !== descriptionValues[0] && // Remove description from this list
                    question.options?.includes(item) && // Only include valid options
                    item !== value // Avoid duplicates
                ),
              ];
            }
          }
          return [value];
        });
      }
    }
  };
  // Unified styling function for all selection types
  const isOptionSelected = (opt: string) => {
    // First check tempData (current user selection)
    if (tempData !== null) {
      return Array.isArray(tempData) && tempData.includes(opt);
    }
    // Then check saved answers
    else if (answers[key]) {
      return Array.isArray(answers[key]) && answers[key].includes(opt);
    }
    return false;
  };

  // Use the unified function for all styling
  const styleSelectedMultiSelect = isOptionSelected;
  const styleMultiselectOrDescription = isOptionSelected;
  const styleSingleSelect = isOptionSelected;
  return (
    <form onSubmit={onSubmit} className="w-full space-y-7 ">
      <p className="hidden text-center text-neutral-100 md:block">{`${
        currentQuestionIndex + 1
      }/${currentPhase.questions.length}`}</p>
      <div>
        <h2 className="mx-5 text-lg text-center md:text-3xl text-wrap md:leading-7">
          {question.question}
        </h2>
        <p className="mt-2 text-center">
          {["multiselect", "multiselect_or_description"].includes(
            question.type
          ) && `(select all that apply)`}
        </p>
      </div>
      <div className="w-9/12 mx-auto">
        {question.type === "text_description" && (
          <Textarea
            onChange={(e) => handleSelect("text_description", e.target.value)}
            required={true}
            value={description}
            placeholder={question.placeholder}
            className="w-full rounded-lg min-h-40 md:min-h-72"
          />
        )}

        {question.type === "single_select" && (
          <div className="space-y-3">
            {question.options?.map((opt) => (
              <div
                key={opt}
                className={`${
                  styleSingleSelect(opt) &&
                  `border-orange-1 bg-blush flex justify-between`
                } flex items-center  gap-2 cursor-pointer  h-10 w-full rounded-md border border-input bg-background px-3 py-7 text-base shadow-none transition-colors placeholder:text-muted-foreground focus:outline-none focus:ring-0 focus:border-input`}
                onClick={() => handleSelect("single_select", opt)}
              >
                {opt}
                {styleSingleSelect(opt) && (
                  <div>
                    <img src={CheckedIcon} className="my-auto" />
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {question.type === "multiselect" && (
          <div className="space-y-3">
            <div className="space-y-3">
              {question.options?.map((opt) => (
                <Label
                  id="multi select"
                  key={opt}
                  className="flex flex-col gap-y-5 "
                >
                  <div
                    className={`${
                      styleSelectedMultiSelect(opt) &&
                      `border-orange-1 bg-blush`
                    } flex items-center gap-2 cursor-pointer  md:min-h-10   w-full rounded-md border border-input bg-background px-3 py-7 text-base shadow-none transition-colors placeholder:text-muted-foreground focus:outline-none focus:ring-0 focus:border-input`}
                  >
                    <Checkbox
                      id="multi select"
                      className="data-[state=checked]:bg-orange-1 data-[state=checked]:text-white data-[state=checked]:border-0"
                      checked={styleSelectedMultiSelect(opt)}
                      onClick={() => handleSelect("multiselect", opt)}
                    />
                    <span className="">{opt}</span>
                  </div>
                </Label>
              ))}
            </div>
          </div>
        )}
        {question.type === "multiselect_or_description" && (
          <div className="space-y-3">
            <div className="space-y-3">
              {question.options?.map((opt) => (
                <Label
                  id="multiselect_or_description"
                  key={opt}
                  className="flex flex-col gap-y-5"
                >
                  <div
                    className={`${
                      styleMultiselectOrDescription(opt) &&
                      `border-orange-1 bg-blush`
                    } flex items-center gap-2 cursor-pointer  md:min-h-10   w-full rounded-md border border-input bg-background px-3 py-7 text-base shadow-none transition-colors placeholder:text-muted-foreground focus:outline-none focus:ring-0 focus:border-input`}
                  >
                    <Checkbox
                      className="data-[state=checked]:bg-orange-1 data-[state=checked]:text-white data-[state=checked]:border-0"
                      id="multiselect_or_description"
                      checked={styleMultiselectOrDescription(opt)}
                      onClick={() =>
                        handleSelect("multiselect_or_description", opt)
                      }
                    />
                    <span>{opt}</span>
                  </div>
                </Label>
              ))}
              <Textarea
                onChange={(e) =>
                  handleSelect(
                    "multiselect_or_description",
                    e.target.value,
                    true
                  )
                }
                value={multiSelectDescription}
                placeholder={question.placeholder || "others"}
                className="w-full rounded-lg"
              />
            </div>
          </div>
        )}
      </div>

      <div className="flex justify-end w-9/12 mx-auto">
        <Button disabled={loading} className="px-8 md:px-12" type="submit">
          {loading ? "Submitting..." : "Next"}
        </Button>
      </div>
    </form>
  );
}

export default QuestionForm;
