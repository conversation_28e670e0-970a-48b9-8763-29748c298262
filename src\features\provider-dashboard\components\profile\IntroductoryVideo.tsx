import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useState } from "react";
import VideoUploadDialog from "./VideoUploadDialog";

interface IntroductoryVideoProps {
  videoUrl?: string;
  videoKey?: string;
  onVideoUpload?: (file: File, existingVideoKey?: string) => void;
  isLoading?: boolean;
}

function IntroductoryVideo({
  videoUrl,
  videoKey,
  onVideoUpload,
  isLoading = false
}: IntroductoryVideoProps) {
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);

  const handleVideoUpload = (file: File) => {
    if (onVideoUpload) {
      // Pass the existing video key if there's a video to replace
      onVideoUpload(file, videoKey);
    }
    setIsUploadDialogOpen(false);
  };

  return (
    <div className="mb-8">
      <h2 className="text-base mb-4">Introductory video</h2>

      {videoUrl ? (
        <div className="relative rounded-lg overflow-hidden aspect-video max-w-2xl">
          <video
            src={videoUrl}
            controls
            className="w-full h-full object-cover"
          />
          <div className="absolute top-4 right-4">
            <Button
              variant="outline"
              size="sm"
              className="rounded-full bg-white"
              onClick={() => setIsUploadDialogOpen(true)}
              disabled={isLoading}
              title="Replace video"
            >
              <Plus className="w-4 h-4" />
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-8 max-w-2xl aspect-video">
          <div className="text-center">
            <p className="text-gray-500 mb-4">No introductory video uploaded yet.</p>
            <Button
              variant="outline"
              onClick={() => setIsUploadDialogOpen(true)}
              className="flex items-center gap-2"
              disabled={isLoading}
            >
              <Plus className="w-4 h-4" />
              Add video
            </Button>
          </div>
        </div>
      )}

      {/* Video Upload Dialog */}
      <VideoUploadDialog
        open={isUploadDialogOpen}
        onOpenChange={setIsUploadDialogOpen}
        onSubmit={handleVideoUpload}
        isLoading={isLoading}
        isReplacing={!!videoUrl}
      />
    </div>
  );
}

export default IntroductoryVideo;
