import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Star, MessageCircle, Phone } from "lucide-react";
import { Provider } from "../../admin/type";
import ScheduleCallDialog from "./ScheduleCallDialog";
import { useNavigate } from "react-router-dom";

interface ServiceProviderInfoProps {
  provider: Provider;
}

function ServiceProviderInfo({ provider }: ServiceProviderInfoProps) {
  const navigate = useNavigate();
  const [scheduleDialogOpen, setScheduleDialogOpen] = useState(false);

  const fullName = `${provider.firstName} ${provider.lastName}`;
  const profileImage =
    provider.profilePicture?.[0]?.url || "/default-avatar.png";
  const serviceLocation =
    provider.serviceLocation?.join(", ") || "Not specified";
  const experienceText = `${provider.experience} yrs exp`;

  const handleSendMessage = () => {
    const navigationState = {
      receiverId: provider._id,
      name: provider.firstName,
      description: provider.specialty,
      profilePicture: provider.profilePicture?.[0],
      role: "provider",
    };
    navigate("/chat", {
      state: navigationState,
    });
  };

  const handleScheduleCall = () => {
    setScheduleDialogOpen(true);
  };

  const handleProceedToPayment = (
    selectedDate: Date,
    selectedTimeSlot: string
  ) => {
    console.log("Proceeding to payment with:", {
      providerId: provider._id,
      providerName: fullName,
      selectedDate,
      selectedTimeSlot,
      price: 80, // Using hardcoded price for now as shown in the design
    });
    // TODO: Implement payment flow
    setScheduleDialogOpen(false);
  };

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <div className="flex items-start gap-4">
        <div className="w-[72px] h-[72px] rounded-full overflow-hidden flex-shrink-0 mt-3">
          <img
            src={profileImage}
            alt={fullName}
            className="object-cover w-full h-full"
          />
        </div>
        <div className="flex-1">
          <div className="flex items-baseline justify-start">
            <h1 className="text-2xl font-medium">{fullName}</h1>
            <div className="flex items-center gap-1 mt-1 ml-2">
              <Star className="w-4 h-4 fill-orange-1 text-orange-1" />
              <span className="text-base text-gray-600">4.6</span>
            </div>
          </div>
          {provider.specialty && (
            <div>
              <p className="text-neutral-600">
                Specialist in {provider.specialty}
              </p>
            </div>
          )}

          <div className="flex flex-wrap gap-4 mt-2 text-base text-neutral-600">
            <div className="flex items-center gap-2">
              <img
                src="/src/assets/location.png"
                alt="Location Icon"
                className="w-5 h-5"
              />
              <span>{serviceLocation}</span>
            </div>
            <div className="flex items-center gap-2">
              <img
                src="/src/assets/calender.png"
                alt="Calendar Icon"
                className="w-5 h-5"
              />
              <span>{experienceText}</span>
            </div>
          </div>

          <div className="flex flex-col gap-3 mt-4 md:flex-row ">
            <Button
              onClick={handleSendMessage}
              variant="outline"
              className="flex items-center gap-2 flex-1 max-w-[174px]"
            >
              <MessageCircle className="w-4 h-4" />
              Send message
            </Button>
            <Button
              onClick={handleScheduleCall}
              className="flex items-center gap-2 flex-1 bg-orange-1 hover:bg-orange-1/90  max-w-[174px]"
            >
              <Phone className="w-4 h-4" />
              Schedule a call
            </Button>
          </div>
        </div>
      </div>

      {/* Specialty */}

      {/* Service Location and Experience */}

      {/* Action Buttons */}

      {/* Description */}
      {provider.description && (
        <div>
          <p className="leading-relaxed text-gray-700">
            {provider.description}
          </p>
        </div>
      )}

      {/* Schedule Call Dialog */}
      <ScheduleCallDialog
        open={scheduleDialogOpen}
        onOpenChange={setScheduleDialogOpen}
        providerName={fullName}
        servicePrice={80} // Using hardcoded price as shown in design
        onProceedToPayment={handleProceedToPayment}
      />
    </div>
  );
}

export default ServiceProviderInfo;
