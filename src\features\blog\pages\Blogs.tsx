import PageLayout from "@/components/layout/PageLayout";
import { useQuery } from "@tanstack/react-query";
import { fetchBlogCountApi, fetchBlogsApi } from "../api";
import BlogCoverImage from "@/assets/blog-cover.png";
import BlogCoverMobileImage from "@/assets/mobile-blog-cover.png";
import { urlFor } from "@/lib/sanity-client";
import BlogCard from "../components/BlogCard";
import { Blog } from "../type";
import BlogCardSkeleton from "../components/BlogCardSkelton";
import DynamicPagination from "@/components/ui/dynamic-pagination";
import { useEffect, useState } from "react";

function Blogs() {
  const [lastIds, setLastIds] = useState<Record<number, string>>({});
  const [currentPage, setcurrentPage] = useState(1);
  const { data, isLoading, isSuccess } = useQuery({
    queryKey: ["blogs", currentPage],
    queryFn: () => fetchBlogsApi(lastIds[currentPage] || null),
  });
  const { data: totalCount } = useQuery({
    queryKey: ["blogs-count"],
    queryFn: fetchBlogCountApi,
  });
  useEffect(() => {
    if (data?.length) {
      const lastId = data[data.length - 1]._id;
      setLastIds({
        ...lastIds,
        [currentPage + 1]: lastId,
      });
    }
  }, [currentPage]);

  const handlePageChange = (page: number) => {
    setcurrentPage(page);
  };

  return (
    <PageLayout>
      <div>
        <img
          src={BlogCoverImage}
          alt="Blog Cover"
          className="hidden md:block"
        />
        <img
          src={BlogCoverMobileImage}
          alt="Blog Cover"
          className="block w-full md:hidden"
        />
        <div className="w-11/12 mx-auto mt-10">
          <h1 className="text-2xl ">Resources</h1>
          <h5 className="mt-1 text-neutral-300">
            Read about postpartum care and many more.
          </h5>
          <div className="grid grid-cols-12 gap-8 mt-10">
            {isLoading ? (
              [1, 2, 3, 4, 5, 6].map((_, i) => (
                <div
                  key={i}
                  className="col-span-12 md:col-span-6 lg:col-span-4 "
                >
                  <BlogCardSkeleton />
                </div>
              ))
            ) : !data?.length ? (
              <div className="col-span-12 text-center">No resources found</div>
            ) : (
              isSuccess &&
              data?.map((blog: Blog) => {
                return (
                  <div
                    key={blog?._id}
                    className="col-span-12 md:col-span-6 lg:col-span-4 "
                  >
                    <BlogCard
                      coverImage={urlFor(blog?.coverImage)}
                      title={blog?.title}
                      redirectTo={`/resources/${blog?.slug?.current}`}
                      publishedAt={blog?.publishedAt}
                    />
                  </div>
                );
              })
            )}
          </div>
          {data?.length > 0 && (
            <div className="mt-1">
              <DynamicPagination
                currentPage={currentPage}
                totalPages={totalCount || 1}
                onPageChange={handlePageChange}
                restrictSkip={true}
              />
            </div>
          )}
        </div>
      </div>
    </PageLayout>
  );
}

export default Blogs;
