import { Route, Routes } from "react-router-dom";
import Login from "@/features/auth/pages/Login";
import Home from "@/features/home/<USER>/Home";
import ProtectedRoutes from "./ProtectedRoutes";
import Register from "@/features/auth/pages/Register";
import ForgotPassword from "@/features/auth/pages/ForgotPassword";
import CheckEmail from "@/features/auth/pages/CheckEmail";
import ResetPassoword from "@/features/auth/pages/ResetPassoword";
import Otp from "@/features/auth/pages/Otp";
import SetupProfile from "@/features/auth/pages/ProfileSetup";
import Questionaire from "@/features/questionaire/pages/Questionaire";
import ProductDetail from "@/features/products/pages/ProductDetail";
import CartPage from "@/features/cart/pages/CartPage";
import Shop from "@/features/products/pages/Shop";
import SellerDetail from "@/features/products/pages/SellerDetail";
import OrderSuccessPage from "@/features/orders/pages/OrderSuccessPage";
import OrderFailurePage from "@/features/orders/pages/OrderFailurePage";
import Account from "@/features/account/pages/Account";
import BasicDetails from "@/features/account/pages/BasicDetails";
import MyOrder from "@/features/account/pages/MyOrder";
import OrderedProduct from "@/features/account/pages/OrderedProduct";
import BookedServices from "@/features/account/pages/BookedServices";
import CarePlan from "@/features/account/pages/CarePlan";
import ScheduledCalls from "@/features/account/pages/ScheduledCalls";
import Services from "@/features/services/pages/Services";
import ServiceDetail from "@/features/services/pages/ServiceDetail";
import ServiceBooking from "@/features/services/pages/ServiceBooking";
import NotFound from "@/components/ui/not-found";
import CustomerChatPage from "@/features/chat/pages/CustomerChatPage";
import Blogs from "@/features/blog/pages/Blogs";
import BlogDetails from "@/features/blog/pages/BlogDetails";
function UserRoute() {
  return (
    <Routes>
      <Route index element={<Home />} />
      <Route path={"/login"} element={<Login />} />
      <Route path={"/register"} element={<Register />} />
      <Route path={"/forgot-password"} element={<ForgotPassword />} />
      <Route path={"/forgot-password/check-email"} element={<CheckEmail />} />
      <Route path={"/reset-password"} element={<ResetPassoword />} />
      <Route path={"/verify-otp"} element={<Otp />} />
      <Route path={"/setup-profile"} element={<SetupProfile />} />
      <Route path={"/product/:id"} element={<ProductDetail />} />
      <Route path={"/shop"} element={<Shop />} />
      <Route path={"/services"} element={<Services />} />
      <Route path={"/services/:providerId"} element={<ServiceDetail />} />
      <Route path={"/services/booking/:serviceId"} element={<ServiceBooking />} />
      <Route path={"/product/seller/:id"} element={<SellerDetail />} />
      <Route path={"/resources"} element={<Blogs />} />
      <Route path={"/resources/:slug"} element={<BlogDetails />} />
      {/* Protected Routes */}
      <Route element={<ProtectedRoutes role="customer" />}>
        <Route path="/chat" element={<CustomerChatPage />} />
        <Route path="/questionaire" element={<Questionaire />} />
        <Route path="/cart" element={<CartPage />} />
        <Route path="/order/success" element={<OrderSuccessPage />} />
        <Route path="/order/failure" element={<OrderFailurePage />} />
        <Route path="/account" element={<Account />} />
        <Route path="/account/basic-details" element={<BasicDetails />} />
        <Route path="/account/orders" element={<MyOrder />} />
        <Route path="/account/orders/:id" element={<OrderedProduct />} />
        <Route path="/account/services" element={<BookedServices />} />
        <Route path="/account/care-plan" element={<CarePlan />} />
        <Route path="/account/scheduled-calls" element={<ScheduledCalls />} />
      </Route>
      {/* 404 */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default UserRoute;
