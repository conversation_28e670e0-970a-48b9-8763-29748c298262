import SellerDashboard<PERSON>ogo from "@/assets/seller-dashboard.png";
import OrderLogo from "@/assets/order.png";
import Message<PERSON>ogo from "@/assets/message.png";
import ReviewLogo from "@/assets/review.png";
import { Link, useLocation } from "react-router-dom";
function Footer() {
  const footerItems = [
    { icon: SellerDashboardLogo, name: "Dashboard", href: "/seller/dashboard" },
    { icon: OrderLogo, name: "Orders", href: "/seller/orders" },
    { icon: MessageLogo, name: "Messages", href: "/seller/messages" },
    { icon: ReviewLogo, name: "Reviews", href: "/seller/reviews" },
  ];
  const { pathname } = useLocation();
  const key = pathname.split("/")[2];
  return (
    <div className="flex justify-between px-2 border-t-2 border-gray-2">
      {footerItems.map((item) => (
        <Link
          to={item.href}
          key={item.name}
          className={`${
            key === item.name.toLocaleLowerCase()
              ? "bg-tints-60"
              : "bg-transparent"
          } flex flex-col items-center p-3 justify-center my-2 rounded-md gap-3 `}
        >
          <img className="my-auto" src={item.icon} alt={item.name} />
          <span
            className={`${
              key === item.name.toLocaleLowerCase()
                ? "font-semibold"
                : "font-normal"
            } text-sm`}
          >
            {item.name}
          </span>
        </Link>
      ))}
    </div>
  );
}

export default Footer;
