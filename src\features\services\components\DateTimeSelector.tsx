import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import { ChevronLeft, ChevronRight } from "lucide-react";

type DateTimeSelectorProps = {
  selectedDate: Date | undefined;
  selectedTimeSlot: string;
  onDateSelect: (date: Date | undefined) => void;
  onTimeSlotSelect: (timeSlot: string) => void;
  servicePrice: number;
  onProceedToPayment: () => void;
  showPromoCode?: boolean;
  promoCode?: string;
  onPromoCodeChange?: (code: string) => void;
  onApplyPromo?: () => void;
  isDialog?: boolean; // New prop to handle dialog styling
};

// Mock time slots data for services (longer durations)
const serviceTimeSlots = [
  "09:00 am - 10:00 am",
  "10:00 am - 11:00 am",
  "11:00 am - 12:00 pm",
  "12:00 pm - 1:00 pm",
  "1:00 pm - 2:00 pm",
  "2:00 pm - 3:00 pm",
];

// Mock time slots data for calls (shorter durations)
const callTimeSlots = [
  "09:00 am - 9:15 am",
  "9:15 am - 9:30 am",
  "9:30 am - 9:45 am",
  "9:45 am - 10:00 am",
  "10:00 am - 10:15 am",
  "10:15 am - 10:30 am",
  "10:30 am - 10:45 am",
  "10:45 am - 11:00 am",
  "11:00 am - 11:15 am",
  "11:15 am - 11:30 am",
];

export default function DateTimeSelector({
  selectedDate,
  selectedTimeSlot,
  onDateSelect,
  onTimeSlotSelect,
  servicePrice,
  onProceedToPayment,
  showPromoCode = false,
  promoCode = "",
  onPromoCodeChange,
  onApplyPromo,
  isDialog = false,
}: DateTimeSelectorProps) {
  // Disable past dates
  const disabledDays = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  const isButtonEnabled = selectedDate && selectedTimeSlot;
  const timeSlots = isDialog ? callTimeSlots : serviceTimeSlots;

  return (
    <div className={isDialog ? "" : "border rounded-lg p-6"}>
      {!isDialog && (
        <>
          <h2 className="text-xl font-semibold mb-4">Select slot</h2>
          <p className="text-gray-600 mb-6">Please pick a date and time for this service</p>
        </>
      )}

      {/* Main Content */}
      <div className={`flex gap-6 mb-6 ${isDialog ? "min-h-0" : ""}`}>
        {/* Date Section */}
        <div className="flex-1">
          <h3 className="text-lg font-medium mb-4">Date</h3>
          <div className="border rounded-lg p-4">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={onDateSelect}
              disabled={disabledDays}
              className="w-full"
              classNames={{
                months: "flex w-full flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
                month: "space-y-4 w-full",
                caption: "flex justify-center pt-1 relative items-center",
                caption_label: "text-sm font-medium",
                nav: "space-x-1 flex items-center",
                nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100",
                nav_button_previous: "absolute left-1",
                nav_button_next: "absolute right-1",
                table: "w-full border-collapse space-y-1",
                head_row: "flex w-full",
                head_cell: "text-gray-500 rounded-md w-8 font-normal text-[0.8rem] flex-1 text-center",
                row: "flex w-full mt-2",
                cell: "text-center text-sm p-0 relative flex-1  first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
                day: "h-8 w-8 p-0 font-normal aria-selected:opacity-100 hover:bg-gray-100 rounded-md mx-auto",
                day_selected: "bg-[#F1D5CD] text-black focus:bg-[#F1D5CD] border-solid border-[#E3AC85]",
                day_today: "bg-gray-100 text-gray-900",
                day_outside: "text-gray-400 opacity-50",
                day_disabled: "text-gray-400 opacity-50 cursor-not-allowed",
                day_range_middle: "aria-selected:bg-orange-100 aria-selected:text-gray-900",
                day_hidden: "invisible",
              }}
              components={{
                IconLeft: ({ ...props }) => <ChevronLeft className="h-4 w-4" {...props} />,
                IconRight: ({ ...props }) => <ChevronRight className="h-4 w-4" {...props} />,
              }}
            />
          </div>
        </div>

        {/* Time Slot Section */}
        <div className="flex-1">
          <h3 className="text-lg font-medium mb-4">Time slot</h3>
          <div className="space-y-2 max-h-[300px] overflow-y-auto">
            {timeSlots.map((timeSlot, index) => (
              <button
                key={index}
                onClick={() => onTimeSlotSelect(timeSlot)}
                disabled={!selectedDate}
                className={`w-full p-3 text-left rounded-lg border transition-colors ${
                  selectedTimeSlot === timeSlot
                    ? "bg-[#F1D5CD] border-[#E3AC85] text-black"
                    : selectedDate
                    ? "border-gray-200 hover:bg-gray-50"
                    : "border-gray-200 text-gray-400 cursor-not-allowed"
                }`}
              >
                {timeSlot}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Promo Code Section - Only show if enabled */}
      {showPromoCode && (
        <div className="mb-6">
          <h3 className="font-medium mb-3">Promo code</h3>
          <div className="flex gap-2">
            <Input
              placeholder="Enter code"
              value={promoCode}
              onChange={(e) => onPromoCodeChange?.(e.target.value)}
              className="flex-1"
            />
            <Button
              onClick={onApplyPromo}
              variant="ghost"
              className="text-orange-1  hover:bg-orange-50"
            >
              APPLY
            </Button>
          </div>
        </div>
      )}

      {/* Session Info */}
      <div className="text-sm text-gray-600 mb-4">
        <p>The session will be conducted only when the provider has accepted your request. If they reject it, full amount will be refunded.</p>
        <p className="mt-2">
          Please read the{" "}
          <button className="text-orange-600 underline hover:text-orange-700">
            refund policy
          </button>
        </p>
      </div>

      {/* Price and Payment */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div className="text-2xl font-bold">
          ${servicePrice}
        </div>
        <Button
          onClick={onProceedToPayment}
          disabled={!isButtonEnabled}
          className="text-white px-8 py-2 rounded-full disabled:bg-gray-300 disabled:cursor-not-allowed"
        >
          Proceed to payment
        </Button>
      </div>
    </div>
  );
}
