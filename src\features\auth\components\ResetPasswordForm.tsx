import FingerPrintImage from "@/assets/fingerprint.png";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { LuEye } from "react-icons/lu";
import { PiEyeSlash } from "react-icons/pi";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

import { resetPasswordPayload } from "../type";
import { resetPasswordApi } from "../api";
import { resetPasswordSchema } from "../validation";
import { useNavigate, useSearchParams } from "react-router-dom";
import { showToast } from "@/lib/toast";
function ResetPasswordForm() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const token = searchParams.get("token");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<resetPasswordPayload>({
    resolver: joiResolver(resetPasswordSchema),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: resetPasswordApi,
    onSuccess: () => {
      alert("Password reset successfully! Redirecting to login...");
      navigate("/login", { replace: true });
    },
    onError: (error: AxiosError) => {
      console.error(error.response?.data);
      showToast("Something went wrong", "error");
    },
  });

  const onSubmit = (data: resetPasswordPayload) => {
    mutate({ ...data, token: token as string });
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="w-full max-w-md text-center"
    >
      <div className="flex flex-col md:justify-center md:items-center p-4">
        <div className="w-full max-w-md bg-white rounded-lg p-6">
          <div className="flex flex-col items-center">
            <img
              src={FingerPrintImage}
              alt="Fingerprint"
              className="mx-auto object-cover"
            />

            {/* Header */}
            <div className="w-full flex items-center my-6">
              <h1 className="text-3xl mx-auto font-medium text-center font-prettywise flex-grow">
                Create a New Password
              </h1>
            </div>

            <p className="text-gray-500 text-center mb-6">
              Choose a strong password to keep <br /> your account secure.
            </p>

            <div className="w-full flex flex-col gap-y-4 mb-4">
              {/* Password Input */}
              <div>
                <div className="border rounded-sm border-input-border focus:border-slate-300 flex">
                  <Input
                    className="border-none"
                    placeholder="Enter password"
                    type={showPassword ? "text" : "password"}
                    {...register("newPassword")}
                  />
                  {showPassword ? (
                    <PiEyeSlash
                      onClick={() => setShowPassword(false)}
                      className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                      size={25}
                    />
                  ) : (
                    <LuEye
                      onClick={() => setShowPassword(true)}
                      className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                      size={25}
                    />
                  )}
                </div>
                {errors.newPassword && (
                  <p className="text-red-500 text-start text-sm">
                    {errors.newPassword.message}
                  </p>
                )}
              </div>

              {/* Confirm Password Input */}
              <div>
                <div className="border rounded-sm border-input-border focus:border-slate-300 flex">
                  <Input
                    className="border-none"
                    placeholder="Re-enter password"
                    type={showConfirmPassword ? "text" : "password"}
                    {...register("confirmPassword")}
                  />
                  {showConfirmPassword ? (
                    <PiEyeSlash
                      onClick={() => setShowConfirmPassword(false)}
                      className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                      size={25}
                    />
                  ) : (
                    <LuEye
                      onClick={() => setShowConfirmPassword(true)}
                      className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                      size={25}
                    />
                  )}
                </div>
                {errors.confirmPassword && (
                  <p className="text-red-500 text-start text-sm">
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>
            </div>

            <Button type="submit" className="w-full mt-3" disabled={isPending}>
              {isPending ? "Saving..." : "Save & Proceed to login"}
            </Button>
          </div>
        </div>
      </div>
    </form>
  );
}

export default ResetPasswordForm;
