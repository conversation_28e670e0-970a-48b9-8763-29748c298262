import { useState } from "react";
import { Dialog, DialogContent, DialogTitle, DialogClose } from "@/components/ui/dialog";
import { X } from "lucide-react";
import DateTimeSelector from "./DateTimeSelector";

type ScheduleCallDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  providerName: string;
  servicePrice: number;
  onProceedToPayment: (selectedDate: Date, selectedTimeSlot: string) => void;
};

export default function ScheduleCallDialog({
  open,
  onOpenChange,
  providerName,
  servicePrice,
  onProceedToPayment,
}: ScheduleCallDialogProps) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<string>("");

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
    // Reset time slot when date changes
    setSelectedTimeSlot("");
  };

  const handleTimeSlotSelect = (timeSlot: string) => {
    setSelectedTimeSlot(timeSlot);
  };

  const handleProceedToPayment = () => {
    if (selectedDate && selectedTimeSlot) {
      onProceedToPayment(selectedDate, selectedTimeSlot);
    }
  };

  const handleDialogChange = (isOpen: boolean) => {
    if (!isOpen) {
      // Reset selections when dialog closes
      setSelectedDate(undefined);
      setSelectedTimeSlot("");
    }
    onOpenChange(isOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogChange}>
      <DialogContent className="max-w-[600px] h-[625px] p-0 gap-0 overflow-auto">
        <div className="p-6 h-full flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <DialogTitle className="text-lg font-semibold mb-2">
                Schedule a 15 minute call
              </DialogTitle>
              <p className="text-gray-600 text-base">
                Before you book a service with {providerName}, you can take a 15 minute call with them.
              </p>
            </div>
            <DialogClose className="rounded-full h-6 w-6 flex items-center justify-center">
              <X className="h-4 w-4" />
            </DialogClose>
          </div>

          {/* DateTimeSelector Component */}
          <div className="flex-1">
            <DateTimeSelector
              selectedDate={selectedDate}
              selectedTimeSlot={selectedTimeSlot}
              onDateSelect={handleDateSelect}
              onTimeSlotSelect={handleTimeSlotSelect}
              servicePrice={servicePrice}
              onProceedToPayment={handleProceedToPayment}
              showPromoCode={false}
              isDialog={true}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
