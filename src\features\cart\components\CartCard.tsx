import { useEffect, useState } from "react";
import { CartItem } from "../type";
import { Link } from "react-router-dom";
import { updateCartApi } from "../api";
import { useMutation } from "@tanstack/react-query";
import { showToast } from "@/lib/toast";
import { useCartStore } from "@/store/cartStore";
import { AxiosError } from "axios";

function CartCard({
  _id,
  image,
  title,
  price,
  currentQuantity,
  productId,
  handleRefetch,
}: CartItem & { handleRefetch: () => void }) {
  const { fetchCartCount } = useCartStore();
  const [quantity, setQuantity] = useState(currentQuantity);

  useEffect(() => {
    setQuantity(currentQuantity);
  }, [currentQuantity]);
  const decreaseQuantity = () => {
    if (quantity < 1) return;
    mutate({ productId, count: 1, type: "dec" });
    setQuantity(quantity - 1);
  };

  const increaseQuantity = () => {
    if (quantity >= 10) return;
    mutate({ productId, count: 1, type: "inc" });
    setQuantity(quantity + 1);
  };
  const { mutate, isPending: loading } = useMutation({
    mutationFn: updateCartApi,
    onSuccess: () => {
      showToast("Cart updated successfully", "success");
      handleRefetch();
      fetchCartCount();
    },
    onError: (error: AxiosError<Array<{ message?: string }>>) => {
      if (error.response?.status === 400) {
        showToast(
          error.response?.data[0]?.message || "Something went wrong",
          "error"
        );
        setQuantity(currentQuantity);
        return;
      }
    },
  });
  return (
    <div
      key={_id}
      className="flex w-full p-5 mb-1 bg-white border rounded-lg gap-x-5 border-gray-2"
    >
      <div className="flex justify-center">
        <Link to={`/product/${productId}`}>
          <img src={image.image} alt={title} className="w-20 h-20 my-auto" />
        </Link>
      </div>
      <div className="flex flex-col justify-between w-3/4 gap-y-5 grow">
        <div>
          <h2 className="text-lg">{title}</h2>
        </div>
        <div className="flex items-center">
          <button
            disabled={loading}
            onClick={decreaseQuantity}
            className="flex items-center justify-center border border-r-0 border-gray-300 disabled:bg-gray-300 w-9 h-9 rounded-l-md"
            aria-label="Decrease quantity"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M5 12h14" />
            </svg>
          </button>
          <input
            type="text"
            value={quantity}
            disabled={loading}
            readOnly
            aria-label="Quantity"
            className="w-10 text-center border-t border-b border-gray-300 h-9"
          />
          <button
            disabled={loading}
            onClick={increaseQuantity}
            className="flex items-center justify-center border border-l-0 border-gray-300 disabled:bg-gray-300 w-9 h-9 rounded-r-md"
            aria-label="Increase quantity"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M12 5v14M5 12h14" />
            </svg>
          </button>
        </div>
      </div>
      <div className="flex justify-center">
        <p className="my-auto text-2xl font-bold">${price}</p>
      </div>
    </div>
  );
}

export default CartCard;
