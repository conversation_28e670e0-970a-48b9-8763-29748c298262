import { Separator } from "@/components/ui/separator";

function Summary({
  subTotal,
  amountPaid,
  discount,
}: {
  subTotal: number;
  amountPaid: number;
  discount?: number;
}) {
  return (
    <div className="p-5 border rounded-lg border-gray-2">
      <h1 className="font-bold">Order Summary</h1>
      <div className="flex justify-between mt-4">
        <h5 className="text-neutral-600">Subtotal</h5>
        <h5 className="text-neutral-600">${subTotal}</h5>
      </div>
      {discount ? (
        <div className="flex justify-between mt-4">
          <h5 className="font-bold text-neutral-600">Discount</h5>
          <h5 className="font-bold text-neutral-600">${discount}</h5>
        </div>
      ) : null}
      <Separator className="my-5" />
      <div className="flex justify-between mt-4">
        <h5 className="text-lg">Amount paid</h5>
        <h5 className="font-bold">${amountPaid}</h5>
      </div>
    </div>
  );
}

export default Summary;
