import ProductCard from "@/components/ui/product-card";
import { fetchProductssApi } from "@/features/home/<USER>";
import { useQuery } from "@tanstack/react-query";
import { Product } from "../type";
import { useRef } from "react";

function ExploreMore() {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const { data, isSuccess } = useQuery({
    queryKey: ["products"],
    queryFn: () => fetchProductssApi({}),
  });

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -300, behavior: "smooth" });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 300, behavior: "smooth" });
    }
  };
  return !isSuccess || data?.data?.products?.length === 0 ? (
    <div className="mt-20 md:mt-28"></div>
  ) : (
    <div className="mt-20 md:mt-28">
      <h3 className="mb-4 text-lg">Explore more</h3>
      <div className="relative">
        {/* Left Arrow */}
        <button
          onClick={scrollLeft}
          className="absolute left-0 z-10 p-2 transition-all -translate-y-1/2 rounded-full shadow-md cursor-pointer top-1/2 bg-white/80 hover:bg-white hover:shadow-lg"
          aria-label="Scroll left"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-orange-1"
          >
            <path d="M15 18l-6-6 6-6" />
          </svg>
        </button>

        {/* Scrollable Container */}
        <div
          ref={scrollContainerRef}
          className="overflow-x-auto scrollbar-hide"
        >
          <div className="flex space-x-4">
            {isSuccess &&
              data.data?.products?.map((product: Product) => (
                <div
                  key={product._id}
                  className="min-w-[300px] border p-4 rounded-lg border-neutral-40 max-w-[300px] md:max-w-[300px] flex-shrink-0"
                >
                  <ProductCard
                    id={product._id}
                    image={product.images[0]}
                    title={product.title}
                    brand={product.seller.companyName}
                    price={product.price}
                    rating={product.totalRating}
                  />
                </div>
              ))}
          </div>
        </div>

        {/* Right Arrow */}
        <button
          onClick={scrollRight}
          className="absolute right-0 z-10 p-2 transition-all -translate-y-1/2 rounded-full shadow-md cursor-pointer top-1/2 bg-white/80 hover:bg-white hover:shadow-lg"
          aria-label="Scroll right"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-orange-1"
          >
            <path d="M9 18l6-6-6-6" />
          </svg>
        </button>
      </div>
    </div>
  );
}

export default ExploreMore;
