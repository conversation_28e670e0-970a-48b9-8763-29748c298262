import Joi from "joi";

export const basicDetailsSchema = Joi.object({
  firstName: Joi.string().required().min(3).max(26).trim().messages({
    "any.required": "First name is a required field",
    "string.base": "First name must be a string",
    "string.min": "First name atleast need 3 characters",
    "string.max": "First name cannot exceed 26 characters",
    "string.empty": "First name cannot be empty",
  }),

  phone: Joi.string()
    .regex(/^[0-9]{10}$/)
    .required()
    .messages({
      "any.required": "Phone number is required",
      "string.empty": "Phone number cannot be empty",
      "string.pattern.base": "Phone number must have 10 digits",
    }),
  lastName: Joi.string().required().min(3).max(26).trim().messages({
    "any.required": "Last name is a required field",
    "string.base": "Last name must be a string",
    "string.min": "Last name atleast need 3 characters",
    "string.max": "Last name cannot exceed 26 characters",
    "string.empty": "Last name cannot be empty",
  }),

  birthStatus: Joi.string()
    .required()
    .valid("currently pregnant", "postpartum", "loss history")
    .messages({
      "any.only":
        "Birth status must be one of: Currently pregnant, Postpartum, or Loss history",
      "any.required": "Birth status is a required field",
      "string.base": "Birth status must be a string",
    }),

  preferredLanguage: Joi.string().required().valid("english").messages({
    "any.only": "Prefferred Language must be one of: English",
    "any.required": "Preferred Language is a required field",
    "string.base": "Preferred Language must be a string",
  }),
});
