import { Product } from "../type";

function ProductDetailCard({ product }: { product: Product }) {
  return (
    <div className="flex flex-col justify-between p-3 border rounded-lg cursor-pointer gap-y-2 border-tints-50">
      <div className="flex justify-between">
        <div className="flex gap-x-2">
          <img
            src={product.images[0].image}
            alt="product"
            className="w-12 h-12"
          />
          <div>
            <h1 className="font-semibold ">{product.title}</h1>
            <p className=" text-neutral-300">quantity: {product.quantity}</p>
          </div>
        </div>
        <div className="my-auto">
          <p className="my-auto text-lg font-semibold">${product.price}</p>
        </div>
      </div>
      {/* footer section */}
      <div>
        <p className="mt-2 text-sm text-neutral-500 lg:w-[500px] truncate">{product.description}</p>
      </div>
    </div>
  );
}

export default ProductDetailCard;
