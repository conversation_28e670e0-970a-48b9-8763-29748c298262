import api from "@/lib/axios";
import {
  BasicDetailsData,
  BookedServicesCardItem,
  CarePlanCardItem,
  ScheduledCallsCardItem,
} from "./type";
import { returnOrExchangeApiPayload } from "./type";

export const fetchOrdersApi = async () => {
  const { data } = await api.get("/api/v1/order");
  return data;
};
export const fetchOrderedProductApi = async (productId: string) => {
  const { data } = await api.get(`/api/v1/order/${productId}`);
  return data;
};

export const fetchSellerRefundPolicy = async (sellerId: string) => {
  const { data } = await api.get(
    `/api/v1/users/seller/${sellerId}/refund-policy`
  );
  return data;
};

export const cancelOrderApi = async (orderId: string) => {
  const { data } = await api.patch(`/api/v1/order/${orderId}/cancel`);
  return data;
};

export const fetchUserProfileApi = async () => {
  try {
    const response = await api.get("/api/v1/users/customer/profile");
    return response.data;
  } catch (error) {
    console.error("Error fetching user profile:", error);
    throw error;
  }
};

export const updateUserProfileApi = async (payload: BasicDetailsData) => {
  try {
    const response = await api.patch("/api/v1/users/customer", payload);
    return response.data;
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw error;
  }
};

export const returnOrExchangeApi = async (
  payload: returnOrExchangeApiPayload
) => {
  const formData = new FormData();
  formData.append("reasons", payload.reason.join(","));
  payload.images.forEach((image) => {
    formData.append("images", image);
  });
  const { data } = await api.patch(
    `/api/v1/order/${payload.orderId}/${payload.type}`,
    formData,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return data;
};

export const submitReviewApi = async (payload: {
  rating: number;
  review: string;
  productId: string;
}) => {
  const { data } = await api.post(`/api/v1/review`, payload);
  return data;
};
export const submitServiceReviewApi = async (payload: {
  rating: number;
  review: string;
  serviceId: string;
}) => {
  const { data } = await api.post(`/api/v1/review`, payload);
  return data;
};

export const fetchCarePlanApi = async (): Promise<CarePlanCardItem[]> => {
  const { data } = await api.get("/api/v1/users/customer/care-plan");
  return data?.data?.carePlan;
};

export const fetchBookedServicesApi = async (): Promise<
  BookedServicesCardItem[]
> => {
  return [
    {
      _id: "appt1",
      provider: {
        firstName: "Alice",
        lastName: "Johnson",
        specialty: "Dermatology",
        refundPolicy: "Full refund if cancelled 24 hours in advance",
      },
      status: "pending",
      bookedAt: "2025-05-28T10:00:00Z",
      hasRequestedReview: false,
      reviewAdded: false,
    },
    {
      _id: "appt2",
      provider: {
        firstName: "Brian",
        lastName: "Lee",
        specialty: "Therapy",
        refundPolicy: "No refunds after booking",
      },
      status: "accepted",
      bookedAt: "2025-06-01T14:30:00Z",
      hasRequestedReview: false,
      reviewAdded: false,
    },
    {
      _id: "appt3",
      provider: {
        firstName: "Clara",
        lastName: "Nguyen",
        specialty: "Nutrition",
        refundPolicy: "50% refund if cancelled within 12 hours",
      },
      status: "rejected",
      bookedAt: "2025-05-27T09:15:00Z",
      hasRequestedReview: false,
      reviewAdded: false,
    },
    {
      _id: "appt4",
      provider: {
        firstName: "David",
        lastName: "Martinez",
        specialty: "Chiropractic",
        refundPolicy: "Refund available within 24 hours of booking",
      },
      status: "completed",
      bookedAt: "2025-01-31T11:00:00Z",
      hasRequestedReview: true,
      reviewAdded: false,
    },
    {
      _id: "appt5",
      provider: {
        firstName: "Emily",
        lastName: "Smith",
        specialty: "Life Coaching",
        refundPolicy: "Non-refundable",
      },
      status: "cancelled",
      bookedAt: "2025-05-25T16:00:00Z",
      hasRequestedReview: false,
      reviewAdded: false,
    },
  ];

  const { data } = await api.get("/api/v1/users/customer/services");
  return data?.data;
};

export const fetchScheduledCallsApi = async (): Promise<
  ScheduledCallsCardItem[]
> => {
  return [
    {
      _id: "booking1",
      provider: {
        firstName: "Alice",
        lastName: "Johnson",
        certifications: [
          {
            url: "https://nurture-ye-s3.s3.us-east-1.amazonaws.com/public/nurture-docs/1748078097495.pdf",
            key: "public/nurture-docs/1748078097495.pdf",

            _id: "68318e17531de579f0e51325",
          },
        ],
        profilePicture: [
          {
            url: "https://nurture-ye-s3.s3.us-east-1.amazonaws.com/public/nurture-images/1748078097495.png",
            key: "public/nurture-images/1748078097495.png",
          },
        ],
        totalRating: 4.8,
        specialty: "Physical Therapy",
        refundPolicy: "Full refund within 24 hours of booking",
        serviceLocation: ["New York", "Brooklyn"],
        experience: 5,
      },
      status: "pending",
      bookedAt: "2025-05-20T10:00:00Z",
    },
    {
      _id: "booking10",
      provider: {
        firstName: "Alice",
        lastName: "Johnson",
        certifications: [
          {
            url: "https://nurture-ye-s3.s3.us-east-1.amazonaws.com/public/nurture-docs/1748078097495.pdf",
            key: "public/nurture-docs/1748078097495.pdf",

            _id: "68318e17531de579f0e51325",
          },
        ],
        profilePicture: [
          {
            url: "https://nurture-ye-s3.s3.us-east-1.amazonaws.com/public/nurture-images/1748078097495.png",
            key: "public/nurture-images/1748078097495.png",
          },
        ],
        totalRating: 4.8,
        specialty: "Physical Therapy",
        refundPolicy: "Full refund within 24 hours of booking",
        serviceLocation: ["New York", "Brooklyn"],
        experience: 5,
      },
      status: "completed",
      bookedAt: "2025-05-20T10:00:00Z",
    },
    {
      _id: "booking3",
      provider: {
        firstName: "Michael",
        lastName: "Smith",
        experience: 7,
        certifications: [
          {
            url: "https://nurture-ye-s3.s3.us-east-1.amazonaws.com/public/nurture-docs/1748078097495.pdf",
            key: "public/nurture-docs/1748078097495.pdf",

            _id: "68318e17531de579f0e51325",
          },
        ],
        profilePicture: [
          {
            url: "https://nurture-ye-s3.s3.us-east-1.amazonaws.com/public/nurture-images/1748078097495.png",
            key: "public/nurture-images/1748078097495.png",
          },
        ],
        totalRating: 4.9,
        specialty: "Chiropractic",
        refundPolicy:
          "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.",
        serviceLocation: ["Los Angeles"],
      },
      status: "cancelled",
      bookedAt: "2025-05-22T15:30:00Z",
    },
  ];

  const { data } = await api.get("/api/v1/users/customer/calls");
  return data?.data;
};
