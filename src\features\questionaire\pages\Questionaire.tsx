import { <PERSON><PERSON> } from "@/components/ui/button";
import StepProgress from "../components/StepProgress";
import FingerprintIcon from "@/assets/fingerprint.png";
import LeftArrowIcon from "@/assets/left-arrow-icon.png";
import { useQuestionnaireStore } from "@/store/questionaireStore";
import { questionnaireData } from "../utils";
import { useState } from "react";
import { Modal } from "@/components/ui/modal";
import { useNavigate } from "react-router-dom";
import QuestionForm from "../components/QuestionForm";

function Questionaire() {
  const { goToPrevQuestion, skipAll } = useQuestionnaireStore();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const navigate = useNavigate();

  const handleClose = (): void => setIsOpen(false);
  const handleConfirm = (): void => {
    setIsOpen(false);
    skipAll();
    navigate("/");
  };
  return (
    <>
      <div className="flex flex-col min-h-screen bg-white md:flex-row ">
        {/* Sidebar: Step Progress */}
        <aside className="flex flex-col md:w-1/3 lg:w-1/4 p-9 md:gap-y-16 bg-brown-1">
          <div className="">
            <div className="flex justify-between mx-auto gap-x-3 md:gap-x-0 md:flex-col gap-y-4">
              <div className="flex justify-between gap-x-2 md:flex-col md:gap-y-3">
                <img
                  src={FingerprintIcon}
                  alt="Fingerprint Icon"
                  className="w-12 h-12"
                />
                <h1 className="text-lg text-white md:text-2xl font-prettywise">
                  Insights for Your <br /> Personalized Care
                </h1>
              </div>
              <Button
                onClick={() => setIsOpen(true)}
                className="my-auto bg-transparent md:hidden focus:bg-transparent hover:bg-transparent"
              >
                skip
              </Button>
              <p className="hidden text-gray-1 md:block">
                Tell us about your pregnancy, and <br /> we'll provide you with
                personalized recommendations to support your <br /> postpartum
                journey.
              </p>
            </div>
          </div>
          <div className="mt-5 md:mt-0">
            <StepProgress />
          </div>
        </aside>

        {/* Main Content: Question + Navigation */}
        <main className="flex-1 w-full h-full">
          <div className="justify-between w-11/12 mx-auto mt-5 mb-10 md:flex">
            <img
              src={LeftArrowIcon}
              alt="left arrow"
              className="my-auto cursor-pointer"
              onClick={() => goToPrevQuestion(questionnaireData)}
            />

            <Button
              onClick={() => setIsOpen(true)}
              className="hidden text-black bg-transparent md:block hover:bg-transparent"
            >
              skip
            </Button>
          </div>
          <div className="flex justify-center md:max-h-[80vh] overflow-y-scroll scrollbar-hide mb-3">
            <QuestionForm />
          </div>
        </main>
      </div>
      <Modal isOpen={isOpen} onClose={handleClose}>
        <h2 className="mb-2 text-lg font-semibold text-gray-900">
          Do you want to skip the questionnaire?
        </h2>
        <p className="mb-6 text-neutral-300">
          Are you sure you want to skip this questionnaire? Completing this quiz
          will let us give you personalized recommendations.
        </p>

        <div className="flex justify-end gap-4">
          <Button
            variant={"outline"}
            onClick={handleClose}
            className="px-8 py-2 border border-gray-300 rounded-full hover:bg-gray-100"
          >
            No
          </Button>
          <Button onClick={handleConfirm}>Yes, skip all</Button>
        </div>
      </Modal>
    </>
  );
}

export default Questionaire;
