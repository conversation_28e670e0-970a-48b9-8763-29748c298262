import { useCallback, useMemo, useState } from "react";
import ChatSearch from "./ChatSearch";
import ChatList from "./ChatList";
import MessageSection from "./MessageSection";
import useDebounce from "@/hooks/useDebounce";
import { SelectedChat } from "../type";

function ChatLayout() {
  const [search, setSearch] = useState<string>("");
  const debouncedSearch = useDebounce(search, 800);
  const memoizedSearch = useMemo(() => debouncedSearch, [debouncedSearch]);
  const [hideMessageSection, setHideMessageSection] = useState(true);
  const handleHideMessageSection = (status: boolean) => {
    setHideMessageSection(status);
    if (status) {
      setSelectedChat({} as SelectedChat);
    }
  };
  const handleSearch = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  }, []);
  const [selectedChat, setSelectedChat] = useState<Omit<SelectedChat, "role">>(
    {} as SelectedChat
  );
  const handleChatSelect = useCallback((data: Omit<SelectedChat, "role">) => {
    setSelectedChat(data);
  }, []);
  return (
    <div className="grid w-full grid-cols-12 mx-auto rounded-sm">
      <div
        className={`${hideMessageSection ? "block " : "hidden md:block"} col-span-12 md:py-4 md:border-x md:col-span-4 border-neutral-40`}
      >
        <div className="flex flex-col px-3">
          <h1 className="mb-1 text-lg">Messaging</h1>
          <ChatSearch search={search} handleSearch={handleSearch} />
        </div>
        {/* chat list */}
        <div className="mt-4">
          <ChatList
            search={memoizedSearch}
            selectedChatId={selectedChat?.conversationId as string}
            onChatSelect={handleChatSelect}
            handleHideMessageSection={() => handleHideMessageSection(false)}
          />
        </div>
      </div>
      <div
        className={`${hideMessageSection ? "hidden md:block" : "block"} max-h-[100vh] md:min-h-[95vh] col-span-12 md:border md:block md:col-span-8 md:rounded-e-sm border-neutral-40`}
      >
        <MessageSection
          handleHideMessageSection={() => handleHideMessageSection(true)}
          name={selectedChat?.name || ""}
          description={selectedChat?.description || ""}
          receiverId={selectedChat?.receiverId}
          conversationId={selectedChat?.conversationId}
        />
      </div>
    </div>
  );
}

export default ChatLayout;
