import { useEffect, useState } from "react";
import RecentC<PERSON>Header from "./RecentChatHeader";
import { fetchCustomerChatListApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import InfiniteScroll from "react-infinite-scroll-component";
import RecentChatHeaderSkelton from "./RecentChatHeaderSkelton";
import { Conversation, SelectedChat } from "../type";
import { useAuthStore } from "@/store/authStore";

function ChatList({
  search,
  selectedChatId,
  onChatSelect,
  handleHideMessageSection,
}: {
  search: string;
  selectedChatId: string | null;
  onChatSelect: (data: Omit<SelectedChat, "role">) => void;
  handleHideMessageSection: () => void;
}) {
  const currentUser = useAuthStore((state) => state.user);
  const socket = currentUser?.socket?.chat;
  const [currentPage, setCurrentPage] = useState(1);
  const [allChats, setAllChats] = useState<Conversation[]>([]);

  const { data, isPending: loading } = useQuery({
    queryKey: ["customer-chat-list", focus, search, currentPage],
    queryFn: () => fetchCustomerChatListApi({ page: currentPage, search }),
  });
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const handleChatSelect = (chat: Conversation) => {
    onChatSelect({
      conversationId: chat._id,
      name: chat.recipient.companyName ?? chat.recipient.firstName ?? "",
      description: chat.recipient.speciality ?? chat.recipient.category ?? "",
      profilePicture: chat.recipient.profilePicture?.[0],
      receiverId: chat.recipient._id,
    });
    handleHideMessageSection();
  };
  useEffect(() => {
    // Reset to the first page when search changes
    setCurrentPage(1);
    setAllChats([]);
  }, [search]);
  useEffect(() => {
    if (!data?.conversations) return;
    if (currentPage === 1) {
      setAllChats(data.conversations);
    } else {
      setAllChats((prev) => [...prev, ...data.conversations]);
    }
  }, [data?.conversations, currentPage]);
  useEffect(() => {
    if (!socket) return;

    const handleConversation = (data: Conversation) => {
      const conversationIndex = allChats.findIndex(
        (chat) => chat._id === data._id
      );
      if (conversationIndex === -1) {
        // if conversation is not in the list
        setAllChats((prev) => [data, ...prev]);
        return;
      }

      const updatedChats = [...allChats];
      updatedChats.splice(conversationIndex, 1);
      updatedChats.unshift(data);
      setAllChats(updatedChats);
    };
    socket.on("conversation", handleConversation);
    return () => {
      socket.off("conversation", handleConversation);
    };
  }, [socket, allChats]);
  return (
    <div className="w-full max-h-[80vh] overflow-y-scroll scrollbar-hide ">
      <InfiniteScroll
        style={{ width: "100%" }}
        scrollThreshold={0.5}
        scrollableTarget="scrollableDiv"
        dataLength={data?.conversations?.length || 0}
        next={() => handlePageChange(currentPage + 1)}
        hasMore={((data?.totalPages as number) || 0) > currentPage}
        loader={
          <div className="flex justify-center py-4">
            <div className="flex items-center gap-2 text-sm text-neutral-500">
              <div className="w-4 h-4 border-2 rounded-full border-coral border-t-transparent animate-spin"></div>
              Loading more conversations...
            </div>
          </div>
        }
        endMessage={
          <div className="flex justify-center py-4">
            <div className="text-sm text-neutral-400">
              No more conversations to load
            </div>
          </div>
        }
      >
        {loading ? (
          <div className="space-y-3">
            {[1, 2, 3, 4].map((_, index) => (
              <RecentChatHeaderSkelton key={index} />
            ))}
          </div>
        ) : (
          allChats.map((chat, index) => (
            <div
              className={`${
                [chat._id, chat.recipient._id].includes(
                  selectedChatId as string
                )
                  ? "bg-tints-40"
                  : ""
              }`}
              key={index}
              onClick={() => handleChatSelect(chat)}
            >
              <RecentChatHeader
                image={chat.recipient.profilePicture?.[0]?.url}
                name={
                  chat.recipient.firstName ||
                  chat.recipient.companyName ||
                  "Unknown"
                }
                lastMessage={chat.lastMessage.content}
                unreadCount={chat.unreadCount}
              />
            </div>
          ))
        )}
      </InfiniteScroll>
    </div>
  );
}

export default ChatList;
