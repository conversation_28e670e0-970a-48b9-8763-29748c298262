import { useCallback, useState } from "react";
import Layout from "@/components/layout/AdminLayout";
import ProviderTable from "../components/ProviderTable";
import { showToast } from "@/lib/toast";
import { useMutation, useQuery } from "@tanstack/react-query";
import { ApproveProviderApi, fetchProvidersApi } from "../api";
import ProviderDetailsModal from "../components/ProviderDetailsModal";
import ProviderServicesModal from "../components/ProviderServicesModal";
import ServiceDetailsModal from "../components/ServiceDetailsModal";

function Providers() {
  const [isOpen, setIsOpen] = useState({
    providerDetails: false,
    providerServices: false,
    serviceDetails: false,
  });
  const [selectedProviderId, setSelectedProviderId] = useState("");
  const onOpen = useCallback((modal: keyof typeof isOpen) => {
    setIsOpen((prev) => ({ ...prev, [modal]: true }));
  }, []);
  const onClose = useCallback((modal: keyof typeof isOpen) => {
    setIsOpen((prev) => ({ ...prev, [modal]: false }));
  }, []);
  const onOpenProviderModal = useCallback(
    (id: string) => {
      onOpen("providerDetails");
      setSelectedProviderId(id);
    },
    [onOpen]
  );
  const [currentPage, setCurrentPage] = useState(1);
  const [focus, setFocus] = useState("all");
  const [selectedServiceId, setSelectedServiceId] = useState("");
  let totalPages = 1;

  const {
    data,
    isPending: loading,
    isSuccess,
    refetch,
  } = useQuery({
    queryKey: ["providers", currentPage, focus],
    queryFn: () => fetchProvidersApi({ type: focus, page: currentPage }),
  });
  if (isSuccess) {
    totalPages = data?.data?.totalPages;
  }
  const { mutate } = useMutation({
    mutationFn: ApproveProviderApi,
    onSuccess: ({ data }) => {
      const toastMessage = `Provider ${
        data.provider.isApproved ? "approved" : "rejected"
      } successfully!`;
      refetch();
      showToast(toastMessage, "success");
    },
    onError: () => {
      showToast("failed to approve provider", "error");
    },
  });

  const handleProviderApprove = useCallback(
    (providerId: string, isApproved: boolean) => {
      mutate({ providerId, isApproved });
    },
    [mutate]
  );
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  return (
    <>
      <Layout>
        <div className="p-6 bg-white rounded-lg">
          <div className="mb-6">
            <h1 className="mb-2 text-2xl font-bold">Providers</h1>
            <div className="flex space-x-4">
              <button
                onClick={() => setFocus("all")}
                className={`${
                  focus === "all" ? "border-b-4 border-orange-1 font-bold" : ""
                }  font-medium pb-2`}
              >
                All
              </button>
              <button
                onClick={() => setFocus("pending")}
                className={`${
                  focus === "pending"
                    ? "border-b-4 border-orange-1 font-bold"
                    : ""
                }  font-medium pb-2`}
              >
                Pending verification
              </button>
            </div>
          </div>

          <ProviderTable
            data={data?.data?.providers}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            focus={focus}
            loading={loading}
            handleProviderApprove={handleProviderApprove}
            onOpen={onOpenProviderModal}
          />
        </div>
      </Layout>
      {isOpen.providerDetails && (
        <ProviderDetailsModal
          isOpen={isOpen.providerDetails}
          onClose={() => onClose("providerDetails")}
          onOpenServices={() => onOpen("providerServices")}
          selectedProviderId={selectedProviderId}
        />
      )}
      {isOpen.providerServices && (
        <ProviderServicesModal
          isOpen={isOpen.providerServices}
          onClose={() => onClose("providerServices")}
          selectedProviderId={selectedProviderId}
          onOpenServiceDetails={() => onOpen("serviceDetails")}
          openProviderDetails={() => onOpen("providerDetails")}
          handleService={(serviceId: string) => setSelectedServiceId(serviceId)}
        />
      )}
      {isOpen.serviceDetails && (
        <ServiceDetailsModal
          isOpen={isOpen.serviceDetails}
          onClose={() => onClose("serviceDetails")}
          openProviderServices={() => onOpen("providerServices")}
          selectedServiceId={selectedServiceId}
        />
      )}
    </>
  );
}

export default Providers;
