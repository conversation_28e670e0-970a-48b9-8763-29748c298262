import { Separator } from "@/components/ui/separator";
import CheckoutButton from "./CheckoutButton";

function OrderSummary({
  subTotal,
  shippingFee,
  payableAmount,
  discount,
  hasAddress,
}: {
  subTotal: number;
  shippingFee: number;
  payableAmount: number;
  discount?: number;
  hasAddress: boolean;
}) {
  return (
    <div className="p-5 border rounded-lg border-gray-2">
      <h1 className="font-bold">Order Summary</h1>
      <div className="flex justify-between mt-4">
        <h5 className="text-neutral-600">Subtotal</h5>
        <h5 className="text-neutral-600">${subTotal}</h5>
      </div>
      {discount ? (
        <div className="flex justify-between mt-4">
          <h5 className="font-bold text-neutral-600">Discount</h5>
          <h5 className="font-bold text-neutral-600">${discount}</h5>
        </div>
      ) : null}
      <div className="flex justify-between mt-4">
        <h5 className="text-neutral-600">Shipping</h5>
        <h5 className="text-neutral-600">${shippingFee}</h5>
      </div>
      <Separator className="my-5" />
      <div className="flex justify-between mt-4">
        <h5 className="text-lg">Payable amount</h5>
        <h5 className="font-bold">${payableAmount}</h5>
      </div>
      <div className="w-full mt-5">
        <CheckoutButton isDisabled={!hasAddress} payableAmount={payableAmount}/>
      </div>
    </div>
  );
}

export default OrderSummary;
