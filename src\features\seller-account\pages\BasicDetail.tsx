import { Button } from "@/components/ui/button";
import SellerLayout from "../components/Layout";
import { Input } from "@/components/ui/input";
import { showToast } from "@/lib/toast";
import { useMutation, useQuery } from "@tanstack/react-query";
import { joiResolver } from "@hookform/resolvers/joi";
import { useForm, Controller } from "react-hook-form";
import { useEffect, useState } from "react";
import { profileData } from "../type";
import { ProfileSchema } from "../validation";
import { fetchSellerDetailssApi, updateSellerProfileApi } from "../api";
import FileUpload from "@/assets/file-upload.png";
import { Textarea } from "@/components/ui/textarea";
import SellerProfileSkelton from "../components/SellerProfileSkelton";

function BasicDetail() {
  const [selectedFileName, setSelectedFileName] =
    useState<string>("Introductory video");

  // Fetch seller details
  const {
    data: sellerDetails,
    isPending: loading,
    refetch,
  } = useQuery({
    queryKey: ["seller-profile"],
    queryFn: fetchSellerDetailssApi,
  });

  const {
    control,
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<profileData>({
    resolver: joiResolver(ProfileSchema),
    defaultValues: {
      companyName: "",
      category: "",
      phone: "",
      taxId: "",
      refundPolicy: "",
      video: undefined,
    },
  });

  // Set initial form values when seller details are loaded
  useEffect(() => {
    if (sellerDetails?.seller) {
      // Reset form with seller details
      reset({
        companyName: sellerDetails.seller.companyName || "",
        category: sellerDetails.seller.category || "",
        phone: sellerDetails.seller.phone || "",
        taxId: sellerDetails.seller.taxId || "",
        refundPolicy: sellerDetails.seller.refundPolicy || "",
      });

      if (sellerDetails.seller.introductionVideo?.url) {
        const urlParts = sellerDetails.seller.introductionVideo.url.split("/");
        const fileName = urlParts[urlParts.length - 1] || "Current video";
        setSelectedFileName(fileName);
      }
    }
  }, [sellerDetails, reset]);

  // Handle video file selection
  const handleVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFileName(file.name);
    }
  };

  const { mutate, isPending } = useMutation({
    mutationFn: updateSellerProfileApi,
    onSuccess: () => {
      showToast("Profile updated successfully", "success");
      refetch();
    },
    onError: () => {
      showToast("Failed to update profile. Please try again.", "error");
    },
  });

  const onSubmit = (data: profileData) => {
    try {
      const formData = new FormData();
      formData.append("companyName", data.companyName);
      formData.append("category", data.category);
      formData.append("phone", data.phone);
      formData.append("taxId", data.taxId);
      formData.append("refundPolicy", data.refundPolicy);

      // Only append video if it exists
      if (data.video && data.video.length > 0) {
        formData.append("introductionVideo", data.video[0]);
      }

      mutate({
        payload: formData,
        sellerId: sellerDetails?.seller._id as string,
      });
    } catch {
      showToast("Error preparing form data", "error");
    }
  };

  return (
    <SellerLayout hideOptions={true}>
      <div className="md:w-1/2">
        {loading ? (
          <SellerProfileSkelton />
        ) : (
          <form className="w-full space-y-4" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <Controller
                name="companyName"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="text"
                    placeholder="Company Name"
                    className="w-full border border-input-border"
                  />
                )}
              />
              {errors.companyName && (
                <p className="text-sm text-red-500 text-start">
                  {errors.companyName.message}
                </p>
              )}
            </div>

            <div>
              <Controller
                name="category"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="text"
                    placeholder="Category"
                    className="w-full border border-input-border"
                  />
                )}
              />
              {errors.category && (
                <p className="text-sm text-red-500 text-start">
                  {errors.category.message}
                </p>
              )}
            </div>

            <div>
              <Controller
                name="phone"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    type="number"
                    inputMode="numeric"
                    placeholder="Phone number"
                    className="w-full border border-input-border"
                  />
                )}
              />
              {errors.phone && (
                <p className="text-sm text-red-500 text-start">
                  {errors.phone.message}
                </p>
              )}
            </div>

            <div>
              <Controller
                name="taxId"
                control={control}
                render={({ field }) => (
                  <Input
                    {...field}
                    placeholder="Tax ID"
                    className="w-full border border-input-border"
                  />
                )}
              />
              {errors.taxId && (
                <p className="text-sm text-red-500 text-start">
                  {errors.taxId.message}
                </p>
              )}
            </div>

            <div>
              {/* video form */}
              <label
                htmlFor="video-upload"
                className="flex items-center overflow-hidden border rounded-md cursor-pointer border-input-border"
              >
                <div className="flex-grow text-base">
                  <p className="mx-3 text-gray-500 truncate text-start">
                    {selectedFileName}
                  </p>
                </div>
                <div className="flex items-center justify-center p-2">
                  <img
                    src={FileUpload}
                    className="w-5 h-5 text-gray-500"
                    alt="Upload"
                  />
                </div>
                <Input
                  id="video-upload"
                  type="file"
                  accept="video/mp4,video/webm,video/ogg"
                  {...register("video", {
                    onChange: handleVideoChange,
                  })}
                  className="hidden"
                />
              </label>
              {errors.video && (
                <p className="text-sm text-red-500 text-start">
                  {errors.video.message}
                </p>
              )}
            </div>

            <div>
              <Controller
                name="refundPolicy"
                control={control}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    placeholder="Refund Policy"
                    className="w-full border border-input-border"
                  />
                )}
              />
              {errors.refundPolicy && (
                <p className="text-sm text-red-500 text-start">
                  {errors.refundPolicy.message}
                </p>
              )}
            </div>

            <div>
              <Button
                type="submit"
                className="w-full mt-4"
                disabled={isPending}
              >
                {isPending ? "Submitting..." : "Submit"}
              </Button>
            </div>
          </form>
        )}
      </div>
    </SellerLayout>
  );
}

export default BasicDetail;
