import { PhaseQuestions } from "./types";

export const formatQuestionAire = (answers: Record<string, Array<string>>) => {
  const formattedAnswers = Object.entries(answers).map((answer) => {
    const [key, value] = answer;
    const [phaseIndex, questionIndex] = key.split("-");
    const question =
      questionnaireData[Number(phaseIndex)].questions[Number(questionIndex)];
    return {
      question: question.question,
      answer: value,
    };
  });
  formattedAnswers.pop(); // removing the last question

  return {
    personal_details: [],
    postnatal_assessment_questions: formattedAnswers,
  };
};
export const phases = [
  `Pregnancy & Postpartum History`,
  "Recovery & Support Preferences",
  "Lifestyle & Customization",
];

export const questionnaireData: PhaseQuestions[] = [
  {
    phase: "Pregnancy & Postpartum",
    questions: [
      {
        question: "Type of Birth",
        type: "single_select",
        options: [
          "Vaginal",
          "Planned C-Section",
          "Emergency C-Section",
          "C-Section (unspecified)",
          "Home Birth",
          "Stillbirth",
          "Surrogate Delivery",
          "Adoptive Placement",
        ],
      },
      {
        question: "Parity (Previous Births)",
        type: "single_select",
        options: [
          "First-time parent / First child",
          "Second child",
          "Third child",
          "Multiparous (More than one child)",
        ],
      },
      {
        question: "Feeding Plan",
        type: "single_select",
        options: [
          "Breastfeeding / Exclusive Breastfeeding",
          "Bottle-feeding / Formula feeding",
          "Pumping Breastmilk",
          "Combo feeding (Breast + Formula)",
          "Induced Lactation",
          "Exclusive pumping (fed by partner)",
          "Mixed feeding (Tandem nursing + formula)",
          "Weaned",
        ],
      },
    ],
  },
  {
    phase: "Recovery & Support",
    questions: [
      {
        question: "Top Concerns",
        type: "multiselect",
        options: [
          "Physical Recovery (e.g., incision healing, core strength, tear healing)",
          "Breastfeeding Challenges (e.g., support, supply, pain)",
          "Emotional Wellbeing (e.g., overwhelm, anxiety, grief, depression, rage, guilt, stress, trauma, disconnection)",
          "Sleep Deprivation / Fatigue",
          "Baby's Wellbeing (e.g., NICU, fussiness, feeding issues, bonding, safety)",
          "Nutrition & Diet (e.g., replenishing nutrients, specific dietary needs/restrictions, blood sugar)",
          "Future Health/Pregnancy (e.g., preparing for future pregnancy, long-term health concerns)",
          "Cultural/Communication Needs (e.g., language barrier, understanding care system, religious practices, balancing traditions)",
          "Specific Conditions (e.g., pelvic pain, hormonal issues, OCD, ADHD)",
        ],
      },
      {
        question: "Current Sleep Pattern",
        type: "text_description",
        placeholder:
          "(e.g., hours per night, interruptions, stress level affecting sleep)",
      },
      {
        question: "Current Physical Symptoms",
        type: "multiselect",
        options: [
          "Breast-related (e.g., engorgement, sore nipples, tenderness)",
          "Incision/Scar-related (e.g., sensitivity, pain)",
          "Pain (e.g., back pain, uterine cramps, pelvic pain, painful sitting, headaches, jaw tension)",
          "Fatigue / Sleep Deprivation",
          "Digestive Issues (e.g., bloating)",
          "Hormonal Symptoms (e.g., fluctuations, hair loss, sugar crashes)",
          "Anxiety Symptoms (e.g., tight chest, shallow breathing)",
          "Bathroom anxiety",
          "Nausea",
          "Insomnia",
          "Other",
        ],
      },
      {
        question: "Support System",
        type: "multiselect_or_description",
        options: [
          "Partner Support (Supportive)",
          "Partner Support (Working full-time)",
          "Partner Support (On leave)",
          "Partner Support (Deployed)",
          "Partner Support (Experiencing PPD)",
          "Family/Friends Support (Limited)",
          "Family/Friends Support (Staying temporarily)",
          "Family/Friends Support (Living with family)",
          "Family/Friends Support (None nearby)",
          "Other Support (e.g., night nurse)",
          "No/Limited Support",
        ],
        allowDescription: true,
      },
      {
        question: "Relevant History",
        type: "multiselect",
        options: [
          "Mental Health (e.g., Depression, Anxiety, OCD, PPD/PPA in previous pregnancy)",
          "Medical Conditions (e.g., Gestational Diabetes, Hypothyroidism)",
          "Pregnancy/Fertility History (e.g., IVF, Previous Loss - Stillbirth/Miscarriage)",
          "Neurodevelopmental (e.g., ADHD)",
          "None relevant",
        ],
      },
      {
        question: "Baby's Status (If applicable)",
        type: "multiselect",
        options: [
          "NICU Stay",
          "Temperament (High needs/fussy)",
          "Health Concerns (e.g., suspected intolerance/allergy, feeding issues)",
          "No specific status/concerns",
          "Not applicable (e.g., adoption, loss)",
        ],
      },
      {
        question: "Cultural, Religious, or Dietary Information",
        type: "multiselect_or_description",
        options: [
          "Specific Cultural Background relevant to care",
          "Specific Religious Practices relevant to care (e.g., Sabbath, Halal, Nifas, Ayurvedic)",
          "Language Barrier Exists",
          "Specific Dietary Restrictions/Preferences (e.g., Vegan, Vegetarian, Halal)",
        ],
        allowDescription: true,
      },
      {
        question: "Lifestyle & Goals",
        type: "multiselect_or_description",
        options: [
          "Parenting Style (e.g., Holistic, Minimalist)",
          "Planning Early Return to Work",
          "Resuming Athletic Training",
          "Other specific goals relevant to postpartum period",
        ],
        allowDescription: true,
      },
      {
        question: "Location & Access",
        type: "multiselect",
        options: [
          "Rural/Remote Location",
          "Limited Access to In-Person Services",
          "Limited/Low-Bandwidth Internet Access",
          "Living on Military Base",
        ],
      },
    ],
  },
  {
    phase: "Lifestyle & Customization",
    questions: [
      {
        question:
          "Would you like personalized product recommendations to support your postpartum recovery?",
        type: "single_select",
        options: ["Yes", "No"],
      },
    ],
  },
];
