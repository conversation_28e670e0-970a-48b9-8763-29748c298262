import Nurture<PERSON>ogo from "@/assets/nurture-logo.png";
import SellerDashboard<PERSON>ogo from "@/assets/seller-dashboard.png";
import OrderLogo from "@/assets/order.png";
import MessageLogo from "@/assets/message.png";
import LogoutLogo from "@/assets/logout.png";
import ReviewLogo from "@/assets/review.png";
import ProfileLogo from "@/assets/profile-icon.png";
import { Link, useLocation } from "react-router-dom";
import LogoutModal from "@/components/ui/logout-modal";
import { useState } from "react";
function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const onClose = () => {
    setIsOpen(false);
  };
  const onOpen = () => {
    setIsOpen(true);
  };
  const navbarItems = [
    { icon: SellerDashboardLogo, name: "Dashboard", href: "/seller/dashboard" },
    { icon: OrderLogo, name: "Orders", href: "/seller/orders" },
    { icon: Message<PERSON>ogo, name: "Messages", href: "/seller/messages" },
    { icon: Review<PERSON>ogo, name: "Reviews", href: "/seller/reviews" },
  ];
  const navBarBottomItems = [
    {
      icon: ProfileLogo,
      name: "Profile",
      href: "/seller/account/basic-details",
    },
  ];
  const { pathname } = useLocation();
  const key = pathname.split("/")[2];

  return (
    <>
      <aside className="flex flex-col justify-between h-full p-5 gap-y-5">
        <div className="p-3">
          <img src={NurtureLogo} alt="nurture" className="mx-auto" />
        </div>
        <div className=" grow">
          <ul>
            {navbarItems.map((item) => (
              <Link
                to={item.href}
                key={item.name}
                className="my-5 cursor-pointer "
              >
                <li
                  className={`${
                    key === item.name.toLocaleLowerCase() ? "bg-tints-200" : ""
                  } flex gap-x-3 my-5`}
                  key={item.name}
                >
                  <div
                    className={`w-1 ${
                      key === item.name.toLocaleLowerCase()
                        ? "bg-orange-1"
                        : "bg-transparent"
                    }`}
                  ></div>

                  <div className="flex py-2 my-auto gap-x-3">
                    <img className="my-auto" src={item.icon} alt={item.name} />
                    <span
                      className={`w-1 ${
                        key === item.name.toLocaleLowerCase()
                          ? "font-semibold"
                          : "font-normal"
                      }`}
                    >
                      {item.name}
                    </span>
                  </div>
                </li>
              </Link>
            ))}
          </ul>
        </div>
        <div className="flex flex-col gap-y-8">
          {navBarBottomItems.map((item) => (
            <Link
              to={item.href}
              key={item.name}
              className="flex ml-5 cursor-pointer gap-x-3"
            >
              <img className="my-auto" src={item.icon} alt={item.name} />
              <p>{item.name}</p>
            </Link>
          ))}
          <div className="flex ml-5 cursor-pointer gap-x-3">
            <img className="my-auto" src={LogoutLogo} alt="logout" />
            <button onClick={onOpen}>Logout</button>
          </div>
        </div>
      </aside>
      <LogoutModal isOpen={isOpen} onClose={onClose} />
    </>
  );
}

export default Navbar;
