import { useState, useRef, useEffect } from "react";
import FingerPrintImage from "@/assets/fingerprint.png";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Link, useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
import { verifyOtpApi, resendOtpApi } from "../api";
import { useMutation } from "@tanstack/react-query";
import { showToast } from "@/lib/toast";
import { AxiosError } from "axios";

function OtpForm() {
  const location = useLocation();
  const navigate = useNavigate();
  const state = location.state as { email: string };
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  const handleChange = (value: string, index: number) => {
    if (value.length > 1) return;
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Move focus to the next input
    if (value && index < otp.length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  // Start countdown for resend OTP
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000);
    } else {
      setCanResend(true);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // Resend OTP mutation
  const { mutate: resendOtp, isPending: isResending } = useMutation({
    mutationFn: resendOtpApi,
    onSuccess: () => {
      showToast("OTP resent successfully", "success");
      setCountdown(60);
      setCanResend(false);
    },
    onError: () => {
      showToast("Failed to resend OTP", "error");
    },
  });

  const handleResendOtp = () => {
    if (canResend && !isResending) {
      resendOtp({ email: state?.email });
    }
  };

  const { mutate, isPending } = useMutation({
    mutationFn: verifyOtpApi,
    onSuccess: (response) => {
      // Handle different response formats
      let isProfileComplete = false;

      // Format 1: { userId, isProfileComplete }
      if (response.userId) {
        isProfileComplete = !!response.isProfileComplete;
      }
      // Format 2: { data: { auth: { _id, hasCompletedProfile } } }
      else if (response.data?.auth?._id) {
        isProfileComplete = !!response.data.auth.hasCompletedProfile;
      }

      // Note: We're not storing userAuthId here since it's already stored during registration
      // This matches the seller implementation where userAuthId is only stored during registration

      // Check if the user has completed profile setup
      if (isProfileComplete) {
        // If profile is complete, redirect to login
        showToast("Email verified successfully. Please login.", "success");
        navigate("/provider/login");
      } else {
        // If profile is not complete, redirect to profile setup
        // Set a flag to indicate this is a new registration
        sessionStorage.setItem("isNewRegistration", "true");

        // Clear any existing provider data to ensure a clean state
        localStorage.removeItem("providerId");
        localStorage.removeItem("hasAddedServices");
        localStorage.removeItem("hasSetAvailability");
        localStorage.removeItem("planChoosen");

        showToast("Email verified successfully", "success");
        navigate("/provider/setup-profile");
      }
    },
    onError: (error: AxiosError) => {
      if (error.response?.status === 401) {
        showToast("Invalid OTP", "error");
        return;
      }
      showToast("Something went wrong", "error");
    },
  });

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const otpValue = otp.join("");
    if (otpValue.length !== 6) return;
    mutate({ otp: otpValue, email: state?.email });
  };

  return (
    <div className="flex flex-col items-center justify-center w-full max-w-md mx-auto space-y-6 text-center md:mx-0">
      <img
        src={FingerPrintImage}
        alt="Fingerprint"
        className="object-cover mx-auto"
      />
      <h1 className="text-3xl font-bold font-prettywise">Verify Email</h1>
      <p className="text-gray-500">
        Please enter the 6 digit OTP that we've sent to your email
        <br />
        <span className="font-semibold">{state?.email}</span>
      </p>
      <form className="flex flex-col gap-y-8" onSubmit={handleSubmit}>
        <div className="flex justify-center gap-4">
          {otp.map((digit, index) => (
            <Input
              key={index}
              ref={(el) => {
                inputRefs.current[index] = el;
              }}
              type="text"
              pattern="\d"
              maxLength={1}
              inputMode="numeric"
              value={digit}
              onChange={(e) => handleChange(e.target.value, index)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              className="text-xl text-center border md:w-12 md:h-12 shrink border-input-border focus:border-orange-1"
            />
          ))}
        </div>
        <Button disabled={isPending} className="w-full">
          Verify email
        </Button>
      </form>

      <div className="flex flex-col items-center gap-2">
        <p className="text-sm text-gray-500">
          {canResend
            ? "Didn't receive the code?"
            : `Resend code in ${countdown} seconds`}
        </p>
        <Button
          variant="link"
          onClick={handleResendOtp}
          disabled={!canResend || isResending}
          className="h-auto p-0 text-orange-1"
        >
          {isResending ? "Sending..." : "Resend OTP"}
        </Button>
      </div>

      <Link
        to="/provider/register"
        className="flex items-center gap-2 font-semibold"
      >
        <span>&larr;</span> Go back
      </Link>
    </div>
  );
}

export default OtpForm;
