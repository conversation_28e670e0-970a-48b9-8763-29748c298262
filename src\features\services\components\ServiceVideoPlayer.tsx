import { Play } from "lucide-react";

interface ServiceVideoPlayerProps {
  videoUrl?: string;
  providerName: string;
}

function ServiceVideoPlayer({ videoUrl, providerName }: ServiceVideoPlayerProps) {
  if (!videoUrl) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg p-8 aspect-video bg-gray-50">
          <div className="text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
              <Play className="w-8 h-8 text-gray-400" />
            </div>
            <p className="text-gray-500">No introductory video available</p>
            <p className="text-sm text-gray-400 mt-1">from {providerName}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="relative rounded-lg overflow-hidden h-[239px] aspect-video bg-black">
        <video
          src={videoUrl}
          controls
          className="w-full h-full object-cover"
          poster="" 
        >
          Your browser does not support the video tag.
        </video>
      </div>
    </div>
  );
}

export default ServiceVideoPlayer;
