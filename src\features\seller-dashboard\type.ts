export type Product = {
  _id: string;
  images: Record<string, string>[];
  title: string;
  description: string;
  quantity: number;
  price: number;
};

export type ProductTableProps = {
  data: Product[];
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  editProduct: (productId: string) => void;
  deleteProduct: (productId: string) => void;
  loading: boolean;
};

export type ProductCreationPayload = {
  _id?: string;
  title: string;
  description: string;
  sellerId: string;
  quantity: number;
  price: number;
  images?: string[];
};

export type SellerProductCardProps = {
  product: Product;
  showDescription?: boolean;
};
