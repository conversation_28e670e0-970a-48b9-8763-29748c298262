import React from "react";
import { Badge } from "./badge";

export type OrderStatusType =
  | "item shipped"
  | "item delivered"
  | "order confirmed"
  | "item cancelled"
  | "exchange requested"
  | "return requested"
  | "return approved"
  | "exchange approved";

interface OrderStatusBadgeProps {
  status: OrderStatusType;
  className?: string;
}

const OrderStatusBadge: React.FC<OrderStatusBadgeProps> = ({
  status,
  className,
}) => {
  const formatStatus = (status: string) => {
    let updatedStatus = status;
    if (["item shipped", "item delivered", "item cancelled"].includes(status)) {
      updatedStatus = updatedStatus.split(" ")[1];
    }
    if (status === "order confirmed") {
      updatedStatus = "ordered";
    }
    return updatedStatus.charAt(0).toUpperCase() + updatedStatus.slice(1);
  };

  return (
    <Badge variant={status} className={className}>
      {formatStatus(status)}
    </Badge>
  );
};

export default OrderStatusBadge;
