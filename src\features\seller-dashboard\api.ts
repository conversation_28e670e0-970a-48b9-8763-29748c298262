import api from "@/lib/axios";

export const getSellerProductsApi = async (page: number) => {
  const { data } = await api.get(`/api/v1/users/seller/products`, {
    params: {
      page,
    },
  });
  return data;
};
export const getSellerSingleProductApi = async (productId: string) => {
  const { data } = await api.get(`/api/v1/product/${productId}`);
  return data;
};
export const deleteSellerProductsApi = async (productId: string) => {
  const { data } = await api.delete(
    `/api/v1/users/seller/products/${productId}`
  );
  return data;
};

export const sellerAddProductApi = async (payload: FormData) => {
  const { data } = await api.post("/api/v1/users/seller/product", payload, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return data;
};
export const sellerUpdateProductApi = async ({
  payload,
  productId,
}: {
  payload: FormData;
  productId: string;
}) => {
  const { data } = await api.patch(
    `api/v1/users/seller/products/${productId}`,
    payload,
    {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    }
  );
  return data;
};
