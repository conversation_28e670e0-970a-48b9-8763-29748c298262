import { Navigate } from "react-router-dom";
import AuthLayout from "../components/AuthLayout";
import OtpForm from "../components/OtpForm";
import { checkAuthentication } from "@/lib/utils";

function Otp() {
  const { isLoggedIn, href } = checkAuthentication();

  return isLoggedIn ? (
    <Navigate to={href} />
  ) : (
    <AuthLayout>
      <OtpForm />
    </AuthLayout>
  );
}

export default Otp;
