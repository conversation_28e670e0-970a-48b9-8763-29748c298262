import React from "react";
import SignupImage from "@/assets/provider-auth.png";
// Using a placeholder for mobile image - will be replaced later
import MobileSignupImage from "@/assets/provider-auth.png";

function PlanSelectionLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="flex flex-col md:flex-row">
      {/* Left side - Fixed image that stays in place when scrolling */}
      <div className="mb-14 md:mb-0 md:flex-shrink-0 md:sticky md:top-0 md:h-screen md:flex md:items-center">
        {/* Desktop image */}
        <img
          src={SignupImage}
          alt="Provider"
          className="hidden md:block md:h-screen w-auto object-cover"
        />
        {/* Mobile image */}
        <img
          src={MobileSignupImage}
          alt="Provider"
          className="w-full md:hidden"
        />
      </div>

      {/* Right side - Scrollable content that takes all remaining space */}
      <div className="w-full md:flex-1 md:overflow-y-auto md:h-screen">
        <div className="w-full px-6 py-8 mx-auto md:py-12" style={{ maxWidth: "900px" }}>
          {children}
        </div>
      </div>
    </div>
  );
}

export default PlanSelectionLayout;
