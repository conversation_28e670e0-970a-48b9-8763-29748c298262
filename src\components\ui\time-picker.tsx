import * as React from "react";
import { ChevronUp, ChevronDown } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface TimePickerProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

export function TimePicker({ value, onChange, className }: TimePickerProps) {
  const [open, setOpen] = React.useState(false);
  const [hours, setHours] = React.useState<number>(9);
  const [minutes, setMinutes] = React.useState<number>(0);
  const [period, setPeriod] = React.useState<"AM" | "PM">("AM");

  // Common time presets
  const timePresets: Array<{ label: string; hours: number; minutes: number; period: "AM" | "PM" }> = [
    { label: "9:00 AM", hours: 9, minutes: 0, period: "AM" },
    { label: "12:00 PM", hours: 12, minutes: 0, period: "PM" },
    { label: "5:00 PM", hours: 5, minutes: 0, period: "PM" },
    { label: "8:00 AM", hours: 8, minutes: 0, period: "AM" },
    { label: "1:00 PM", hours: 1, minutes: 0, period: "PM" },
    { label: "6:00 PM", hours: 6, minutes: 0, period: "PM" },
  ];

  // Parse the initial value when component mounts or value changes
  React.useEffect(() => {
    if (value) {
      const match = value.match(/(\d+):(\d+)\s*(am|pm)/i);
      if (match) {
        const parsedHours = parseInt(match[1], 10);
        const parsedMinutes = parseInt(match[2], 10);
        const parsedPeriod = match[3].toUpperCase() as "AM" | "PM";

        setHours(parsedHours);
        setMinutes(parsedMinutes);
        setPeriod(parsedPeriod);
      }
    }
  }, [value]);

  // Update the time when any part changes
  const updateTime = (newHours?: number, newMinutes?: number, newPeriod?: "AM" | "PM") => {
    const updatedHours = newHours !== undefined ? newHours : hours;
    const updatedMinutes = newMinutes !== undefined ? newMinutes : minutes;
    const updatedPeriod = newPeriod !== undefined ? newPeriod : period;

    // Format without leading zeros for hours, but with leading zeros for minutes
    const formattedHours = updatedHours.toString();
    const formattedMinutes = updatedMinutes.toString().padStart(2, "0");
    const formattedTime = `${formattedHours}:${formattedMinutes} ${updatedPeriod}`;

    onChange(formattedTime);
  };

  // Increment hours
  const incrementHours = () => {
    const newHours = hours === 12 ? 1 : hours + 1;
    setHours(newHours);
    updateTime(newHours);
  };

  // Decrement hours
  const decrementHours = () => {
    const newHours = hours === 1 ? 12 : hours - 1;
    setHours(newHours);
    updateTime(newHours);
  };

  // Increment minutes
  const incrementMinutes = () => {
    const newMinutes = (minutes + 5) % 60;
    setMinutes(newMinutes);
    updateTime(undefined, newMinutes);
  };

  // Decrement minutes
  const decrementMinutes = () => {
    const newMinutes = (minutes - 5 + 60) % 60;
    setMinutes(newMinutes);
    updateTime(undefined, newMinutes);
  };

  // Toggle AM/PM
  const togglePeriod = () => {
    const newPeriod = period === "AM" ? "PM" : "AM";
    setPeriod(newPeriod);
    updateTime(undefined, undefined, newPeriod);
  };

  // Apply a preset time
  const applyPreset = (preset: { hours: number; minutes: number; period: "AM" | "PM" }) => {
    setHours(preset.hours);
    setMinutes(preset.minutes);
    setPeriod(preset.period);
    updateTime(preset.hours, preset.minutes, preset.period);
    setOpen(false);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-full justify-start text-left font-normal border-input-border focus:border-slate-300 h-10 px-3 py-2",
            !value && "text-muted-foreground",
            className
          )}
        >
          {value || "Select time"}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[280px] p-0" align="start">
        <div className="p-4 bg-white rounded-lg shadow-lg">
          {/* Time Selector */}
          <div className="flex items-center justify-center mb-4">
            {/* Hours */}
            <div className="flex flex-col items-center">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-gray-100 text-orange-1"
                onClick={incrementHours}
              >
                <ChevronUp className="h-5 w-5" />
              </Button>
              <div className="text-3xl font-bold w-16 text-center">
                {hours.toString().padStart(2, "0")}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-gray-100 text-orange-1"
                onClick={decrementHours}
              >
                <ChevronDown className="h-5 w-5" />
              </Button>
            </div>

            {/* Separator */}
            <div className="text-3xl font-bold mx-1">:</div>

            {/* Minutes */}
            <div className="flex flex-col items-center">
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-gray-100 text-orange-1"
                onClick={incrementMinutes}
              >
                <ChevronUp className="h-5 w-5" />
              </Button>
              <div className="text-3xl font-bold w-16 text-center">
                {minutes.toString().padStart(2, "0")}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-gray-100 text-orange-1"
                onClick={decrementMinutes}
              >
                <ChevronDown className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* AM/PM Toggle */}
          <div className="mb-4">
            <Button
              variant="outline"
              className={cn(
                "w-full rounded-full font-medium",
                period === "AM"
                  ? "bg-orange-1 text-white hover:bg-orange-1/90 border-orange-1"
                  : "hover:bg-gray-100"
              )}
              onClick={togglePeriod}
            >
              {period}
            </Button>
          </div>

          {/* Time Presets */}
          <div className="grid grid-cols-3 gap-2 mb-4">
            {timePresets.map((preset, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                className={cn(
                  "text-sm border-input-border",
                  hours === preset.hours &&
                  minutes === preset.minutes &&
                  period === preset.period &&
                  "bg-orange-1 text-white hover:bg-orange-1/90 border-orange-1"
                )}
                onClick={() => applyPreset(preset)}
              >
                {preset.label}
              </Button>
            ))}
          </div>

          {/* Done Button */}
          <Button
            className="w-full bg-orange-1 hover:bg-orange-1/90 text-white"
            onClick={() => setOpen(false)}
          >
            Done
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
