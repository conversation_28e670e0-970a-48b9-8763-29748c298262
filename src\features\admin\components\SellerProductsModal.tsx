import { Modal } from "@/components/ui/modal";
import { fetchSellerProductsApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import CloseIcon from "@/assets/close-icon.svg";
import GoBackIcon from "@/assets/go-back.svg";
import { Product } from "../type";
import ProductDetailCard from "./ProductDetailCard";
import DynamicPagination from "@/components/ui/dynamic-pagination";
import { useState } from "react";
import ProductDetailSkelton from "./ProductDetailSkelton";
function SellerProductsModal({
  isOpen,
  onClose,
  selectedSellerId,
  onOpenProductDetails,
  openSellerDetails,
  handleProduct,
}: {
  isOpen: boolean;
  selectedSellerId: string;
  onOpenProductDetails: () => void;
  openSellerDetails: () => void;
  onClose: () => void;
  handleProduct: (productId: string) => void;
}) {
  const [currentPage, setCurrentPage] = useState(1);
  let totalPages = 1;
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const handleOpenProductDetails = (productId: string) => {
    handleProduct(productId);
    onOpenProductDetails();
    onClose();
  };

  const handleGoBack = () => {
    openSellerDetails();
    onClose();
  };

  const {
    data,
    isPending: loading,
    isSuccess,
  } = useQuery({
    queryKey: ["seller-products", selectedSellerId],
    queryFn: () => fetchSellerProductsApi(selectedSellerId, currentPage),
    enabled: !!selectedSellerId && isOpen,
  });

  if (isSuccess) {
    totalPages = data?.totalPages;
  }
  return (
    <Modal className="lg:min-w-[650px]" isOpen={isOpen} onClose={onClose}>
      <div>
        <div className="flex items-center justify-between mb-6">
          <div className="flex gap-x-4">
            <img
              onClick={handleGoBack}
              className="transition-opacity cursor-pointer hover:opacity-70"
              src={GoBackIcon}
              alt="go back"
            />
            <h1 className="text-gray-900 ">
              {isSuccess && data?.products?.length > 1
                ? `${data?.products.length} products`
                : `${data?.products.length} product`}
            </h1>
          </div>
          <img
            onClick={onClose}
            className="transition-opacity cursor-pointer hover:opacity-70"
            src={CloseIcon}
            alt="close"
          />
        </div>
        <div>
          {loading ? (
            <div className="flex flex-col gap-x-4 gap-y-5">
              {[1, 2, 3, 4, 5].map((_, index) => (
                <div key={index} className="">
                  <ProductDetailSkelton />
                </div>
              ))}
            </div>
          ) : (
            <>
              <div className="flex flex-col gap-x-4 gap-y-5 max-h-[75vh] overflow-y-scroll scrollbar-hide">
                {data?.products.map((product: Product) => (
                  <div
                    key={product._id}
                    className=""
                    onClick={() => handleOpenProductDetails(product._id)}
                  >
                    <ProductDetailCard product={product} />
                  </div>
                ))}
              </div>
              <DynamicPagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            </>
          )}
        </div>
      </div>
    </Modal>
  );
}

export default SellerProductsModal;
