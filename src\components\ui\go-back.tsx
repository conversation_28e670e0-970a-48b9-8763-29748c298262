import GoBackIcon from "@/assets/left-arrow-icon.png";
import { useNavigate } from "react-router-dom";

function GoBack({ href }: { href?: string }) {
  const navigate = useNavigate();
  const handleGoBack = () => {
    if (href) {
      navigate(href);
      return;
    }
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate("/"); // fallback to home
    }
  };
  return (
    <div
      onClick={handleGoBack}
      className="flex items-center gap-2 my-5 cursor-pointer "
    >
      <img src={GoBackIcon} alt="go back" className="w-3 h-3" />
      Go back
    </div>
  );
}

export default GoBack;
