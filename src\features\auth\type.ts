export type RegisterPayload = {
  email: string | undefined;
  password: string | undefined;
};
export type LoginPayload = {
  email: string;
  password: string;
};
export type CustomerGoogleApiResponse = {
  hasCompletedQuiz?: boolean;
  auth: {
    _id: string;
    hasCompletedProfile: boolean;
  };
  accessToken?: string;
};
export type FormData = {
  email: string;
  password: string;
  confirmPassword: string;
};

export type OtpPayloadData = {
  otp: string;
  email: string;
};

export type profileData = {
  firstName: string;
  lastName: string;
  phone: string;
  address: string;
  birthStatus: "currently pregnant" | "loss history" | "postpartum";
  preferredLanguage: string;
  authId?: string;
  authStrategy: "google" | "local";
};

export type forgotPasswordPayload = {
  email: string;
};
export type resetPasswordPayload = {
  newPassword: string;
  confirmPassword: string;
  token: string;
};
