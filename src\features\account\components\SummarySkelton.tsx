import { Skeleton } from "@/components/ui/skeleton";

function SummarySkelton() {
  return (
     <div className="p-5 border border-gray-200 rounded-lg">
      <Skeleton className="w-32 h-6" /> {/* Header */}
      
      <div className="flex justify-between mt-4">
        <Skeleton className="w-24 h-4" />
        <Skeleton className="w-16 h-4" />
      </div>

      <div className="flex justify-between mt-4">
        <Skeleton className="w-24 h-4" />
        <Skeleton className="w-16 h-4" />
      </div>

      <div className="flex justify-between mt-4">
        <Skeleton className="w-24 h-4" />
        <Skeleton className="w-16 h-4" />
      </div>

      <div className="my-5">
        <Skeleton className="w-full h-px" /> {/* Separator */}
      </div>

      <div className="flex justify-between mt-4">
        <Skeleton className="w-32 h-6" />
        <Skeleton className="w-16 h-6" />
      </div>
    </div>
  )
}

export default SummarySkelton
