import { useNavigate } from "react-router-dom";
import { ServiceDetails } from "./ServiceDetailsDialog";
import { Button } from "@/components/ui/button";

type ServiceCardProps = {
  service: ServiceDetails & { id?: string };
};

function ServiceCard({ service }: ServiceCardProps) {
  const navigate = useNavigate();

  const handleViewMore = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card click when button is clicked
    // Use service id if available, otherwise use service name as fallback
    const serviceId = service.id || service.serviceName.toLowerCase().replace(/\s+/g, '-');
    navigate(`/services/booking/${serviceId}`);
  };

  return (
    <div className="border border-gray-200 rounded-lg p-6 h-[250px] hover:shadow-md transition-shadow">
      <div className="flex justify-between items-start mb-2">
        <div>
          <h2 className="text-lg font-semibold">{service.serviceName}</h2>
          <p className="text-gray-600">{service.duration}</p>
        </div>
        <div>
          <span className="font-semibold text-lg">${service.price}</span>
        </div>
      </div>
      <p className="text-gray-700 line-clamp-3 mb-4">{service.description}</p>
      <Button
        onClick={handleViewMore}
        variant="outline"
        className= " h-auto font-normal w-full"
      >
        View more
      </Button>
    </div>
  );
}

export default ServiceCard;
