import { useQuery } from "@tanstack/react-query";
import CouponCard from "../components/CouponCard";
import { fetchPromotionalOffersApi } from "../api";
import CouponSkelton from "../components/CouponSkelton";
import CouponDeleteModal from "../components/CouponDeleteModal";
import { useCallback, useState } from "react";
import PlusIcon from "@/assets/white-plusicon.png";
import Buttonwithicon from "@/components/ui/buttonwithicon";
import CouponAddModal from "../components/CouponAddModal";

function PromotionalOffers() {
  const {
    data,
    isPending: loading,
    refetch,
  } = useQuery({
    queryKey: ["coupons"],
    queryFn: fetchPromotionalOffersApi,
  });
  const [selectedCoupon, setSelectedCoupon] = useState("");
  const [isOpen, setIsOpen] = useState({ add: false, delete: false });
  const onOpen = (key: keyof typeof isOpen) =>
    setIsOpen({ ...isOpen, [key]: true });

  const onClose = (key: keyof typeof isOpen) =>
    setIsOpen({ ...isOpen, [key]: false });

  const handleDelete = useCallback((couponId: string) => {
    setSelectedCoupon(couponId);
    onOpen("delete");
  }, []);
  return (
    <>
        <div className="w-full mt-5">
          <div className="flex justify-end mb-5">
            <Buttonwithicon
              onClick={() => onOpen("add")}
              variant="button"
              icon={PlusIcon}
              text="Add new"
            />
          </div>
          <div className="flex justify-between px-4 mb-2">
            <h1>Code</h1>
            <h1>Discount offer</h1>
            <h1></h1>
          </div>
          <div className="flex flex-col gap-y-2">
            {loading
              ? [1, 2, 3].map((_, i) => <CouponSkelton key={i} />)
              : data?.coupons.map(
                  (item: { code: string; discount: number; _id: string }) => (
                    <CouponCard
                      _id={item._id}
                      code={item.code}
                      key={item.code}
                      discount={item.discount}
                      handleDelete={handleDelete}
                    />
                  )
                )}
            {data?.coupons.length === 0 && (
              <div className="py-8 text-center">
                <p className="text-gray-500">No coupons found</p>
              </div>
            )}
          </div>
        </div>
      {isOpen.delete && (
        <CouponDeleteModal
          couponId={selectedCoupon}
          isOpen={isOpen.delete}
          onClose={() => onClose("delete")}
          refetch={refetch}
        />
      )}
      {isOpen.add && (
        <CouponAddModal
          isOpen={isOpen.add}
          onClose={() => onClose("add")}
          refetch={refetch}
        />
      )}
    </>
  );
}

export default PromotionalOffers;
