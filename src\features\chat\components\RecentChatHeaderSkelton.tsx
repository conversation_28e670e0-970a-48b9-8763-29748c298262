import { Skeleton } from "@/components/ui/skeleton";

function RecentChatHeaderSkelton() {
  return (
    <div className="flex justify-between p-4">
      <div className="flex grow gap-x-2">
        {/* Profile Image */}
        <div className="flex-shrink-0 w-12 h-12 overflow-hidden rounded-full">
          <Skeleton className="w-full h-full rounded-full" />
        </div>

        {/* Name and Last Message */}
        <div className="flex flex-col gap-y-2">
          <Skeleton className="w-32 h-4" />
          <Skeleton className="w-40 h-3 md:w-60" />
        </div>
      </div>

      {/* Unread Count Badge */}
      <div className="min-w-5 h-5 max-w-[50px] my-auto rounded-full">
        <Skeleton className="w-10 h-5 rounded-full" />
      </div>
    </div>
  );
}

export default RecentChatHeaderSkelton;
