import { ThinStar, Rating } from "@smastrom/react-rating";

import "@smastrom/react-rating/style.css";
function StarRating({
  rating,
  setRating,
  className,
}: {
  className?: string;
  rating: number;
  setRating: React.Dispatch<React.SetStateAction<number>>;
}) {
  return (
    <Rating
      value={rating}
      className={`${className} max-w-[200px] gap-x-2 m-auto`}
      onChange={setRating}
      itemStyles={{
        itemShapes: ThinStar,
        activeFillColor: "#D1795E",
        inactiveFillColor: "#ffedd5",
      }}
    />
  );
}

export default StarRating;
