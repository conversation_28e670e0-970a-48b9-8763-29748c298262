import { ScheduledCallsCardItem } from "../type";
import StarIcon from "@/assets/star.svg";
import LocationIcon from "@/assets/location.png";
import CalendarIcon from "@/assets/calendar.svg";
import { useState } from "react";
import CertificationModal from "./CertificationModal";
import { showToast } from "@/lib/toast";
import ProivderRefundPolicyModal from "./ProivderRefundPolicyModal";
import ServiceAndCallStatus from "./ServiceAndCallStatus";
import CancelModal from "@/components/ui/cancel-modal";

function ScheduledCallsCard({ data }: { data: ScheduledCallsCardItem }) {
  const [isOpen, setIsOpen] = useState({
    certification: false,
    refund: false,
    cancel: false,
  });
  const onOpen = (modal: keyof typeof isOpen) => {
    setIsOpen((prev) => ({ ...prev, [modal]: true }));
  };
  const onClose = (modal: keyof typeof isOpen) => {
    setIsOpen((prev) => ({ ...prev, [modal]: false }));
  };
  return (
    <>
      <div className="container p-4 border rounded-lg border-gray-2 ">
        <div className="flex gap-x-3">
          <img
            src={data.provider?.profilePicture[0]?.url || ""}
            alt="profile"
            className="w-16 h-16 rounded-full md:w-20 md:h-20"
          />
          <div>
            <div className="flex gap-x-2">
              <h1 className="text-md md:text-lg ">
                {data.provider.firstName} {data.provider.lastName}
              </h1>
              <div className="flex gap-x-1">
                <img src={StarIcon} alt="rating" className="w-5 h-5 my-auto" />
                <p className="my-auto">{data.provider.totalRating}</p>
              </div>
            </div>
            <div className="flex flex-col mt-1 md:flex-row">
              <h1 className="text-sm text-neutral-600 md:text-base">
                Specialty in {data.provider.specialty}
              </h1>
              <div className="hidden w-1 h-1 mx-2 my-auto bg-black rounded-full md:block"></div>
              <h5
                onClick={() => onOpen("certification")}
                className="text-sm cursor-pointer text-link md:text-base"
              >
                view certifications
              </h5>
            </div>
            {/* Location and Experience */}
            <div className="flex mt-3 gap-x-2">
              <div className="flex gap-x-1">
                <img
                  src={LocationIcon}
                  alt="location"
                  className="w-4 h-4 my-auto "
                />
                <p className="my-auto text-sm text-neutral-600 md:text-base">
                  {data.provider.serviceLocation[0]}
                </p>
              </div>
              <div className="flex gap-x-1">
                <img
                  src={CalendarIcon}
                  alt="calendar"
                  className="w-4 h-4 my-auto"
                />
                <p className="my-auto text-sm text-neutral-600 md:text-base">
                  {data.provider.experience} yrs exp
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="flex flex-col justify-between mt-3 md:flex-row">
          <ServiceAndCallStatus
            openRefundPolicy={() => onOpen("refund")}
            status={data.status}
            date={data.bookedAt}
          />
          {data.status !== "cancelled" && (
            <button
              onClick={() => onOpen("cancel")}
              className="self-end mt-2 text-sm underline md:self-auto md:mt-0"
            >
              Cancel
            </button>
          )}
        </div>
      </div>
      {isOpen.certification && (
        <CertificationModal
          isOpen={isOpen.certification}
          onClose={() => onClose("certification")}
          providerName={`${data.provider.firstName} ${data.provider.lastName}`}
          certifications={data.provider.certifications}
        />
      )}
      {isOpen.cancel && (
        <CancelModal
          isOpen={isOpen.cancel}
          onClose={() => onClose("cancel")}
          handleConfirm={() => {
            showToast("Call cancelled successfully", "success");
          }}
        />
      )}
      {isOpen.refund && (
        <ProivderRefundPolicyModal
          isOpen={isOpen.refund}
          onClose={() => onClose("refund")}
          refundPolicy={data.provider.refundPolicy}
        />
      )}
    </>
  );
}

export default ScheduledCallsCard;
