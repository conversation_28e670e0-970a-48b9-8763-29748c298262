export type Product = {
  _id: string;
  images: { image: string; key: string }[];
  title: string;
  seller: { companyName: string };
  price: string;
  totalRating: number;
};

export type ProductQueryParams = {
  page?: number;
  category?: string;
  minPrice?: string;
  maxPrice?: string;
  brand?: string;
  search?: string;
};

export type FilterProps = {
  selectedCategories: string[];
  selectedBrands: string[];
  priceRange: number[];
  removeCategory: (category: string) => void;
  addCategory: (value: string) => void;
  addBrand: (value: string) => void;
  removeBrand: (brand: string) => void;
  setPriceRange: (value: number[]) => void;
  clearFilter?: () => void;
};

export type CartApiPayload = {
  productId: string;
  count: number;
  type: "inc" | "dec";
};

export type ProductReviewCardProps = {
  _id?: string;
  name: string;
  rating: number;
  content: string;
};
