import PageLayout from "@/components/layout/PageLayout";
import Banner from "../components/Banner";
import ProductSection from "../components/ProductSection";
import ServiceSection from "../components/ServiceSection";
import QARecomment from "../components/QARecomment";
import PartnerWithUsSection from "../components/PartnerWithUsSection";
import { useState } from "react";
import CarePlanModal from "../components/CarePlanModal";

function Home() {
  const currentRole = localStorage.getItem("nurtureUser");
  const hasCompletedQuiz = JSON.parse(
    localStorage.getItem("hasCompletedQuiz") as string
  );
  const [isOpen, setIsOpen] = useState(true);
  const showCarePlan = localStorage.getItem("showCarePlan") === "true";
  const onClose = () => {
    localStorage.setItem("showCarePlan", "false");
    setIsOpen(false);
  };
  return (
    <PageLayout>
      <>
        <Banner />
        {!hasCompletedQuiz && currentRole === "customer" && <QARecomment />}
        <div className="flex flex-col gap-y-20">
          <ProductSection />
          <ServiceSection />
          <PartnerWithUsSection />
        </div>
        {showCarePlan && <CarePlanModal isOpen={isOpen} onClose={onClose} />}
      </>
    </PageLayout>
  );
}

export default Home;
