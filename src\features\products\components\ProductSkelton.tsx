import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

function ProductSkelton({ className }: { className?: string }) {
  return (
    <div
      className={cn(
        className,
        "border rounded-xl p-3 w-[240px]  md:w-[300px] space-y-3 gap-x-3"
      )}
    >
      {/* Image Skeleton */}
      <Skeleton className="w-full h-48 rounded-lg" />

      {/* Textual Content */}
      <div className="space-y-1">
        <Skeleton className="w-3/4 h-4" /> {/* Product title */}
        <Skeleton className="w-1/2 h-3" /> {/* Brand */}
        <Skeleton className="w-16 h-5" /> {/* Price */}
      </div>

      {/* Favorite Icon (e.g., star) */}
      <div className="flex justify-end">
        <Skeleton className="w-5 h-5 rounded-full" />
      </div>
    </div>
  );
}

export default ProductSkelton;
