import Joi from "joi";

export const addressSchema = Joi.object({
  address: Joi.string().min(5).max(255).required().messages({
    "string.base": `"address" should be a type of 'text'`,
    "string.empty": `"address" cannot be an empty field`,
    "string.min": `"address" should have a minimum length of {#limit}`,
    "any.required": `"address" is a required field`,
  }),

  zipCode: Joi.string()
    .pattern(/^[A-Za-z0-9][A-Za-z0-9\s\-]{2,10}$/)
    .required()
    .messages({
      "string.pattern.base": `"zipcode" must be a valid international postal code`,
      "any.required": `"zipcode" is a required field`,
    }),

  city: Joi.string()
    .pattern(/^[a-zA-Z\s]+$/)
    .min(2)
    .max(100)
    .required()
    .messages({
      "string.pattern.base": `"city" must only contain letters and spaces`,
      "string.empty": `"city" cannot be an empty field`,
      "any.required": `"city" is a required field`,
    }),

  state: Joi.string()
    .pattern(/^[a-zA-Z\s]+$/)
    .min(2)
    .max(100)
    .required()
    .messages({
      "string.pattern.base": `"state" must only contain letters and spaces`,
      "string.empty": `"state" cannot be an empty field`,
      "any.required": `"state" is a required field`,
    }),
});
