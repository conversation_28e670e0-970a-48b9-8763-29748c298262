import banner from "@/assets/banner.png";
import NurtureSymbol from "@/assets/white-nurture-symbol.png";
import VectorIcon from "@/assets/vector.png";
import StarIcon from "@/assets/star.png";
function Banner() {
  return (
    <div
      className="flex justify-center w-full md:h-[400px] h-[250px]"
      style={{
        backgroundImage: `url(${banner})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        width: "100%",
      }}
    >
      <div className="flex flex-col items-center justify-center gap-y-6 md:gap-y-10">
        <div className="flex gap-x-2">
          <img
            src={NurtureSymbol}
            className="h-6 w-6 md:h-12 md:w-12"
            alt="mother-love-embracing-her-newborn-baby-girl"
          />
          <h1 className="text-white font-prettywise md:text-2xl my-auto">
            nurture postnatal care
          </h1>
        </div>
        <div className="flex justify-center gap-x-2">
          <img
            src={VectorIcon}
            className="my-auto"
            alt="mother-love-embracing-her-newborn-baby-girl"
          />
          <img
            src={StarIcon}
            className="h-6 w-6"
            alt="mother-love-embracing-her-newborn-baby-girl"
          />

          <img
            className="my-auto"
            src={VectorIcon}
            alt="mother-love-embracing-her-newborn-baby-girl"
          />
        </div>
        <div>
          <h1 className="font-prettywise text-white text-2xl md:text-4xl text-center">
            Nurturing care, for <br /> every step ahead.
          </h1>
        </div>
      </div>
    </div>
  );
}

export default Banner;
