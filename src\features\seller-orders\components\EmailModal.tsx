import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import TextEditor from "@/components/ui/text-editor";
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";
import { sendEmailApi } from "../api";
import { showToast } from "@/lib/toast";
import { useParams } from "react-router-dom";

function EmailModal({
  heading,
  subheading,
  type,
  isOpen,
  onClose,
  refetch,
}: {
  heading: string;
  subheading: string;
  type: string;
  isOpen: boolean;
  onClose: () => void;
  refetch: () => void;
}) {
  const { id } = useParams();
  const [content, setContent] = useState("");
  const [error, setError] = useState("");
  const handleContentChange = (value: string) => {
    setContent(value);
  };
  const handleClose = () => {
    setContent("");
    onClose();
  };
  const { mutate, isPending: loading } = useMutation({
    mutationFn: sendEmailApi,
    onSuccess: () => {
      showToast("Email sent successfully", "success");
      handleClose();
      refetch();
    },
    onError: () => {
      showToast("Failed to send email", "error");
    },
  });
  const handleSend = () => {
    if (loading) return;
    setError("");
    if (content.trim().length < 20) {
      setError("Minimum 20 characters required for email");
      return;
    }
    const updatedType =
      type === "delay"
        ? "delay"
        : type === "return requested"
          ? "return"
          : "exchange";
    mutate({ orderId: id as string, email: content, actionType: updatedType });
  };

  return (
    <Modal
      restrictClose={type === "delay" ? false : true}
      isOpen={isOpen}
      onClose={handleClose}
      className="lg:min-w-[550px]"
    >
      <div>
        <header className="flex flex-col gap-y-1">
          <h1 className="text-lg font-semibold">{heading}</h1>
          <h5 className="text-neutral-300">{subheading}</h5>
        </header>
        <div className="mt-5 mb-2">
          <h5 className="text-neutral-300">Type email content here</h5>
        </div>
        {error && <p className="text-sm text-red-500">{error}</p>}
        <TextEditor
          value={content}
          className="h-[230px] md:h-[280px] rounded-md"
          onChange={handleContentChange}
        />
        <Button
          disabled={loading}
          onClick={handleSend}
          className="w-full mt-20 md:mt-16 error:border-red-500"
        >
          {loading ? "Sending..." : "Send"}
        </Button>
      </div>
    </Modal>
  );
}

export default EmailModal;
