import { fetchRefreshToken<PERSON>pi, logout<PERSON>pi } from "@/api";
import {
  connectNotificationSocket,
  connectSocket,
  disconnectNotificationSocket,
  disconnectSocket,
} from "@/lib/socket";
import { Socket } from "socket.io-client";

import { showToast } from "@/lib/toast";
import { create } from "zustand";

type User = {
  accessToken: string;
  role: string;
  socket?: {
    chat: Socket | null;
    notification: Socket | null;
  };
};
type AuthStore = {
  user: User | null;
  login: (user: Omit<User, "socket">) => void;
  updateUser: (user: Omit<User, "socket">) => void;
  logout: (role?: string) => Promise<void>;
  setAccesstoken: () => Promise<string>;
  authReset: () => void;
};

export const useAuthStore = create<AuthStore>((set, get) => ({
  user: null,
  login: (user: Omit<User, "socket">) => {
    const chatSocket = connectSocket(user.accessToken);
    const notificationSocket = connectNotificationSocket(user.accessToken);
    set({
      user: {
        ...user,
        socket: { chat: chatSocket, notification: notificationSocket },
      },
    });
    localStorage.setItem("nurtureUser", user.role);
  },
  updateUser: ({ accessToken, role }: Pick<User, "accessToken" | "role">) => {
    const chatSocket = connectSocket(accessToken);
    const notificationSocket = connectNotificationSocket(accessToken);

    set({
      user: {
        ...get().user,
        accessToken,
        role,
        socket: { chat: chatSocket, notification: notificationSocket },
      },
    });
  },
  authReset: () => {
    showToast("Session expired. Please login again.", "error");
    localStorage.removeItem("nurtureUser");
    localStorage.removeItem("hasCompletedQuiz");
    localStorage.removeItem("showCarePlan");
    set({ user: null });
  },
  logout: async () => {
    try {
      await logoutApi();
      const role = localStorage.getItem("nurtureUser") as string;
      if (role === "customer") {
        window.location.href = "/";
      } else if (role === "seller") {
        window.location.href = "/seller/login";
      } else if (role === "provider") {
        window.location.href = "/provider/login";
      } else if (role === "admin") {
        window.location.href = "/admin/login";
      }
      localStorage.removeItem("nurtureUser");
      localStorage.removeItem("hasCompletedQuiz");
      localStorage.removeItem("showCarePlan");
      disconnectSocket();
      disconnectNotificationSocket();
      set(() => ({ user: null }));
    } catch (error) {
      console.log(error, "error");
    }
  },
  setAccesstoken: async () => {
    try {
      const role = localStorage.getItem("nurtureUser") as string;
      const { data } = await fetchRefreshTokenApi(role);
      const chatSocket = connectSocket(data.accessToken);
      const notificationSocket = connectNotificationSocket(data.accessToken);
      set({
        user: {
          accessToken: data.accessToken,
          role,
          socket: { chat: chatSocket, notification: notificationSocket },
        },
      });
      return data.accessToken;
    } catch {
      return "";
    }
  },
}));
