import { Skeleton } from "@/components/ui/skeleton";

function ProductDetailSkelton() {
  return (
    <div className="flex flex-col justify-between p-3 border rounded-lg gap-y-2 border-tints-50">
      <div className="flex justify-between">
        <div className="flex gap-x-2">
          {/* Image placeholder */}
          <Skeleton className="w-12 h-12 rounded-md" />

          <div className="flex flex-col gap-y-1">
            {/* Title placeholder */}
            <Skeleton className="w-32 h-6 rounded-md" />
            {/* Quantity placeholder */}
            <Skeleton className="w-24 h-4 rounded-md" />
          </div>
        </div>

        {/* Price placeholder */}
        <Skeleton className="w-16 h-6 rounded-md" />
      </div>

      {/* Description placeholder */}
      <Skeleton className="mt-2 h-4 w-full rounded-md lg:w-[500px]" />
    </div>
  );
}

export default ProductDetailSkelton;
