import { Card, CardContent } from "@/components/ui/card";

interface StatsCardProps {
  title: string;
  value: string | number;
  prefix?: string;
}

function StatsCard({ title, value, prefix }: StatsCardProps) {
  return (
    <Card className="border border-gray-2 h-[175px] shadow-none">
      <CardContent className="flex flex-col h-full items-center justify-center p-6">
        <div className="text-4xl font-semibold mb-2">
          {prefix && <span>{prefix}</span>}
          {value}
        </div>
        <p className="text-gray-500">{title}</p>
      </CardContent>
    </Card>
  );
}

export default StatsCard;
