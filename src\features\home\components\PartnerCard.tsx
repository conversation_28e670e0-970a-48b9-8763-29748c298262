import Buttonwithicon from "@/components/ui/buttonwithicon";

interface PartnerCardProps {
  image: string;
  imageAlt: string;
  description: string;
  buttonText: string;
  linkTo: string;
}

function PartnerCard({
  image,
  imageAlt,
  description,
  linkTo,
  buttonText,
}: PartnerCardProps) {
  return (
    <div className="flex-1 bg-warmth rounded-lg overflow-hidden">
      <div className="relative">
        <img
          src={image}
          alt={imageAlt}
          className="w-full h-auto object-cover"
        />
      </div>
      <div className="p-6 md:p-8">
        <p className="text-center md:text-left text-xl md:text-2xl text-white	 mb-6">
          {description}
        </p>
        <div className="mx-auto">
        <Buttonwithicon classname="mx-auto" href={linkTo} variant="white" text={buttonText} />
        </div>
      </div>
    </div>
  );
}

export default PartnerCard;
