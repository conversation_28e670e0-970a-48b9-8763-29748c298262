import { Navigate } from "react-router-dom";
import AuthLayout from "../components/AuthLayout";
import RegisterForm from "../components/RegisterForm";
import { checkAuthentication } from "@/lib/utils";

function ProviderRegister() {
  const { isLoggedIn, href } = checkAuthentication();

  return isLoggedIn ? (
    <Navigate to={href} />
  ) : (
    <AuthLayout>
      <RegisterForm />
    </AuthLayout>
  );
}

export default ProviderRegister;
