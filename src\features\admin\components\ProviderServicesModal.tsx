import { Modal } from "@/components/ui/modal";
import { fetchProviderServicesApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import CloseIcon from "@/assets/close-icon.svg";
import GoBackIcon from "@/assets/go-back.svg";
import { Service } from "../type";
import DynamicPagination from "@/components/ui/dynamic-pagination";
import { useState } from "react";
import ServiceCardSkelton from "./ServiceCardSkelton";
import ServiceDetailCard from "./ServiceDetailCard";

function ProviderServicesModal({
  isOpen,
  onClose,
  selectedProviderId,
  onOpenServiceDetails,
  openProviderDetails,
  handleService,
}: {
  isOpen: boolean;
  selectedProviderId: string;
  onOpenServiceDetails: () => void;
  openProviderDetails: () => void;
  onClose: () => void;
  handleService: (serviceId: string) => void;
}) {
  const [currentPage, setCurrentPage] = useState(1);
  let totalPages = 1;

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleOpenServiceDetails = (serviceId: string) => {
    handleService(serviceId);
    onOpenServiceDetails();
    onClose();
  };

  const handleGoBack = () => {
    openProviderDetails();
    onClose();
  };

  const {
    data,
    isPending: loading,
    isSuccess,
  } = useQuery({
    queryKey: ["provider-services", selectedProviderId, currentPage],
    queryFn: () => fetchProviderServicesApi(selectedProviderId, currentPage),
    enabled: !!selectedProviderId && isOpen,
  });

  if (isSuccess) {
    totalPages = data?.totalPages || 1;
  }

  return (
    <Modal className="lg:min-w-[650px]" isOpen={isOpen} onClose={onClose}>
      <div>
        <div className="flex items-center justify-between mb-6">
          <div className="flex cursor-pointer gap-x-4" onClick={handleGoBack}>
            <img
              
              className="transition-opacity hover:opacity-70"
              src={GoBackIcon}
              alt="go back"
            />
            <h1 className="text-gray-900">
              {isSuccess && data?.services?.length > 1
                ? `${data?.services.length} services`
                : `${data?.services?.length || 0} service`}
            </h1>
          </div>
          <img
            onClick={onClose}
            className="transition-opacity cursor-pointer hover:opacity-70"
            src={CloseIcon}
            alt="close"
          />
        </div>

        <div>
          {loading ? (
            <div className="flex flex-col gap-y-5">
              {[1, 2, 3].map((_, index) => (
                <ServiceCardSkelton key={index} />
              ))}
            </div>
          ) : (
            <>
              <div className="flex flex-col gap-y-5 max-h-[75vh] overflow-y-scroll scrollbar-hide">
                {isSuccess && data?.services?.length > 0 ? (
                  data.services.map((service: Service) => (
                    <div
                      key={service._id}
                      onClick={() => handleOpenServiceDetails(service._id)}
                      className="cursor-pointer"
                    >
                      <ServiceDetailCard service={service} />
                    </div>
                  ))
                ) : (
                  <div className="py-8 text-center">
                    <p className="text-gray-500">No services found</p>
                  </div>
                )}
              </div>
              {totalPages > 1 && (
                <DynamicPagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={handlePageChange}
                />
              )}
            </>
          )}
        </div>
      </div>
    </Modal>
  );
}

export default ProviderServicesModal;
