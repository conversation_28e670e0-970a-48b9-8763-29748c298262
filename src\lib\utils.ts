import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { questionnaireData } from "./constant";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const navigateLoggedInUser = () => {
  const user = localStorage.getItem("nurtureUser");
  let href = "/";
  switch (user) {
    case "admin":
      href = "/admin/sellers";
      break;
    case "seller":
      href = "/seller/dashboard";
      break;
    case "provider":
      href = "/provider/dashboard";
      break;
    case "customer":
      href = "/account/basic-details";
      break;
  }
  return href;
};

export const checkAuthentication = () => {
  let href = "/";
  const user = localStorage.getItem("nurtureUser");
  if (!user) {
    return { isLoggedIn: false, href, role: user };
  }

  switch (user) {
    case "admin":
      href = "/admin/sellers";
      break;
    case "seller":
      href = "/seller/dashboard";
      break;
    case "provider":
      href = "/provider/dashboard";
      break;
    case "user":
      href = "/";
      break;
  }

  return { isLoggedIn: true, href, role: user };
};

export const dateFormatter = (isoDate: string) => {
  const date = new Date(isoDate);
  const options: Intl.DateTimeFormatOptions = {
    day: "numeric",
    month: "short",
    year: "numeric",
  };
  const formattedDate = date.toLocaleDateString("en-GB", options);

  return formattedDate === "Invalid Date" ? "" : formattedDate;
};

export const timeFormatter = (dateInput: string): string => {
  const inputDate = new Date(dateInput);
  const hours = String(inputDate.getHours()).padStart(2, "0");
  const minutes = String(inputDate.getMinutes()).padStart(2, "0");
  return `${hours}:${minutes}`;
};

export const formatQuestionAire = (answers: Record<string, Array<string>>) => {
  const formattedAnswers = Object.entries(answers).map((answer) => {
    const [key, value] = answer;
    const [phaseIndex, questionIndex] = key.split("-");
    const question =
      questionnaireData[Number(phaseIndex)].questions[Number(questionIndex)];
    return {
      question: question.question,
      answer: value,
    };
  });
  formattedAnswers.pop(); // removing the last question

  return {
    personal_details: [],
    postnatal_assessment_questions: formattedAnswers,
  };
};
