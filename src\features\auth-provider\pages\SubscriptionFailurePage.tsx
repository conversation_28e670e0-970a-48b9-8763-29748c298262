import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { AlertCircle } from "lucide-react";
import{ Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default function SubscriptionFailurePage() {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-gray-50">
      <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md">
        <div className="flex flex-col items-center text-center">
          <AlertCircle className="w-16 h-16 mb-4 text-red-500" />
          <h1 className="text-2xl font-bold text-gray-900">Payment Incomplete</h1>
          <p className="mt-2 text-gray-600">
            Your subscription payment was not completed. You can try again or choose a different plan.
          </p>
        </div>

        <Alert variant="destructive" className="mt-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Payment Cancelled</AlertTitle>
          <AlertDescription>
            The payment process was cancelled or interrupted before completion.
          </AlertDescription>
        </Alert>

        <div className="flex flex-col gap-4 mt-8">
          <Button
            onClick={() => navigate("/provider/setup-profile/plan-selection")}
            className="w-full"
          >
            Try Again
          </Button>
          <Button
            variant="outline"
            onClick={() => navigate("/provider/dashboard")}
            className="w-full"
          >
            Go to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
}
