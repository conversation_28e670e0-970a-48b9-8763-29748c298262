import { useState } from "react";
import FingerPrintImage from "@/assets/fingerprint.png";
import { Button } from "@/components/ui/button";
import { Link, useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
import { verifyOtpApi } from "../api";
import { useMutation } from "@tanstack/react-query";
import { showToast } from "@/lib/toast";
import { AxiosError } from "axios";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS } from "input-otp";
function OtpForm() {
  const location = useLocation();
  const navigate = useNavigate();
  const state = location.state as { email: string };
  const [otp, setOtp] = useState("");

  const handleChange = (value: string) => {
    setOtp(value);
  };

  const { mutate, isPending } = useMutation({
    mutationFn: verifyOtpApi,
    onSuccess: () => {
      navigate("/seller/setup-profile", {
        state: {
          loginViaGoogle: false,
        },
      });
    },
    onError: (error: AxiosError) => {
      if (error.response?.status === 401) {
        showToast("Invalid OTP", "error");
        return;
      }
      showToast("Something went wrong", "error");
    },
  });
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (otp.length !== 6) return;
    mutate({ otp, email: state.email });
  };
  return (
    <div className="flex flex-col items-center justify-center w-full max-w-md mx-auto space-y-6 text-center md:mx-0">
      <img
        src={FingerPrintImage}
        alt="Fingerprint"
        className="object-cover mx-auto"
      />
      <h1 className="text-3xl font-bold font-prettywise">Verify Email</h1>
      <p className="text-gray-500">
        Please enter the 6 digit OTP that we've sent to your email
        <br />
        <span className="font-semibold">{state?.email}</span>
      </p>
      <form className="flex flex-col gap-y-8" onSubmit={handleSubmit}>
        <InputOTP
          onChange={(value) => handleChange(value)}
          maxLength={6}
          value={otp}
          pattern={REGEXP_ONLY_DIGITS}
        >
          <InputOTPGroup className="flex justify-center gap-x-3">
            {Array.from({ length: 6 }).map((_, index) => (
              <InputOTPSlot
                className="md:w-12 md:h-12"
                key={index}
                index={index}
              />
            ))}
          </InputOTPGroup>
        </InputOTP>
        <Button disabled={isPending} className="w-full">
          Verify email
        </Button>
      </form>
      <Link
        to="/seller/register"
        className="flex items-center gap-2 font-semibold"
      >
        <span>&larr;</span> Go back
      </Link>
    </div>
  );
}

export default OtpForm;
