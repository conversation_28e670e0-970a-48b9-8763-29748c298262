import React from "react";
import DataTable from "@/components/ui/data-table/DataTable";
import { Column } from "@/components/ui/data-table/types";
import { Provider } from "../type";
import CheckedIcon from "@/assets/white-checked-icon.png";
import CloseIcon from "@/assets/close-orange.png";
import { IoEllipsisVertical } from "react-icons/io5";
import { Button } from "@/components/ui/button";

interface ProviderTableProps {
  data: Provider[];
  currentPage: number;
  totalPages: number;
  focus: string;
  loading: boolean;
  onPageChange: (page: number) => void;
  handleProviderApprove: (providerId: string, isApproved: boolean) => void;
  onOpen: (id: string) => void;
}

const ProviderTable: React.FC<ProviderTableProps> = ({
  data,
  currentPage,
  totalPages,
  focus,
  loading,
  onPageChange,
  handleProviderApprove,
  onOpen,
}) => {
  const columns: Column<Provider>[] = [
    {
      header: "Name & email",
      accessorKey: "name",
      className: "w-[250px]",
      cell: (provider) => (
        <div className="flex items-center gap-2">
          {provider.profilePicture[0].url && (
            <img
              src={provider.profilePicture[0].url}
              alt={provider.firstName}
              className="object-cover w-8 h-8 rounded-full"
            />
          )}
          <div>
            <div>{provider.firstName}</div>
            <div className="text-sm text-gray-500">{provider.authId.email}</div>
          </div>
        </div>
      ),
    },
    {
      header: "Specialty",
      accessorKey: "specialty",
      cell: (provider) => (
        <p className="text-sm text-neutral-600">{provider.specialty}</p>
      ),
    },
    {
      header: "Phone number",
      accessorKey: "phoneNumber",
      cell: (provider) => (
        <p className="text-sm text-neutral-600">{provider.phone}</p>
      ),
    },
    {
      header: "Business name",
      accessorKey: "businessName",
      cell: (provider) => (
        <p className="text-sm text-neutral-600">{provider.businessName}</p>
      ),
    },
    {
      header: "Tax ID",
      accessorKey: "taxId",
      cell: (provider) => (
        <p className="text-sm text-neutral-600">{provider.taxId}</p>
      ),
    },
    {
      header: "Certifications",
      accessorKey: "certifications",
      cell: (provider) =>
        `${provider.certifications.length > 1 ? provider.certifications.length + " files" : provider.certifications.length + " file"}`,
    },
    {
      header: "Preferred location",
      accessorKey: "location",
      cell: (provider) => (
        <p className="text-sm text-neutral-600">{provider.serviceLocation}</p>
      ),
    },
    {
      header: "Video",
      accessorKey: "video",
      cell: () => `1 file`,
    },
    {
      header: "",
      accessorKey: "actions",
      className: "w-[50px]",
      cell: (provider) =>
        focus === "pending" ? (
          <div className="flex gap-x-2">
            <Button
              variant={"outline"}
              className="p-4"
              onClick={() => handleProviderApprove(provider._id, false)}
            >
              <img src={CloseIcon} alt="checked" className="min-h-5 min-w-5" />
            </Button>
            <Button
              className="p-4"
              onClick={() => handleProviderApprove(provider._id, true)}
            >
              <img
                src={CheckedIcon}
                alt="checked"
                className="min-h-6 min-w-6"
              />
            </Button>
          </div>
        ) : (
          <IoEllipsisVertical
            onClick={() => onOpen(provider._id)}
            className="w-5 h-5 cursor-pointer"
          />
        ),
    },
  ];

  return (
    <DataTable
      data={data}
      columns={columns}
      currentPage={currentPage}
      totalPages={totalPages}
      loading={loading}
      onPageChange={onPageChange}
    />
  );
};

export default ProviderTable;
