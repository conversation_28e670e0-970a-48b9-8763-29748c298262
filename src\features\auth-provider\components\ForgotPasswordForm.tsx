import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import FingerPrintImage from "@/assets/fingerprint.png";
import { Link, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { joi<PERSON><PERSON>olver } from "@hookform/resolvers/joi";
import { useMutation } from "@tanstack/react-query";

import { forgotPasswordPayload } from "../type";
import { forgotPasswordApi } from "../api";
import { forgotPasswordSchema } from "../validation";
import { useState } from "react";
import { showToast } from "@/lib/toast";

function ForgotPasswordForm() {
  const [email, setEmail] = useState<string>("");
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<forgotPasswordPayload>({
    resolver: joiResolver(forgotPasswordSchema),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: forgotPasswordApi,
    onSuccess: () => {
      navigate("/provider/forgot-password/check-email", {
        state: {
          email,
        },
      });
    },
    onError: () => {
      showToast("Something went wrong", "error");
    },
  });

  const onSubmit = (data: forgotPasswordPayload) => {
    setEmail(data.email);
    mutate(data);
  };

  return (
    <div className="w-full max-w-md space-y-6 text-center">
      <div className="space-y-2">
        <Link to={"/"}>
          <img
            src={FingerPrintImage}
            alt="Fingerprint"
            className="object-cover mx-auto"
          />
        </Link>
        <h1 className="text-3xl font-bold md:text-4xl font-prettywise">
          Forgot Password
        </h1>
        <p className="text-gray-500">
          Enter your email and we'll send you a link to reset your password
        </p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-4 text-left">
          {/* Email Input */}
          <div>
            <Input
              className="border-input-border focus:border-slate-300"
              placeholder="Email"
              type="email"
              {...register("email")}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
          </div>
        </div>

        <div className="flex flex-col mt-6 space-y-2 md:mt-12 gap-y-3 md:gap-y-1">
          <Button
            type="submit"
            className="w-full p-2 hover:bg-[#c65a3c]"
            disabled={isPending}
          >
            {isPending ? "Sending..." : "Send reset link"}
          </Button>
        </div>
      </form>
      <Link
        to="/provider/login"
        className="flex items-center justify-center gap-2 font-semibold"
      >
        <span>&larr;</span> Back to login
      </Link>
    </div>
  );
}

export default ForgotPasswordForm;
