import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import GoogleButton from "@/components/ui/google-button";
import { useState } from "react";
import FingerPrintImage from "@/assets/fingerprint.png";
import { Link, useNavigate } from "react-router-dom";
import { LuEye } from "react-icons/lu";
import { PiEyeSlash } from "react-icons/pi";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

import { LoginPayload } from "../type";
import { googleLoginApi, loginApi } from "../api";
import { LoginSchema } from "../validation";
import { useAuthStore } from "@/store/authStore";
import { showToast } from "@/lib/toast";
function LoginForm() {
  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginPayload>({
    resolver: joiResolver(LoginSchema),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: loginApi,
    onSuccess: ({ data }) => {
      login({
        accessToken: data?.accessToken,
        role: "customer",
      });
      localStorage.setItem(
        "hasCompletedQuiz",
        JSON.stringify(data?.hasCompletedQuiz)
      );
      if (data?.auth?.hasCompletedProfile) {
        navigate("/");
      } else {
        localStorage.setItem("userAuthId", data.auth._id);
        navigate("/setup-profile");
      }
    },
    onError: (error: AxiosError) => {
      if (error.response?.status === 401) {
        showToast("Invalid email or password", "error");
      } else {
        showToast("Something went wrong", "error");
      }
    },
  });
  const googleLogin = useMutation({
    mutationFn: googleLoginApi,
    onSuccess: ({ data }) => {
      if (!data.accessToken || !data.auth.hasCompletedProfile) {
        localStorage.setItem("userAuthId", data.auth._id);
        navigate("/setup-profile", {
          state: {
            loginViaGoogle: true,
          },
        });

        return;
      }
      login({ accessToken: data.accessToken, role: "customer" });
      localStorage.setItem(
        "hasCompletedQuiz",
        JSON.stringify(data?.hasCompletedQuiz || false)
      );
      navigate("/");
    },
    onError: (error: AxiosError<Array<{ message?: string }>>) => {
      if (
        error.response?.status === 400 &&
        error.response?.data[0]?.message == "User already exist"
      ) {
        showToast("User already exists", "error");
        return;
      }
      showToast("Something went wrong", "error");
    },
  });
  const onGoogleLogin = (token: string) => {
    googleLogin.mutate(token);
  };
  const onSubmit = (data: LoginPayload) => {
    mutate(data);
  };

  return (
    <div className="w-full max-w-md space-y-6 text-center">
      <div className="space-y-2">
        <Link to={"/"}>
          <img
            src={FingerPrintImage}
            alt="Fingerprint"
            className="object-cover mx-auto"
          />
        </Link>
        <h1 className="text-3xl font-bold md:text-4xl font-prettywise">
          Welcome Back
        </h1>
      </div>

      <GoogleButton
        disabled={googleLogin.isPending}
        onGoogleLogin={onGoogleLogin}
        text="Sign in with Google"
      />

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="px-2 bg-white text-muted-foreground">or</span>
        </div>
      </div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-4 text-left">
          {/* Email Input */}
          <div>
            <Input
              className="border-input-border focus:border-slate-300"
              placeholder="Email"
              type="email"
              {...register("email")}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
          </div>

          {/* Password Input */}
          <div>
            <div className="flex border rounded-sm border-input-border focus:border-slate-300">
              <Input
                className="border-none"
                placeholder="Password"
                type={showPassword ? "text" : "password"}
                {...register("password")}
              />
              {showPassword ? (
                <PiEyeSlash
                  onClick={() => setShowPassword(false)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              ) : (
                <LuEye
                  onClick={() => setShowPassword(true)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              )}
            </div>
            {errors.password && (
              <p className="text-sm text-red-500">{errors.password.message}</p>
            )}
          </div>
        </div>

        <div className="flex justify-end my-4">
          <Link
            to="/forgot-password"
            className="underline text-muted-foreground"
          >
            Forgot password?
          </Link>
        </div>

        <div className="flex flex-col mt-6 space-y-2 md:mt-12 gap-y-3 md:gap-y-1">
          <Button
            type="submit"
            className="w-full p-2 hover:bg-[#c65a3c]"
            disabled={isPending}
          >
            {isPending ? "Logging in..." : "Login"}
          </Button>
        </div>
      </form>
      <div className="flex justify-center gap-x-1">
        <p>Don’t have an account?</p>
        <Link to="/register" className="font-semibold underline text-orange-1">
          Sign up
        </Link>
      </div>
    </div>
  );
}

export default LoginForm;
