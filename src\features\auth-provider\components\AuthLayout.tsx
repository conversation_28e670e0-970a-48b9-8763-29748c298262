import React from "react";
import SignupImage from "@/assets/provider-auth.png";
// Using a placeholder for mobile image - will be replaced later
import MobileSignupImage from "@/assets/provider-auth.png";

interface AuthLayoutProps {
  children: React.ReactNode;
  isStepComponent?: boolean;
}

function AuthLayout({ children, isStepComponent = false }: AuthLayoutProps) {
  return (
    <div className="flex flex-col mb-2 md:mb-0 md:flex-row">
      {/* Left side - Fixed Image */}
      <div className="md:w-1/2 mb-14 md:mb-0 md:sticky md:top-0 md:h-screen">
        {/* Desktop image */}
        <img
          src={SignupImage}
          alt="Provider"
          className="hidden md:block md:h-screen w-auto"
        />
        {/* Mobile image */}
        <img
          src={MobileSignupImage}
          alt="Provider"
          className="w-full md:hidden"
        />
      </div>

      {/* Right side - Content */}
      <div className={`w-full md:w-1/2 flex ${isStepComponent ? 'items-start' : 'items-center'} justify-center`}>
        <div className="w-11/12 mx-auto">{children}</div>
      </div>
    </div>
  );
}

export default AuthLayout;
