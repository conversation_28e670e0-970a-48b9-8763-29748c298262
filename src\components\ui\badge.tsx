import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
        outline: "text-foreground",
        // Order status variants
        "item shipped":
          "border-transparent bg-blue-100 text-blue-800 text-center",
        "item delivered":
          "border-transparent bg-green-100 text-green-800 text-center",
        "order confirmed":
          "border-transparent bg-amber-100 text-amber-800 text-center",
        "return requested":
          "border-transparent bg-red-100 text-red-800 text-center",
        "return approved":
          "border-transparent bg-red-100 text-red-800 text-center",
        "exchange requested":
          "border-transparent bg-purple-100 text-purple-800 text-center",
        "exchange approved":
          "border-transparent bg-purple-100 text-purple-800 text-center",
        "item cancelled":
          "border-transparent bg-primary text-primary-foreground hover:bg-primary/80 text-center",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  );
}

export { Badge, badgeVariants };
