import { create } from "zustand";

type SearchState = {
  search: string;
  setGlobalSearch: (search: string) => void;
  getGlobalSearch: () => string;
  clearGlobalSearch: () => void;
};

export const useGlobalSearchStore = create<SearchState>((set, get) => ({
  search: "",
  setGlobalSearch: (search) => set({ search }),
  getGlobalSearch: () => get().search,
  clearGlobalSearch: () => set({ search: "" }),
}));
