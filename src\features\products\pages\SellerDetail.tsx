import PageLayout from "@/components/layout/PageLayout";
import GoBack from "@/components/ui/go-back";
import { Link, useParams } from "react-router-dom";
import { getSellerDetailsApi, getSellerProductsDetailsApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import ProductSkelton from "../components/ProductSkelton";
import { Product } from "../type";
import ProductCard from "@/components/ui/product-card";

function SellerDetail() {
  const { id } = useParams();
  const sellerDetails = useQuery({
    queryKey: ["seller-info", id],
    queryFn: () => getSellerDetailsApi(id as string),
  });
  const productDetails = useQuery({
    queryKey: ["seller-products", id],
    queryFn: () => getSellerProductsDetailsApi(id as string),
  });
  return (
    <PageLayout>
      <div className="w-11/12 mx-auto">
        <GoBack />
        <div className="flex flex-col w-full mt-5 md:flex-row gap-y-5 md:gap-y-0 md:justify-between md:max-h-[180px] md:min-h-[180px]">
          <div className="flex flex-col md:w-1/3 md:justify-evenly">
            <div>
              <h1 className="text-3xl font-semibold">
                {sellerDetails?.data?.data?.seller?.companyName}
              </h1>
              <div className="flex gap-x-2">
                <span className="text-lg text-neutral-300">
                  {sellerDetails?.data?.data?.seller?.category}
                </span>
                <div className="w-1 h-1 my-auto bg-black rounded-full md:hidden"></div>
                <span className="md:hidden text-neutral-300">
                  {sellerDetails?.data?.data?.productCount} products
                </span>
              </div>
            </div>
            {/* <div className="flex items-center w-full grow"> */}
            <Link to="/chat" className="w-full">
              <Button className="mt-5 md:w-auto" variant={"outline"}>
                Send message
              </Button>
            </Link>
            {/* </div> */}
          </div>
          {/* right */}
          <div className="md:w-1/3">
            <video
              src={sellerDetails?.data?.data?.seller?.introductionVideo?.url}
              className="w-full h-full mt-5 rounded-lg md:mt-0"
              controls
            />
          </div>
        </div>
        {/* product section */}
        <div className="mt-8 md:mt-16">
          <h1 className="text-lg font-semibold">
            {sellerDetails?.data?.data?.productCount} Products
          </h1>
          <div className="grid grid-cols-12 mt-3 gap-x-4 gap-y-5">
            {productDetails.isPending
              ? [1, 2, 3, 4, 5, 6].map((_, i) => (
                  <ProductSkelton
                    className="col-span-12 mx-auto sm:mx-0 sm:col-span-6 md:col-span-4 lg:col-span-3"
                    key={i}
                  />
                ))
              : productDetails.data?.data?.products.map((product: Product) => (
                  <div
                    key={product._id}
                    className="col-span-12  mx-auto sm:mx-0 sm:col-span-6  md:col-span-4 lg:col-span-3 min-w-[180px]  border p-4 rounded-lg border-neutral-40  max-w-[350px] md:max-w-[300px] "
                  >
                    <ProductCard
                      id={product._id}
                      image={product.images[0]}
                      title={product.title}
                      brand={product.seller.companyName}
                      price={product.price}
                      rating={product.totalRating}
                    />
                  </div>
                ))}
          </div>
        </div>
      </div>
    </PageLayout>
  );
}

export default SellerDetail;
