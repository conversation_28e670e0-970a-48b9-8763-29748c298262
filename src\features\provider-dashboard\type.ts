import { PlanName } from "../auth-provider/type";

export type CouponFormData = {
  code: string;
  discount: number;
};

type TimeSlot = {
  start: string;
  end: string;
};

type WeekDay =
  | "monday"
  | "tuesday"
  | "wednesday"
  | "thursday"
  | "friday"
  | "saturday"
  | "sunday";

export type Availability = {
  timeSlot: {
    [day in WeekDay]: TimeSlot[];
  };
};

export type ProviderAvailabilityResponse = {
  availability: Availability;
};

export type SubscriptionPlan = {
  id: string;
  title: PlanName;
  price: number;
  period: string;
  features: { title: string; description: string }[];
};

export type CurrentSubscriptionApiResponse = {
  subscripiton: {
    subscriptionType: string;
    endDate: string | null;
  };
  price: number;
};
