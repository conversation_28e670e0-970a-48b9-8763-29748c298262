import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogTitle, DialogClose } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { X, Plus, Trash2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import Joi from "joi";
import { ServiceHighlight } from "./ServiceDetailsDialog";
import { ServiceDetails } from "../api";

type EditServiceDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: EditServiceFormData) => void;
  initialData?: ServiceDetails | null;
  isLoading?: boolean;
};

export type EditServiceFormData = {
  title: string;
  duration: number;
  highlights: string[];
  price: number;
  description: string;
};

// Joi schema for form validation
const editServiceSchema = Joi.object({
  serviceName: Joi.string().required().trim().messages({
    "any.required": "Service name is required",
    "string.empty": "Service name is required",
    "string.base": "Service name must be a string",
  }),
  duration: Joi.number().required().min(0.5).messages({
    "any.required": "Duration is required",
    "number.base": "Duration must be a number",
    "number.min": "Duration must be at least 0.5 hours",
  }),
  price: Joi.number().required().min(1).precision(2).messages({
    "any.required": "Price is required",
    "number.base": "Price must be a number",
    "number.min": "Price must be at least 1",
  }),
  description: Joi.string().required().min(10).trim().messages({
    "any.required": "Description is required",
    "string.empty": "Description is required",
    "string.base": "Description must be a string",
    "string.min": "Description must be at least 10 characters",
  }),
});

type FormData = {
  serviceName: string;
  duration: number | undefined;
  price: number | undefined;
  description: string;
};

export default function EditServiceDialog({
  open,
  onOpenChange,
  onSubmit,
  initialData,
  isLoading = false,
}: EditServiceDialogProps) {
  const [highlights, setHighlights] = useState<ServiceHighlight[]>([
    { text: "", id: crypto.randomUUID() },
  ]);
  const [highlightsError, setHighlightsError] = useState<string>("");

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<FormData>({
    resolver: joiResolver(editServiceSchema),
    defaultValues: {
      serviceName: "",
      duration: undefined,
      price: undefined,
      description: "",
    },
  });

  // Populate form with initial data when editing
  useEffect(() => {
    if (initialData && open) {
      setValue("serviceName", initialData.title);
      setValue("duration", initialData.duration);
      setValue("price", initialData.price);
      setValue("description", initialData.description);
      
      // Set highlights
      if (initialData.highlights && initialData.highlights.length > 0) {
        const formattedHighlights = initialData.highlights.map((highlight, index) => ({
          text: highlight,
          id: `${initialData._id}-highlight-${index}`,
        }));
        setHighlights(formattedHighlights);
      } else {
        setHighlights([{ text: "", id: crypto.randomUUID() }]);
      }
      
      setHighlightsError("");
    }
  }, [initialData, open, setValue]);

  // Validate highlights on every form submission attempt
  const validateHighlights = () => {
    const validHighlights = highlights.filter((h) => h.text.trim() !== "");
    if (validHighlights.length === 0) {
      setHighlightsError("At least one service highlight is required");
      return false;
    }
    setHighlightsError("");
    return true;
  };

  const handleAddHighlight = () => {
    setHighlights([...highlights, { text: "", id: crypto.randomUUID() }]);
    // Clear error when adding a new highlight
    if (highlightsError) {
      setHighlightsError("");
    }
  };

  const handleRemoveHighlight = (id: string) => {
    if (highlights.length > 1) {
      setHighlights(highlights.filter((highlight) => highlight.id !== id));
    }
  };

  const handleHighlightChange = (id: string, value: string) => {
    setHighlights(
      highlights.map((highlight) =>
        highlight.id === id ? { ...highlight, text: value } : highlight
      )
    );

    // Clear error when user starts typing
    if (value.trim() !== "" && highlightsError) {
      setHighlightsError("");
    }
  };

  const handleFormSubmit = (data: FormData) => {
    // Validate highlights first
    if (!validateHighlights()) {
      return; // Stop submission if highlights validation fails
    }

    // Filter out empty highlights
    const validHighlights = highlights.filter((h) => h.text.trim() !== "").map(h => h.text);

    // Convert form data to API format
    const serviceData: EditServiceFormData = {
      title: data.serviceName,
      duration: data.duration || 0,
      price: data.price || 0,
      description: data.description,
      highlights: validHighlights,
    };

    onSubmit(serviceData);
  };

  // Reset form when dialog closes
  const handleDialogChange = (isOpen: boolean) => {
    if (!isOpen) {
      // Reset form when closing
      reset({
        serviceName: "",
        duration: undefined,
        price: undefined,
        description: "",
      });
      setHighlights([{ text: "", id: crypto.randomUUID() }]);
      setHighlightsError("");
    }
    onOpenChange(isOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogChange}>
      <DialogContent className="sm:max-w-[425px] p-0 gap-0 overflow-hidden">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <DialogTitle className="text-lg font-semibold">
              Edit service details
            </DialogTitle>
            <DialogClose className="rounded-full h-6 w-6 flex items-center justify-center">
              <X className="h-4 w-4" />
            </DialogClose>
          </div>

          <form onSubmit={handleSubmit(handleFormSubmit, () => {
            // This runs when form validation fails
            // Still validate highlights to show error
            validateHighlights();
          })} className="space-y-4">
            {/* Service Name */}
            <div>
              <Input
                placeholder="Service name"
                {...register("serviceName")}
                className="border-input-border focus:border-slate-300"
              />
              {errors.serviceName && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.serviceName.message}
                </p>
              )}
            </div>

            {/* Duration */}
            <div>
              <Input
                placeholder="Duration (in hours)"
                type="number"
                inputMode="decimal"
                min="0.5"
                step="0.5"
                {...register("duration", { valueAsNumber: true })}
                className="border-input-border focus:border-slate-300"
                onKeyDown={(e) => {
                  // Allow numbers, decimal point, and control keys
                  if (!/[0-9.]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
                    e.preventDefault();
                  }
                }}
              />
              {errors.duration && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.duration.message}
                </p>
              )}
            </div>

            {/* Service Highlights */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">Service highlights</span>
                <Button
                  type="button"
                  onClick={handleAddHighlight}
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 text-orange-1"
                >
                  Add <Plus className="h-4 w-4 ml-1" />
                </Button>
              </div>

              <div className="space-y-2">
                {highlights.map((highlight) => (
                  <div key={highlight.id} className="flex gap-2">
                    <Input
                      placeholder="Type here"
                      value={highlight.text}
                      onChange={(e) =>
                        handleHighlightChange(highlight.id, e.target.value)
                      }
                      className="border-input-border focus:border-slate-300"
                    />
                    <Button
                      type="button"
                      onClick={() => handleRemoveHighlight(highlight.id)}
                      variant="ghost"
                      size="icon"
                      className="h-10 w-10 text-gray-500"
                      disabled={highlights.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
              {highlightsError && (
                <p className="text-sm text-red-500 mt-1">
                  {highlightsError}
                </p>
              )}
            </div>

            {/* Price */}
            <div className="">
              <Input
                placeholder=" $ Pricing"
                type="number"
                inputMode="decimal"
                min="1"
                step="0.01"
                {...register("price", { valueAsNumber: true })}
                className="border-input-border focus:border-slate-300"
                onKeyDown={(e) => {
                  // Allow numbers, decimal point, and control keys
                  if (!/[0-9.]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
                    e.preventDefault();
                  }
                }}
              />
              {errors.price && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.price.message}
                </p>
              )}
            </div>

            {/* Service Description */}
            <div>
              <Textarea
                placeholder="Service description"
                {...register("description")}
                className="min-h-[120px] border-input-border focus:border-slate-300"
              />
              {errors.description && (
                <p className="text-sm text-red-500 mt-1">
                  {errors.description.message}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              className="w-full bg-orange-1 hover:bg-orange-1/90"
              disabled={isLoading}
              onClick={() => {
                // Validate highlights on every submit attempt
                setTimeout(() => validateHighlights(), 0);
              }}
            >
              {isLoading ? "Updating..." : "Update"}
            </Button>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
