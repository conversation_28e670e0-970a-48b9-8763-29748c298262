import GoBack from "@/components/ui/go-back";
import { useParams } from "react-router-dom";
import EditIcon from "@/assets/edit.svg";
import DeleteIcon from "@/assets/delete.svg";
import Layout from "@/components/layout/SellerLayout";
import SellerProductCard from "../components/SellerProductCard";
import { useQuery } from "@tanstack/react-query";
import { getSellerSingleProductApi } from "../api";
import SellerProductCardSkelton from "../components/SellerProductCardSkelton";
import DeleteModal from "../components/DeleteModal";
import { useState } from "react";
import EditProductDrawer from "../components/EditProductDrawer";
function SellerProductDetails() {
  const { id } = useParams();
  const [openEditDrawer, setOpenEditDrawer] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const onCloseEditDrawer = () => setOpenEditDrawer(false);
  const onOpenEditDrawer = () => setOpenEditDrawer(true);
  const onClose = () => setIsOpen(false);
  const onOpen = () => setIsOpen(true);
  const {
    data,
    isPending: loading,
    refetch,
  } = useQuery({
    queryKey: ["seller-product", id],
    queryFn: () => getSellerSingleProductApi(id as string),
  });
  return (
    <>
      <Layout>
        <div className="md:hidden">
          <div className="flex justify-between ">
            <GoBack />
            <div className="flex gap-x-3">
              <img
                onClick={onOpenEditDrawer}
                src={EditIcon}
                alt="edit"
                className="max-w-[50px] max-h-[50px] m-auto"
              />
              <img
                onClick={onOpen}
                src={DeleteIcon}
                alt="delete"
                className="max-w-[50px] max-h-[50px] m-auto"
              />
            </div>
          </div>
          <div className="mt-5">
            {loading ? (
              <SellerProductCardSkelton />
            ) : (
              <SellerProductCard
                product={data?.data?.product}
                showDescription={true}
              />
            )}
          </div>
        </div>
      </Layout>
      {isOpen && (
        <DeleteModal
          refetch={refetch}
          shoulNavigate={true}
          productId={id as string}
          isOpen={isOpen}
          onClose={onClose}
        />
      )}
      <EditProductDrawer
        openDrawer={openEditDrawer}
        onCloseDrawer={onCloseEditDrawer}
        prodcutId={id as string}
        refetch={refetch}
      />
    </>
  );
}

export default SellerProductDetails;
