import { useEffect } from "react";
import { useLocation } from "react-router-dom";

const ScrollToTop: React.FC = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    const scrollToTop = () => {
      window.scrollTo({ top: 0, behavior: "smooth" });
    };

    if (document.readyState === "complete") {
      scrollToTop();
    } else {
      window.addEventListener("load", scrollToTop);
      return () => window.removeEventListener("load", scrollToTop);
    }
  }, [pathname]);

  return null;
};

export default ScrollToTop;
