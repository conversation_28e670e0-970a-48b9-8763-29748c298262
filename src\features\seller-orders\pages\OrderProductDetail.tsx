import Layout from "@/components/layout/SellerLayout";
import SummarySkelton from "../components/SummarySkelton";
import Summary from "../components/Summary";
import ShippedAddress from "../components/ShippedAddress";
import ShippedAddressSkelton from "../components/ShippedAddressSkelton";
import OrderCardSkelton from "../components/OrderCardSkelton";
import GoBack from "@/components/ui/go-back";
import OrderCard from "../components/OrderCard";
import OrderStatusSection from "../components/OrderStatusSection";
import { useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { fetchSellersSingleProductApi } from "../api";
import { OrderItems } from "../type";
import ReviewRequest from "../components/ReviewRequest";

function OrderProductDetail() {
  const { id } = useParams();

  const {
    data,
    isPending: loading,
    refetch,
  } = useQuery({
    queryKey: ["order-product", id],
    queryFn: () => fetchSellersSingleProductApi(id as string),
  });
  function fetchCurrentOrderStatus(): {
    currentOrderStatus: string;
    lastOrderStatus: string;
  } {
    const length = data?.data?.order?.status.length;
    for (let i = length - 1; i >= 0; i--) {
      if (data?.data?.order?.status[i].completed) {
        return {
          currentOrderStatus: data?.data?.order?.status[i].status,
          lastOrderStatus: data?.data?.order?.status[length - 1].status,
        };
      }
    }
    return { currentOrderStatus: "unknown", lastOrderStatus: "unknown" };
  }
  const { currentOrderStatus, lastOrderStatus } = fetchCurrentOrderStatus();

  const formatedOrder = {
    _id: data?.data?.order?._id,
    product: {
      title: data?.data?.order?.product?.title,
      images: data?.data?.order?.product?.images,
    },
    status: currentOrderStatus,
    quantity: data?.data?.order?.quantity,
  };
  const showReviewRequest = () => {
    return (
      currentOrderStatus === "item delivered" &&
      lastOrderStatus === "item delivered" &&
      !data?.data?.order?.hasRequestedReview
    );
  };

  return (
    <Layout showFooter={true}>
      <div className=" md:mt-8 md:mb-12">
        <GoBack />
      </div>
      <div className="grid w-full grid-cols-12 mb-5 lg:w-11/12 gap-x-5 gap-y-4 lg:gap-y-0">
        <div className="flex flex-col col-span-12 lg:col-span-7 gap-y-4">
          {loading ? (
            <div className="space-y-4">
              {[1, 2].map((_, index) => (
                <OrderCardSkelton key={index} />
              ))}
            </div>
          ) : (
            <OrderCard order={formatedOrder as OrderItems} />
          )}
          {showReviewRequest() && <ReviewRequest refetch={refetch} />}
          <OrderStatusSection
            refetch={refetch}
            currentOrderStatus={currentOrderStatus}
            orderProgress={data?.data?.order?.status}
          />
        </div>
        <div className="flex flex-col h-full col-span-12 lg:col-span-5 gap-y-4">
          {loading ? (
            <ShippedAddressSkelton />
          ) : (
            <ShippedAddress
              orderedBy={data?.data?.order?.user?.firstName}
              orderedOn={data?.data?.order?.createdAt}
              address={data?.data?.order?.shippingAddress}
            />
          )}
          {loading ? (
            <SummarySkelton />
          ) : (
            <Summary
              subTotal={data?.data?.order?.subTotal}
              amountPaid={data?.data?.order?.amountPaid}
              discount={data?.data?.order?.couponDiscountAmount}
            />
          )}
        </div>
      </div>
    </Layout>
  );
}

export default OrderProductDetail;
