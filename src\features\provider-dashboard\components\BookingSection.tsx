import { useState } from "react";
import { LuChevronDown, LuChevronUp } from "react-icons/lu";
import BookingItem from "./BookingItem";

interface BookingSectionProps {
  title: string;
  icon: React.ReactNode;
  count: number;
  bookings: {
    id: string;
    name: string;
    date: string;
    time: string;
  }[];
  type: "pending" | "upcoming" | "completed";
}

function BookingSection({ title, icon, count, bookings, type }: BookingSectionProps) {
  const [isExpanded, setIsExpanded] = useState(true);

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const handleAccept = (id: string) => {
    console.log(`Accepted booking ${id}`);
    // Implement accept logic here
  };

  const handleReject = (id: string) => {
    console.log(`Rejected booking ${id}`);
    // Implement reject logic here
  };

  return (
    <div className="mb-6">
      <div 
        className="flex items-center cursor-pointer" 
        onClick={handleToggle}
      >
        <div className="flex items-center gap-2">
          <span className="text-gray-700">{icon}</span>
          <h3 className="font-medium">{title}</h3>
          {count > 0 && (
            <span className="text-gray-700 font-normal text-base mr-1">
              ({count})
            </span>
          )}
        </div>
        {isExpanded ? (
          <LuChevronUp className="w-5 h-5" />
        ) : (
          <LuChevronDown className="w-5 h-5" />
        )}
      </div>
      
      {isExpanded && bookings.length > 0 && (
        <div className="mt-4">
          {bookings.map((booking) => (
            <BookingItem
              key={booking.id}
              name={booking.name}
              date={booking.date}
              time={booking.time}
              status={type}
              onAccept={() => handleAccept(booking.id)}
              onReject={() => handleReject(booking.id)}
            />
          ))}
        </div>
      )}
      
      {isExpanded && bookings.length === 0 && (
        <p className="mt-4 text-gray-500 text-sm">No {title.toLowerCase()} at the moment.</p>
      )}
    </div>
  );
}

export default BookingSection;
