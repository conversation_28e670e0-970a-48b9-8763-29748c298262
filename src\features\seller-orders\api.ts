import api from "@/lib/axios";

export const fetchSellerProductsApi = async (page: number) => {
  const { data } = await api.get("/api/v1/users/seller/orders", {
    params: { page },
  });
  return data;
};
export const fetchSellersSingleProductApi = async (orderId: string) => {
  const { data } = await api.get(`/api/v1/users/seller/orders/${orderId}`);
  return data;
};
export const fetchRequestDetailsApi = async (orderId: string) => {
  const { data } = await api.get(`/api/v1/users/seller/after-sales/${orderId}`);
  return data;
};
export const updateOrderStatusApi = async ({
  orderId,
  type,
}: {
  orderId: string;
  type: string;
}) => {
  const { data } = await api.patch(
    `/api/v1/users/seller/orders/${orderId}/${type}`
  );
  return data;
};
export const approveOrRejectRequestApi = async ({
  orderId,
  status,
  type,
}: {
  orderId: string;
  status: string;
  type: string;
}) => {
  const { data } = await api.patch(
    `/api/v1/users/seller/orders/${orderId}/${status}`,
    { actionType: type }
  );
  return data;
};
export const sendEmailApi = async ({
  orderId,
  email,
  actionType,
}: {
  orderId: string;
  email: string;
  actionType: string;
}) => {
  const { data } = await api.patch(
    `/api/v1/users/seller/orders/${orderId}/mail/${actionType}`,
    { email }
  );
  return data;
};

export const requestReviewApi = async (orderId: string) => {
  const { data } = await api.patch(
    `/api/v1/users/seller/orders/${orderId}/request-review`
  );
  return data;
};
