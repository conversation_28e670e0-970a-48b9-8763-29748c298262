import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'blog',
  title: 'Blog',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Title',
      type: 'string',
      validation: (rule) =>
        rule.required().min(3).error('A title of min. 3 characters is required'),
    }),
    defineField({
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (rule) => rule.required(),
    }),
    // defineField({
    //   name: 'author',
    //   title: 'Author',
    //   type: 'reference',
    //   to: {type: 'author'},
    // }),
    defineField({
      name: 'coverImage',
      title: 'Cover image',
      type: 'image',
      options: {
        hotspot: true,
      },
      validation: (rule) => rule.required(),
    }),
    defineField({
      name: 'mainImage',
      title: 'Main image',
      type: 'image',
      options: {
        hotspot: true,
      },
      validation: (rule) => rule.required(),
    }),
    // defineField({
    //   name: 'categories',
    //   title: 'Categories',
    //   type: 'array',
    //   of: [{type: 'reference', to: {type: 'category'}}],
    // }),
    defineField({
      name: 'publishedAt',
      title: 'Published at',
      type: 'date',
      validation: (rule) =>
        rule.required().max(new Date().toISOString()).error('Future dates are not allowed'),
    }),
    defineField({
      name: 'content',
      title: 'Content',
      type: 'array',
      of: [
        {
          type: 'block',
          styles: [], // Remove all block styles (e.g., h1, h2)
          lists: [], // Disable bullet/numbered lists
          marks: {
            decorators: [
              {title: 'Bold', value: 'strong'}, // Only allow bold
            ],
            annotations: [], // Disable links or custom annotations
          },
        },
      ],
       validation: (Rule) =>
    Rule.custom((blocks) => {
      if (!blocks || blocks.length === 0) {
        return 'Content is required';
      }

      const plainText = blocks
        .map((block) => ((block as { children?: { text: string }[] }).children || []).map((child) => child.text).join(''))
        .join('\n');

      const length = plainText.trim().length;

      if (length < 10) return 'Content must be at least 10 characters';
      if (length > 300000) return 'Content must be under 300,000 characters';

      return true;
    }),
    }),
  ],
})
