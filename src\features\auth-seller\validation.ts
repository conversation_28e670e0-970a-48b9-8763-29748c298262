import Joi from "joi";

export const signupSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .lowercase()
    .trim()
    .messages({
      "any.required": "Email is a required field.",
      "string.base": "Email must be a string",
      "string.email": "Invalid email",
      "string.empty": "Email cannot be empty",
    }),

  password: Joi.string()
    .trim()
    .required()
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    )
    .messages({
      "string.base": "Password must be a string",
      "string.empty": "Password cannot be empty",
      "any.required": "Password is a required field",
      "string.pattern.base":
        "Password must be 8-32 characters with at least one uppercase, lowercase, and special character",
    }),
  confirmPassword: Joi.string()
    .valid(Joi.ref("password"))
    .required()
    .label("Confirm password")
    .messages({
      "any.only": "{{#label}} does not match",
    }),
});
export const LoginSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .lowercase()
    .trim()
    .messages({
      "any.required": "Email is a required field.",
      "string.base": "Email must be a string",
      "string.email": "Invalid email",
      "string.empty": "Email cannot be empty",
    }),

  password: Joi.string()
    .trim()
    .required()

    .messages({
      "string.base": "Password must be a string",
      "string.empty": "Password cannot be empty",
      "any.required": "Password is a required field",
      "string.pattern.base":
        "Password must be 8-32 characters with at least one uppercase, lowercase, and special character",
    }),
});

export const forgotPasswordSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .lowercase()
    .trim()
    .messages({
      "any.required": "Email is a required field.",
      "string.base": "Email must be a string",
      "string.email": "Invalid email",
      "string.empty": "Email cannot be empty",
    }),
});
export const resetPasswordSchema = Joi.object({
  newPassword: Joi.string()
    .trim()
    .required()
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    )
    .messages({
      "string.base": "Password must be a string",
      "string.empty": "Password cannot be empty",
      "any.required": "Password is a required field",
      "string.pattern.base":
        "Password must be 8-32 characters with at least one uppercase, lowercase, and special character",
    }),
  confirmPassword: Joi.string()
    .valid(Joi.ref("newPassword"))
    .required()
    .label("Confirm password")
    .messages({
      "any.only": "{{#label}} does not match",
    }),
});
export const setupProfileSchema = Joi.object({
  companyName: Joi.string().required().min(2).max(100).trim().messages({
    "any.required": "Company name is a required field",
    "string.base": "Company name must be a string",
    "string.min": "Company name should have at least {#limit} characters",
    "string.max": "Company name should have at most {#limit} characters",
    "string.empty": "Company name cannot be empty",
  }),

  category: Joi.string().required().min(1).max(100).trim().messages({
    "any.required": "Category is a required field",
    "string.base": "Category must be a string",
    "string.min": "Category should have at least {#limit} characters",
    "string.max": "Category should have at most {#limit} characters",
    "string.empty": "Category cannot be empty",
  }),

  refundPolicy: Joi.string().required().trim().min(10).max(400).messages({
    "string.base": "Refund policy must be a string",
    "any.required": "Refund policy is a required field",
    "string.empty": "Refund policy cannot be empty",
    "string.min": `Refund policy must be at least {#limit} characters`,
    "string.max": `Refund policy must be at most {#limit} characters`,
  }),
  phone: Joi.string().pattern(/^\d+$/).required().messages({
    "string.pattern.base": "Phone number must contain digits only.",
    "string.empty": "Phone number cannot be empty.",
    "any.required": "Phone number is required.",
  }),
  taxId: Joi.string().alphanum().min(6).max(20).required().messages({
    "string.base": `Tax id should be a text`,
    "string.alphanum": `Tax id must contain only letters and numbers`,
    "string.empty": `Tax id cannot be empty`,
    "string.min": `Tax id must be at least {#limit} characters`,
    "string.max": `Tax id must be at most {#limit} characters`,
    "any.required": `Tax id is a required field`,
  }),
  video: Joi.any()
    .custom((value, helpers) => {
      const file: File | undefined = value?.[0];
      if (!file)
        return helpers.error("any.required", { message: "Video is required." });

      const allowedTypes = ["video/mp4", "video/webm", "video/ogg"];
      if (!allowedTypes.includes(file.type)) {
        return helpers.error("any.invalid", {
          message: "Only MP4, WebM, and OGG formats are allowed.",
        });
      }

      const maxSizeMB = 100;
      if (file.size > maxSizeMB * 1024 * 1024) {
        return helpers.error("any.custom", {
          message: `Video must be smaller than ${maxSizeMB}MB.`,
        });
      }

      return value;
    })
    .required(),
});

export const productCreationSchema = Joi.object({
  title: Joi.string().trim().required().min(5).max(70).messages({
    "any.required": "Title is a required field",
    "string.base": "Title must be a string",
    "string.min": "Title atleast need 5 characters",
    "string.max": "Title cannot exceed 70 characters",
    "string.empty": "Title cannot be empty",
  }),

  description: Joi.string().trim().required().min(10).max(3000).messages({
    "any.required": "Description is a required field",
    "string.base": "Description must be a string",
    "string.min": "Description atleast need 10 characters",
    "string.max": "Description cannot exceed 3000 characters",
    "string.empty": "Description cannot be empty",
  }),

  quantity: Joi.number().integer().required().min(1).max(5000).messages({
    "number.base": "Quantity must be a number",
    "any.required": "Quantity is a required field",
    "number.integer": "Quantity must be an integer value",
    "number.min": `"quantity" must be at least {#limit}`,
    "number.max": `"quantity" must be less than or equal to {#limit}`,
  }),

  price: Joi.number().integer().required().min(1).messages({
    "number.base": "price must be a number",
    "any.required": "price is a required field",
    "number.integar": "price must be a integar value",
    "number.min": `"price" must be at least {#limit}`,
  }),
  // images: Joi.array()
  //   .items(Joi.string().required().trim())
  //   .required()
  //   .min(1)
  //   .messages({
  //     "array.base": "Images values must be array",
  //     "array.min": "Atleast one image is required",
  //     "any.required": "Images is a required field",
  //     "string.base": "Each image must be string",
  //   }),
});
