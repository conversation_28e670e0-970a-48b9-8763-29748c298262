import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.tsx";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import "@fontsource/public-sans/300.css"; // Light
import "@fontsource/public-sans/400.css"; // Regular
import "@fontsource/public-sans/600.css"; // SemiBold
import "@fontsource/public-sans/700.css"; // Bold
const queryClient = new QueryClient();
createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  </StrictMode>
);
