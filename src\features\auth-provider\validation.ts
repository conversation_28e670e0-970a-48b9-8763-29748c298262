import Joi from "joi";

export const signupSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .lowercase()
    .trim()
    .messages({
      "any.required": "Email is a required field.",
      "string.base": "Email must be a string",
      "string.email": "Invalid email",
      "string.empty": "Email cannot be empty",
    }),

  password: Joi.string()
    .trim()
    .required()
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    )
    .messages({
      "string.base": "Password must be a string",
      "string.empty": "Password cannot be empty",
      "any.required": "Password is a required field",
      "string.pattern.base":
        "Password must be 8-32 characters with at least one uppercase, lowercase, and special character",
    }),
  confirmPassword: Joi.string()
    .valid(Joi.ref("password"))
    .required()
    .label("Confirm password")
    .messages({
      "any.only": "{{#label}} does not match",
    }),
});

export const LoginSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .lowercase()
    .trim()
    .messages({
      "any.required": "Email is a required field.",
      "string.base": "Email must be a string",
      "string.email": "Invalid email",
      "string.empty": "Email cannot be empty",
    }),

  password: Joi.string().trim().required().messages({
    "string.base": "Password must be a string",
    "string.empty": "Password cannot be empty",
    "any.required": "Password is a required field",
    "string.pattern.base":
      "Password must be 8-32 characters with at least one uppercase, lowercase, and special character",
  }),
});

export const forgotPasswordSchema = Joi.object({
  email: Joi.string()
    .email({ tlds: { allow: false } })
    .required()
    .lowercase()
    .trim()
    .messages({
      "any.required": "Email is a required field.",
      "string.base": "Email must be a string",
      "string.email": "Invalid email",
      "string.empty": "Email cannot be empty",
    }),
});

export const resetPasswordSchema = Joi.object({
  newPassword: Joi.string()
    .trim()
    .required()
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    )
    .messages({
      "string.base": "Password must be a string",
      "string.empty": "Password cannot be empty",
      "any.required": "Password is a required field",
      "string.pattern.base":
        "Password must be 8-32 characters with at least one uppercase, lowercase, and special character",
    }),
  confirmPassword: Joi.string()
    .valid(Joi.ref("newPassword"))
    .required()
    .label("Confirm password")
    .messages({
      "any.only": "{{#label}} does not match",
    }),
});

export const setupProfileSchema = Joi.object({
  firstName: Joi.string().required().min(3).max(26).trim().messages({
    "any.required": "First name is a required field",
    "string.base": "First name must be a string",
    "string.min": "First name atleast need 3 characters",
    "string.max": "First name cannot exceed 26 characters",
    "string.empty": "First name cannot be empty",
  }),

  lastName: Joi.string().required().min(3).max(26).trim().messages({
    "any.required": "Last name is a required field",
    "string.base": "Last name must be a string",
    "string.min": "Last name atleast need 3 characters",
    "string.max": "Last name cannot exceed 26 characters",
    "string.empty": "Last name cannot be empty",
  }),

  description: Joi.string().required().min(10).trim().messages({
    "any.required": "Description is a required field",
    "string.base": "Description must be a string",
    "string.min": "Description must be at least 10 characters",
    "string.empty": "Description cannot be empty",
  }),

  phone: Joi.string().pattern(/^\d+$/).required().messages({
    "string.pattern.base": "Phone number must contain digits only.",
    "string.empty": "Phone number cannot be empty.",
    "any.required": "Phone number is required.",
  }),
  specialty: Joi.string().required().trim().messages({
    "any.required": "Specialty is a required field",
    "string.base": "Specialty must be a string",
    "string.empty": "Specialty cannot be empty",
  }),

  businessName: Joi.string().required().trim().messages({
    "any.required": "Business name is a required field",
    "string.base": "Business name must be a string",
    "string.empty": "Business name cannot be empty",
  }),

  taxId: Joi.string().required().trim().messages({
    "any.required": "Tax ID is a required field",
    "string.base": "Tax ID must be a string",
    "string.empty": "Tax ID cannot be empty",
  }),

  yearsOfExperience: Joi.string().required().trim().pattern(/^\d+$/).messages({
    "any.required": "Years of experience is a required field",
    "string.base": "Years of experience must be a number",
    "string.empty": "Years of experience cannot be empty",
    "string.pattern.base": "Years of experience must contain only numbers",
  }),

  serviceLocation: Joi.array().items(Joi.string()),

  introductoryVideo: Joi.any().required().messages({
    "any.required": "Introductory video is required",
    "any.invalid": "Invalid video format",
    // "any.custom": "Video must be smaller than 100MB",
    "any.empty": "Video cannot be empty",
  }),

  certifications: Joi.any().required().messages({
    "any.required": "Certifications are required",
    "any.invalid": "Invalid certification format",
    "any.empty": "Certification cannot be empty",
  }),

  photo: Joi.any().required().messages({
    "any.required": "Profile picture is required",
  }),

  refundPolicy: Joi.string().required().min(10).trim().messages({
    "any.required": "Refund policy is a required field",
    "string.base": "Refund policy must be a string",
    "string.empty": "Refund policy cannot be empty",
    "string.min": "Refund policy must be at least 10 characters",
  }),
});

export const serviceDetailsSchema = Joi.object({
  serviceName: Joi.string().required().trim().messages({
    "any.required": "Service name is required",
    "string.empty": "Service name is required",
    "string.base": "Service name must be a string",
  }),
  duration: Joi.number().required().min(0.5).messages({
    "any.required": "Duration is required",
    "number.base": "Duration must be a number",
    "number.min": "Duration must be at least 0.5 hours",
  }),
  price: Joi.number().required().min(1).precision(2).messages({
    "any.required": "Price is required",
    "number.base": "Price must be a number",
    "number.min": "Price must be at least 1",
  }),
  description: Joi.string().required().min(10).trim().messages({
    "any.required": "Description is required",
    "string.empty": "Description is required",
    "string.base": "Description must be a string",
    "string.min": "Description must be at least 10 characters",
  }),
  highlights: Joi.array().items(
    Joi.object({
      text: Joi.string().allow(""),
      id: Joi.string().required(),
    })
  ),
});

// Edit profile validation schema (same as setup profile but without video, certifications, refund policy)
export const editProfileSchema = Joi.object({
  firstName: Joi.string().required().trim().messages({
    "any.required": "First name is required",
    "string.empty": "First name is required",
    "string.base": "First name must be a string",
  }),
  lastName: Joi.string().required().trim().messages({
    "any.required": "Last name is required",
    "string.empty": "Last name is required",
    "string.base": "Last name must be a string",
  }),
  description: Joi.string().required().min(10).trim().messages({
    "any.required": "Description is a required field",
    "string.base": "Description must be a string",
    "string.min": "Description must be at least 10 characters",
    "string.empty": "Description cannot be empty",
  }),
  phone: Joi.string().required().trim().messages({
    "any.required": "Phone number is required",
    "string.empty": "Phone number is required",
    "string.base": "Phone number must be a string",
  }),
  specialty: Joi.string().required().trim().messages({
    "any.required": "Specialty is required",
    "string.empty": "Specialty is required",
    "string.base": "Specialty must be a string",
  }),
  businessName: Joi.string().required().trim().messages({
    "any.required": "Business name is required",
    "string.empty": "Business name is required",
    "string.base": "Business name must be a string",
  }),
  taxId: Joi.string().required().trim().messages({
    "any.required": "Tax ID is required",
    "string.empty": "Tax ID is required",
    "string.base": "Tax ID must be a string",
  }),
  yearsOfExperience: Joi.number().required().min(0).max(50).messages({
    "any.required": "Years of experience is required",
    "number.base": "Years of experience must be a number",
    "number.min": "Years of experience must be at least 0",
    "number.max": "Years of experience cannot exceed 50",
  }),
  serviceLocation: Joi.array().items(Joi.string()).min(1).messages({
    "array.min": "Service location is required",
  }),
  photo: Joi.any().optional(),
});
