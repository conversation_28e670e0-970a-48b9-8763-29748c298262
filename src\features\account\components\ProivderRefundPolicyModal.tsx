import { Modal } from "@/components/ui/modal";

function ProivderRefundPolicyModal({
  isOpen,
  onClose,
  refundPolicy,
}: {
  isOpen: boolean;
  onClose: () => void;
  refundPolicy: string;
}) {
  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <>
        <h1 className="text-lg font-medium">Refund Policy</h1>
        <p className="mt-3 text-neutral-300">{refundPolicy}</p>
      </>
    </Modal>
  );
}

export default ProivderRefundPolicyModal;
