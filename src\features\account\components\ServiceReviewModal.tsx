import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import StarRating from "@/components/ui/star-rating";
import { Textarea } from "@/components/ui/textarea";
import { showToast } from "@/lib/toast";
import { useMutation } from "@tanstack/react-query";
import React, { useState } from "react";
import { submitServiceReviewApi } from "../api";

function ServiceReviewModal({
  serviceId,
  isOpen,
  onClose,
  providerName,
}: {
  providerName: string;
  serviceId: string;
  isOpen: boolean;
  onClose: () => void;
}) {
  const [rating, setRating] = useState(0);
  const [review, setReview] = useState("");
  const onReviewChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setReview(e.target.value);
  };
  const { mutate, isPending: loading } = useMutation({
    mutationFn: submitServiceReviewApi,
    onSuccess: () => {
      showToast("review submitted successfully", "success");
      onClose();
    },
    onError: () => {
      showToast("Failed to submit review", "error");
    },
  });
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!rating) return;
    mutate({ review, rating, serviceId });
  };
  return (
    <Modal  isOpen={isOpen} onClose={onClose}>
      <form onSubmit={handleSubmit} className="flex flex-col gap-y-6">
        <h1 className="text-lg font-semibold">
          How was your session with {providerName}?
        </h1>
        <div className="flex justify-center">
          <StarRating rating={rating} setRating={setRating} />
        </div>
        <Textarea
          onChange={(e) => onReviewChange(e)}
          value={review}
          placeholder="Type here..."
          className="border border-neutral-50 "
        />
        <Button disabled={loading} className="w-full">
          {loading ? "submitting" : "submit"}
        </Button>
      </form>
    </Modal>
  );
}

export default ServiceReviewModal;
