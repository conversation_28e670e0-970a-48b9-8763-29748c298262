import { useEffect, useRef, useState } from "react";
import RecentC<PERSON><PERSON>eader from "./RecentChatHeader";
import { fetchCustomerChatListApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import { Conversation, SelectedChat } from "../type";
import RecentChatHeaderSkelton from "./RecentChatHeaderSkelton";
import InfiniteS<PERSON>roll from "react-infinite-scroll-component";
import { useAuthStore } from "@/store/authStore";

function CustomerChatList({
  currentFocus,
  selectedChatId,
  search,
  onChatSelect,
  handleHideMessageSection,
}: {
  currentFocus: "seller" | "provider";
  selectedChatId: string | null;
  search: string;
  onChatSelect: (data: SelectedChat) => void;
  handleHideMessageSection: () => void;
}) {
  const currentUser = useAuthStore((state) => state.user);
  const socket = currentUser?.socket?.chat;
  const scrollableRef = useRef<HTMLDivElement>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [allChats, setAllChats] = useState<Conversation[]>([]);
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const [focus, setFocus] = useState<"seller" | "provider">("seller");
  const handleChatSelect = (chat: Conversation) => {
    onChatSelect({
      conversationId: chat._id,
      name: chat.recipient.companyName ?? chat.recipient.firstName ?? "",
      description: chat.recipient.speciality ?? chat.recipient.category ?? "",
      profilePicture: chat.recipient.profilePicture?.[0],
      receiverId: chat.recipient._id,
      role: focus,
    });
    handleHideMessageSection();
  };
  const { data, isPending: loading } = useQuery({
    queryKey: ["customer-chat-list", focus, search, currentPage],
    queryFn: () =>
      fetchCustomerChatListApi({ page: currentPage, category: focus, search }),
  });
  useEffect(() => { // Reset to the first page when search changes
    setCurrentPage(1);
    setAllChats([]);
  }, [search]);
  useEffect(() => {
    if (currentFocus) {
      setFocus(currentFocus);
    }
  }, [currentFocus]);
  useEffect(() => {
    setAllChats([]);
    setCurrentPage(1);
    if (scrollableRef.current) {
      (scrollableRef.current as HTMLElement)?.scrollIntoView({
        behavior: "smooth",
      });
    }
  }, [focus]);

  useEffect(() => {
    if (!data?.conversations) return;
    if (currentPage === 1) {
      setAllChats(data.conversations);
    } else {
      setAllChats((prev) => [...prev, ...data.conversations]);
    }
  }, [data?.conversations, currentPage]);

  // socket listener for new conversations
  useEffect(() => {
    if (!socket) return;

    const handleConversation = (data: Conversation) => {
      const conversationIndex = allChats.findIndex((chat) => {
        return chat._id === data._id;
      });
      if (conversationIndex === -1) {
        // if conversation is not in the list
        setAllChats((prev) => [data, ...prev]);
        return;
      }
      const updatedChats = [...allChats];
      updatedChats.splice(conversationIndex, 1);
      updatedChats.unshift(data);
      setAllChats(updatedChats);
    };
    socket.on("conversation", handleConversation);
    return () => {
      socket.off("conversation", handleConversation);
    };
  }, [socket, allChats]);
  return (
    <div className="w-full ">
      <div className="flex">
        <h1
          onClick={() => setFocus("seller")}
          className={`w-1/2 text-center cursor-pointer ${focus === "seller" ? "font-semibold	border-b-4 border-orange-1" : ""} `}
        >
          Sellers
        </h1>
        <h1
          onClick={() => setFocus("provider")}
          className={`w-1/2 text-center cursor-pointer ${focus === "provider" ? "font-semibold	border-b-4 border-orange-1" : ""} `}
        >
          Providers
        </h1>
      </div>

      <div
        id="scrollableDiv"
        className="w-full h-[45vh] md:h-[50vh] overflow-y-scroll scrollbar-hide"
      >
        <InfiniteScroll
          style={{ width: "100%" }}
          scrollThreshold={0.5}
          scrollableTarget="scrollableDiv"
          dataLength={allChats.length || 0}
          next={() => handlePageChange(currentPage + 1)}
          hasMore={((data?.totalPages as number) || 0) > currentPage}
          loader={
            <div className="flex justify-center py-4">
              <div className="flex items-center gap-2 text-sm text-neutral-500">
                <div className="w-4 h-4 border-2 rounded-full border-coral border-t-transparent animate-spin"></div>
                Loading more conversations...
              </div>
            </div>
          }
          endMessage={
            currentPage === data?.totalPages && (
              <div className="flex justify-center py-4">
                <div className="text-sm text-neutral-400">
                  No more conversations to load
                </div>
              </div>
            )
          }
        >
          {loading ? (
            <div className="space-y-3">
              {[1, 2].map((_, index) => (
                <RecentChatHeaderSkelton key={index} />
              ))}
            </div>
          ) : (
            allChats.map((chat, index) => (
              <div
                className={`${
                  [chat._id, chat.recipient._id].includes(
                    selectedChatId as string
                  )
                    ? "bg-tints-40"
                    : ""
                }`}
                ref={index === 0 ? scrollableRef : null}
                key={index}
                onClick={() => handleChatSelect(chat)}
              >
                <RecentChatHeader
                  image={chat.recipient.profilePicture?.[0]?.url}
                  name={
                    chat.recipient.firstName ||
                    chat.recipient.companyName ||
                    "Unknown"
                  }
                  lastMessage={chat.lastMessage.content}
                  unreadCount={chat.unreadCount}
                />
              </div>
            ))
          )}
        </InfiniteScroll>
      </div>
    </div>
  );
}

export default CustomerChatList;
