export type AddressFormData = {
  address: string;
  city: string;
  state: string;
  zipCode: string;
};

export type UpdateCartApiPayload = {
  productId: string;
  count: number;
  type: "inc" | "dec";
};

export type CartItem = {
  _id: string;
  image: { image: string; key: string };
  title: string;
  price: number;
  currentQuantity: number;
  productId: string;
};

export type CartItemResponse = {
  _id: string;
  promoCode: string;
  quantity: number;
  discountAmount: number;
  discountedPercentage: number;
  price: number;
  products: {
    _id: string;
    title: string;
    images: string[];
  };
};
