import { Link } from "react-router-dom";

function DetailRow({
  label,
  value,
  link,
}: {
  label: string;
  value: string | number;
  link?: string;
}) {
  return (
    <div className="grid grid-cols-12 py-4 border-t border-gray-200 first:border-t-0 hover:bg-gray-50">
      <span className="col-span-5 text-sm text-neutral-300 text-start">
        {label}
      </span>
      <span className="col-span-2 text-center text-neutral-600">:</span>
      {link ? (
        <Link
          to={link}
          target="_blank"
          rel="noopener noreferrer"
          className="col-span-5 text-sm font-medium text-gray-900 underline"
        >
          {value}
        </Link>
      ) : (
        <span className="col-span-5 text-sm font-medium text-gray-900 ">
          {value}
        </span>
      )}
    </div>
  );
}

export default DetailRow;
