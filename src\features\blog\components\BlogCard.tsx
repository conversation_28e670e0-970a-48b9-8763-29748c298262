import { BlogCardProps } from "../type";
import { dateFormatter } from "@/lib/utils";
import { Link } from "react-router-dom";

function BlogCard({
  publishedAt,
  coverImage,
  title,
  redirectTo,
}: BlogCardProps) {
  return (
    <Link
      to={redirectTo}
      className="block transition-all border rounded-lg shadow-sm hover:shadow-md hover:scale-[1.01]"
    >
      <img
        src={coverImage}
        alt="blog cover"
        className="w-full h-3/4 max-h-[200px]  object-cover rounded-t-lg "
      />
      <div className="p-2">
        <h3 className="mt-3 text-lg">{title}</h3>
        <h5 className="text-sm text-neutral-300">
          {dateFormatter(publishedAt)}
        </h5>
      </div>
    </Link>
  );
}

export default BlogCard;
