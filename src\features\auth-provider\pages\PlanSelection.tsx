import { Navigate } from "react-router-dom";
import { checkAuthentication } from "@/lib/utils";
import PlanSelectionLayout from "../components/PlanSelectionLayout";
import ProfileSetupStep4 from "../components/ProfileSetupStep4";
import FingerPrintImage from "@/assets/fingerprint.png";
import { useNavigate } from "react-router-dom";

function PlanSelection() {
  const { isLoggedIn, href } = checkAuthentication();
  const navigate = useNavigate();

  const handleBack = () => {
    // Navigate back to the previous step
    navigate("/provider/setup-profile");
  };

  return isLoggedIn ? (
    <Navigate to={href} />
  ) : (
    <PlanSelectionLayout>
      <div className="w-full text-center space-y-6">
        <img
          src={FingerPrintImage}
          alt="Fingerprint"
          className="mx-auto object-cover"
        />
        <ProfileSetupStep4
          onBack={handleBack}
        />
      </div>
    </PlanSelectionLayout>
  );
}

export default PlanSelection;
