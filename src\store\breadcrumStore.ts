import { create } from "zustand";

export type Crumb = {
  name: string;
  href: string;
};

type BreadcrumbStore = {
  crumbs: Record<string, Crumb>;
  setCrumbs: (list: Crumb[]) => void;
  getCrumbByHref: (href: string) => Crumb | undefined;
  clearCrumbs: () => void;
};

//initial values
const initialCrumbs: Record<string, Crumb> = {
  "/account": { name: "Account", href: "/account" },
  "/account/basic-details": {
    name: "Basic details",
    href: "/account/basic-details",
  },
  "/account/orders": { name: "My orders", href: "/account/orders" },
  "/account/services": { name: "Services booked", href: "/account/services" },
  "/account/scheduled-calls": {
    name: "Scheduled calls",
    href: "/account/scheduled-calls",
  },
  "/account/care-plan": { name: "Care plan", href: "/account/care-plan" },
  "/seller/account/basic-details": {
    name: "basic details",
    href: "/seller/account/basic-details",
  },
  "/seller/account": { name: "Account", href: "/seller/account" },
  "/seller/account/promotional-offers": {
    name: "Promotional offers",
    href: "/seller/account/promotional-offers",
  },
};

export const useBreadcrumbStore = create<BreadcrumbStore>((set, get) => ({
  crumbs: initialCrumbs,

  setCrumbs: (list) =>
    set((state) => ({
      crumbs: {
        ...state.crumbs,
        ...Object.fromEntries(list.map((crumb) => [crumb.href, crumb])),
      },
    })),

  getCrumbByHref: (href) => get().crumbs[href],

  clearCrumbs: () => set({ crumbs: initialCrumbs }),
}));
