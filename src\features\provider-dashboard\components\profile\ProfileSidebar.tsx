import { Link, useLocation } from "react-router-dom";

// Navigation items for the profile sidebar
const getNavItems = (isPreview: boolean) => [
  {
    name: "Basic details",
    href: isPreview ? "/provider/profile-preview/basic-details" : "/provider/profile/basic-details"
  },
  {
    name: "Availability",
    href: isPreview ? "/provider/profile-preview/availability" : "/provider/profile/availability"
  },
  {
    name: "Promotional offers",
    href: isPreview ? "/provider/profile-preview/promotional-offers" : "/provider/profile/promotional-offers"
  },
  {
    name: "Subscription",
    href: isPreview ? "/provider/profile-preview/subscription" : "/provider/profile/subscription"
  },
];

function ProfileSidebar() {
  const location = useLocation();
  const currentPath = location.pathname;
  const isPreview = currentPath.includes('profile-preview');
  const navItems = getNavItems(isPreview);

  return (
    <aside className="w-full pr-6">
      <div>
        <h2 className="mb-6 md:text-xl font-semibold">My account</h2>
        <nav className="flex flex-col space-y-2">
          {navItems.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className={`py-2 px-3 rounded-md md:text-base transition-colors ${
                currentPath === item.href
                  ? "text-black font-semibold"
                  : "text-gray-600 hover:bg-gray-50"
              }`}
            >
              {item.name}
            </Link>
          ))}
        </nav>
      </div>
    </aside>
  );
}

export default ProfileSidebar;
