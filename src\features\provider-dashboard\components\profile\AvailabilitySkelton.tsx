import { Skeleton } from "@/components/ui/skeleton";

export default function AvailabilitySkeleton() {
  const days = [
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
    "sunday",
  ];

  return (
    <div className="w-full p-8">
      <div className="mb-8 space-y-4">
        {days.map((day) => (
          <div key={day} className="flex items-center gap-6">
            {/* Day Checkbox + Label */}
            <div className="flex items-center w-20">
              <Skeleton className="w-4 h-4 mr-2 rounded" />
              <Skeleton className="w-12 h-4" />
            </div>

            {/* Time Slots or Unavailable text */}
            <div className="flex-1 space-y-2">
              {/* Mimic one time slot row for skeleton */}
              <div className="flex items-center">
                <Skeleton className="w-24 h-10 rounded" />
                <span className="mx-2 text-gray-400">-</span>
                <Skeleton className="w-24 h-10 rounded" />
                <Skeleton className="w-8 h-8 ml-2 rounded" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Save Button */}
      <Skeleton className="w-full h-10 rounded md:w-40" />
    </div>
  );
}
