import { useState } from "react";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import Jo<PERSON> from "joi";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog";
import { X, Plus, Trash2 } from "lucide-react";

interface ServiceHighlightDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (highlights: string[]) => void;
  isLoading?: boolean;
}

interface ServiceHighlightFormData {
  highlights: string[];
}

// Validation schema for service highlights
const serviceHighlightSchema = Joi.object({
  highlights: Joi.array()
    .items(
      Joi.string().min(3).max(100).required().messages({
        "string.min": "Please enter at least 3 characters",
        "string.max": "Highlight must be less than 100 characters",
        "any.required": "This field cannot be empty",
        "string.empty": "This field cannot be empty",
      })
    )
    .custom((value, helpers) => {
      // Filter out empty strings for validation
      const validHighlights = value.filter((item: string) => item && item.trim() !== "");
      if (validHighlights.length === 0) {
        return helpers.error("array.min");
      }
      return value;
    })
    .min(1)
    .required()
    .messages({
      "array.min": "Please add at least one service highlight",
      "array.includesRequiredUnknowns": "Please add at least one service highlight",
      "array.includesRequiredKnowns": "Please add at least one service highlight",
      "array.hasUnknown": "Please add at least one service highlight",
      "array.hasKnown": "Please add at least one service highlight",
      "any.required": "Please add at least one service highlight",
    }),
});

export default function ServiceHighlightDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
}: ServiceHighlightDialogProps) {
  const [highlights, setHighlights] = useState<string[]>([""]);

  const {
    handleSubmit,
    setValue,
    trigger,
    formState: { errors },
  } = useForm<ServiceHighlightFormData>({
    resolver: joiResolver(serviceHighlightSchema),
  });

  // Add new highlight field
  const addHighlight = () => {
    const newHighlights = [...highlights, ""];
    setHighlights(newHighlights);
    setValue("highlights", newHighlights);
  };

  // Remove highlight field
  const removeHighlight = (index: number) => {
    if (highlights.length > 1) {
      const newHighlights = highlights.filter((_, i) => i !== index);
      setHighlights(newHighlights);
      setValue("highlights", newHighlights);
      trigger("highlights");
    }
  };

  // Update highlight text
  const updateHighlight = (index: number, value: string) => {
    const newHighlights = [...highlights];
    newHighlights[index] = value;
    setHighlights(newHighlights);
    setValue("highlights", newHighlights);
    trigger("highlights");
  };

  // Handle form submission
  const handleFormSubmit = (data: ServiceHighlightFormData) => {
    // Filter out empty highlights
    const validHighlights = data.highlights.filter(h => h.trim() !== "");
    onSubmit(validHighlights);

    // Reset form state after successful submission
    setHighlights([""]);
    setValue("highlights", [""]);
  };

  // Reset form when dialog closes
  const handleDialogClose = () => {
    setHighlights([""]);
    setValue("highlights", [""]);
    onOpenChange(false);
  };

  // Reset form when dialog opens
  const handleDialogOpen = (open: boolean) => {
    if (open) {
      // Reset state when opening
      setHighlights([""]);
      setValue("highlights", [""]);
    }
    onOpenChange(open);
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogOpen}>
      <DialogContent className="max-w-md mx-auto max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-lg font-semibold">Add Service Highlights</DialogTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDialogClose}
            className="h-6 w-6"
            disabled={isLoading}
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          <div className="space-y-3">
            <p className="text-sm text-gray-600">
              Add highlights that showcase your key services and expertise.
            </p>

            {highlights.map((highlight, index) => (
              <div key={index} className="flex gap-2 items-start">
                <div className="flex-1">
                  <Input
                    placeholder={`Service highlight ${index + 1}`}
                    value={highlight}
                    onChange={(e) => updateHighlight(index, e.target.value)}
                    className="border-input-border focus:border-slate-300"
                    disabled={isLoading}
                  />
                  {errors.highlights?.[index] && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors.highlights[index]?.message}
                    </p>
                  )}
                </div>

                {highlights.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeHighlight(index)}
                    className="text-gray-400 hover:text-red-500 mt-1"
                    disabled={isLoading}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                )}
              </div>
            ))}

            {/* Add More Button */}
            {highlights.length < 5 && (
              <Button
                type="button"
                variant="outline"
                onClick={addHighlight}
                className="w-full flex items-center gap-2 text-orange-600 border-orange-300 hover:bg-orange-50"
                disabled={isLoading}
              >
                <Plus className="w-4 h-4" />
                Add Another Highlight
              </Button>
            )}

            {highlights.length >= 5 && (
              <p className="text-xs text-gray-500 text-center">
                Maximum 5 highlights allowed
              </p>
            )}
          </div>

          {errors.highlights && typeof errors.highlights.message === 'string' && (
            <p className="text-sm text-red-500">{errors.highlights.message}</p>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleDialogClose}
              className="flex-1"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1"
              disabled={highlights.every(h => h.trim() === "") || isLoading}
            >
              {isLoading ? "Adding..." : "Add Highlights"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
