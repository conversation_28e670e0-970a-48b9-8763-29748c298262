import { useEffect, useRef, useState } from "react";
import { Input } from "@/components/ui/input";
import { dateFormatter, timeFormatter } from "@/lib/utils";
import GoBackIcon from "@/assets/go-back.svg";
import SendIcon from "@/assets/send.svg";
import Buttonwithicon from "@/components/ui/buttonwithicon";
import EmptyConversation from "./EmptyConversation";
import { useMutation, useQuery } from "@tanstack/react-query";
import { fetchConversationMessagesApi, sendMessageApi } from "../api";
import { Message } from "../type";
import InfiniteScroll from "react-infinite-scroll-component";
import { useAuthStore } from "@/store/authStore";

function MessageSection({
  name,
  description,
  handleHideMessageSection,
  receiverId,
  conversationId,
}: {
  name: string;
  description: string;
  handleHideMessageSection: () => void;
  receiverId: string;
  conversationId?: string;
}) {
  const currentUser = useAuthStore((state) => state.user);
  const socket = currentUser?.socket?.chat;
  const user = localStorage.getItem("nurtureUser");
  const scrollableRef = useRef<HTMLDivElement>(null);
  const prevReceiverIdRef = useRef<string>(receiverId);
  const lastMessageRef = useRef(null);
  const roomIdRef = useRef<string | null>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const [allMessages, setAllMessages] = useState<Message[]>([]);
  const [message, setMessage] = useState("");

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const { data: conversations } = useQuery({
    queryKey: ["provider-customer-conversation", conversationId, currentPage],
    queryFn: () =>
      fetchConversationMessagesApi(conversationId as string, currentPage),
    enabled: !!conversationId,
  });

  //join room when receiverId changes
  useEffect(() => {
    if (!socket || !receiverId) return;
    socket.emit("join:room", receiverId, (roomId: string) => {
      roomIdRef.current = roomId;
    });
  }, [receiverId]);

  useEffect(() => {
    if (!socket) return;

    const handleMessages = (
      data: {
        message: Message;
        roomId: string;
        senderId: string;
        receiverId: string;
      },
      ack: (isLiveChat: boolean) => void
    ) => {
      if (data.roomId === roomIdRef.current) {
        // Ensure the message is for the current receiver
        setAllMessages((prev) => [data?.message, ...prev]);
        ack(receiverId === data.senderId); // acknowledge the message as live chat if the receiverId matches the senderId
        if (user === data.message.sentBy) {
          // If the current user is the sender, we dont need to emit seen message
          return;
        }
        if (receiverId === data.senderId) {
          socket.emit("message:seen", {
            chatId: data.message._id,
            conversationId: data.message.conversationId,
          });
        }
      }
    };
    socket.on("chat", handleMessages);
    return () => {
      socket.off("chat", handleMessages);
    };
  }, [socket, receiverId, roomIdRef]);

  //scroll to bottom
  useEffect(() => {
    if (lastMessageRef.current) {
      (lastMessageRef.current as HTMLElement)?.scrollIntoView({
        behavior: "smooth",
      });
    }
  }, []);

  //reset when receiverId changes
  useEffect(() => {
    if (prevReceiverIdRef.current === receiverId) return;
    if (scrollableRef.current) {
      (scrollableRef.current as HTMLElement)?.scrollIntoView({
        behavior: "smooth",
      });
    }
    setCurrentPage(1);
    setAllMessages([]);
    prevReceiverIdRef.current = receiverId;
  }, [receiverId]);

  // reverse the conversation
  useEffect(() => {
    if (!conversations) return;
    if (currentPage === 1) {
      setAllMessages(conversations.chats);
    } else {
      setAllMessages((prev) => [...conversations.chats, ...prev]);
    }
  }, [conversations, currentPage]);
  // send message
  const { mutate, isPending: sendMessageLoading } = useMutation({
    mutationFn: sendMessageApi,
    onSuccess: () => {},
    onError: () => {},
  });
  const handleSendMessage = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!message.trim() || sendMessageLoading) return;
    let mutationPayload = { receiverId, content: message };
    if (conversationId) {
      mutationPayload = {
        ...mutationPayload,
        ...{ conversationId: conversationId },
      };
    }
    mutate(mutationPayload);
    setMessage("");
  };
  if (!receiverId) {
    return (
      <EmptyConversation handleHideMessageSection={handleHideMessageSection} />
    );
  }
  return (
    <div className="flex flex-col w-full h-full">
      {/* Header */}

      <div className="flex flex-shrink-0 p-4 border-b grow gap-x-3 md:gap-0 border-nueutral-40">
        <img
          onClick={handleHideMessageSection}
          src={GoBackIcon}
          alt="go back"
          className="w-4 h-4 my-auto cursor-pointer md:hidden"
        />
        <div>
          <h1>{name}</h1>
          <h5 className="text-sm text-neutral-300">{description}</h5>
        </div>
      </div>
      <InfiniteScroll
        style={{ display: "flex", flexDirection: "column-reverse" }}
        inverse={true}
        dataLength={allMessages.length}
        next={() => handlePageChange(currentPage + 1)}
        hasMore={((conversations?.totalPages as number) || 0) > currentPage}
        loader={
          <div className="flex justify-center py-4 ">
            <div className="flex items-center gap-2 text-sm text-neutral-500">
              <div className="w-4 h-4 border-2 rounded-full border-coral border-t-transparent animate-spin"></div>
              Loading older messages...
            </div>
          </div>
        }
        endMessage={
          currentPage === conversations?.totalPages && (
            <div className="flex justify-center py-4">
              <div className="text-sm text-neutral-400">
                No more messages to load
              </div>
            </div>
          )
        }
        height={"78vh"}
        className="p-5 scrollbar-hide"
      >
        {allMessages.map((conversation, index) => (
          <div key={conversation?.createdAt + index} className="flex flex-col">
            <div className="flex justify-center">
              <p className="text-xs text-neutral-400">
                {index % 6 === 0 && dateFormatter(conversation?.createdAt)}
              </p>
            </div>
            <div
              ref={index === allMessages.length - 1 ? scrollableRef : null}
              key={`${conversation?._id}-${index}`}
              className={`my-3 p-3 max-w-[80%] md:max-w-[49%] border ${
                conversation?.sentBy == user
                  ? "bg-coral text-white self-end rounded-tr-none rounded-xl"
                  : "self-start rounded-tl-none rounded-xl border-neutral-50"
              }`}
            >
              <p className="break-words">{conversation?.content}</p>
              <p
                className={`${conversation?.sentBy == user ? "text-tints-60" : "text-neutral-300"} text-sm text-end mt-1`}
              >
                {timeFormatter(conversation?.createdAt)}
              </p>
            </div>
          </div>
        ))}
      </InfiniteScroll>
      <form
        onSubmit={handleSendMessage}
        className="flex flex-shrink-0 p-4 border-t gap-x-2 border-neutral-40"
      >
        <Input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type message here..."
          className="w-full p-2 border rounded-md grow border-neutral-40"
        />
        <Buttonwithicon
          disabled={message.trim().length === 0}
          icon={SendIcon}
          variant="button"
        />
      </form>
    </div>
  );
}

export default MessageSection;
