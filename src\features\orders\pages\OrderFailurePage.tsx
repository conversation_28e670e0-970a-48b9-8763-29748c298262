import PageLayout from "@/components/layout/PageLayout";
import GoBack from "@/components/ui/go-back";
import FailureIcon from "@/assets/failure.png";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
function OrderFailurePage() {
  return (
    <PageLayout>
      <div className="w-11/12 mx-auto">
        <GoBack href="/cart" />
        <div className="flex items-center justify-center ">
          <div className="flex flex-col items-center p-5 border rounded-md border-gray-2 gap-y-2">
            <img
              src={FailureIcon}
              alt="order success"
              className="w-16 h-16 mx-auto"
            />
            <h2 className="mt-3 text-lg font-semibold">Purchase Failed!</h2>
            <p className="text-center text-neutral-300">
              Your purchase was not successful, go back and try <br />
              again.
            </p>
            <div className="flex mt-5 gap-x-4">
              <Link to={"/shop"}>
                <Button variant={"outline"}>Browse other products</Button>
              </Link>
              <Link to={"/cart"}>
                {" "}
                <Button>View cart</Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}

export default OrderFailurePage;
