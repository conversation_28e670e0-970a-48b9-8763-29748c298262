import { useState } from "react";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import Joi from "joi";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { X, Upload, FileText, Trash2 } from "lucide-react";
import { showToast } from "@/lib/toast";

interface CertificationUploadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (files: File[]) => void;
  isLoading?: boolean;
}

interface CertificationFormData {
  certifications: File[];
}

// Validation schema for certification upload
const certificationUploadSchema = Joi.object({
  certifications: Joi.array().min(1).required().messages({
    "array.min": "At least one certification file is required",
    "any.required": "Certification files are required",
  }),
});

// Allowed file types and size limit
const allowedFileTypes = ["application/pdf", "image/jpeg", "image/png", "image/jpg"];
const maxFileSize = 20 * 1024 * 1024; // 20MB per file

export default function CertificationUploadDialog({
  open,
  onOpenChange,
  onSubmit,
  isLoading = false,
}: CertificationUploadDialogProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [dragActive, setDragActive] = useState(false);

  const {
    handleSubmit,
    setValue,
    trigger,
    formState: { errors },
  } = useForm<CertificationFormData>({
    resolver: joiResolver(certificationUploadSchema),
  });

  // Handle file selection
  const handleFileSelect = (files: FileList) => {
    const validFiles: File[] = [];
    const invalidFiles: string[] = [];

    Array.from(files).forEach((file) => {
      // Validate file type
      if (!allowedFileTypes.includes(file.type)) {
        invalidFiles.push(`${file.name} - Invalid file type`);
        return;
      }

      // Validate file size
      if (file.size > maxFileSize) {
        invalidFiles.push(`${file.name} - File too large (max 20MB)`);
        return;
      }

      validFiles.push(file);
    });

    if (invalidFiles.length > 0) {
      showToast(`Some files were rejected: ${invalidFiles.join(", ")}`, "error");
    }

    if (validFiles.length > 0) {
      const updatedFiles = [...selectedFiles, ...validFiles];
      setSelectedFiles(updatedFiles);
      setValue("certifications", updatedFiles);
      trigger("certifications");
    }
  };

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFileSelect(e.target.files);
    }
  };

  // Handle drag and drop
  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  // Remove selected file
  const removeFile = (index: number) => {
    const updatedFiles = selectedFiles.filter((_, i) => i !== index);
    setSelectedFiles(updatedFiles);
    setValue("certifications", updatedFiles);
    trigger("certifications");
  };

  // Handle form submission
  const handleFormSubmit = (data: CertificationFormData) => {
    onSubmit(data.certifications);
  };

  // Reset form when dialog closes
  const handleDialogClose = () => {
    setSelectedFiles([]);
    setValue("certifications", []);
    onOpenChange(false);
  };

  // Reset form when dialog opens
  const handleDialogOpen = (open: boolean) => {
    if (open) {
      // Reset state when opening
      setSelectedFiles([]);
      setValue("certifications", []);
    }
    onOpenChange(open);
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogOpen}>
      <DialogContent className="max-w-lg mx-auto max-h-[90vh] overflow-y-auto">
        <DialogHeader className="flex flex-row items-center justify-between">
          <DialogTitle className="text-lg font-semibold">Upload Certifications</DialogTitle>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDialogClose}
            className="h-6 w-6"
            disabled={isLoading}
          >
            <X className="h-4 w-4" />
          </Button>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              dragActive
                ? "border-orange-500 bg-orange-50"
                : "border-gray-300 hover:border-gray-400"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <div className="space-y-2">
              <div className="flex items-center justify-center w-12 h-12 mx-auto bg-gray-100 rounded-full">
                <Upload className="w-6 h-6 text-gray-400" />
              </div>
              <div>
                <p className="text-sm text-gray-600">
                  Drag and drop your certifications here, or{" "}
                  <button
                    type="button"
                    className="text-orange-600 hover:text-orange-700 font-medium"
                    onClick={() => document.getElementById('certification-upload')?.click()}
                    disabled={isLoading}
                  >
                    browse
                  </button>
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  PDF, JPG, PNG up to 20MB each. Multiple files allowed.
                </p>
              </div>
            </div>

            <input
              id="certification-upload"
              type="file"
              accept={allowedFileTypes.join(',')}
              multiple
              className="hidden"
              onChange={handleFileInputChange}
              disabled={isLoading}
            />
          </div>

          {/* Selected Files List */}
          {selectedFiles.length > 0 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-900">Selected Files:</h4>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {selectedFiles.map((file, index) => (
                  <div
                    key={`${file.name}-${index}`}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      <FileText className="w-5 h-5 text-gray-400 flex-shrink-0" />
                      <div className="min-w-0 flex-1">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {file.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatFileSize(file.size)}
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                      className="text-gray-400 hover:text-red-500 flex-shrink-0"
                      disabled={isLoading}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {errors.certifications && (
            <p className="text-sm text-red-500">{errors.certifications.message}</p>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleDialogClose}
              className="flex-1"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1"
              disabled={selectedFiles.length === 0 || isLoading}
            >
              {isLoading ? "Uploading..." : `Upload ${selectedFiles.length} File${selectedFiles.length !== 1 ? 's' : ''}`}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
