import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import OrderStatusDisplay from "./OrderStatusDisplay";
import { useParams } from "react-router-dom";
import { updateOrderStatusApi } from "../api";
import { useMutation } from "@tanstack/react-query";
import { showToast } from "@/lib/toast";
import RequestModal from "./RequestModal";
import EmailModal from "./EmailModal";

function OrderStatusSection({
  orderProgress,
  refetch,
  currentOrderStatus,
}: {
  orderProgress: { status: string; date: string; completed: boolean }[];
  currentOrderStatus: string;
  refetch: () => void;
}) {
  const { id } = useParams();
  const [isOpen, setIsOpen] = useState({ request: false, email: false });
  const [currentType, setCurrentType] = useState("");
  const [emailType, setEmailType] = useState(currentOrderStatus);
  useEffect(() => {
    setEmailType(currentOrderStatus);
  }, [currentOrderStatus]);
  const { mutate, isPending: loading } = useMutation({
    mutationFn: updateOrderStatusApi,
    onSuccess: () => {
      showToast(
        currentType === "ship"
          ? "Order marked as shipped successfully"
          : "Order marked as delivered successfully",
        "success"
      );
      refetch();
    },
    onError: () => {
      showToast(
        currentType === "ship"
          ? "Failed to mark order as shipped"
          : "Failed to mark order as delivered",
        "error"
      );
    },
  });
  const onClose = (modal: keyof typeof isOpen) => {
    setIsOpen((prev) => ({ ...prev, [modal]: false }));
  };
  const onOpen = (modal: keyof typeof isOpen) => {
    setIsOpen((prev) => ({ ...prev, [modal]: true }));
  };
  const handleMarkAsShipped = () => {
    setCurrentType("ship");
    mutate({ orderId: id as string, type: "ship" });
  };
  const handleMarkAsDelivered = () => {
    setCurrentType("deliver");
    mutate({ orderId: id as string, type: "deliver" });
  };
  const showRequestButton = (status: string, completed: boolean) => {
    return (
      ["exchange requested", "return requested"].includes(status) &&
      ["exchange requested", "return requested"].includes(currentOrderStatus) &&
      completed
    );
  };
  const showMarkAsShippedButton = (status: string, completed: boolean) => {
    return status === "item shipped" && !completed;
  };
  const showMarkAsDeliveredButton = (
    status: string,
    completed: boolean,
    prevStatus: { status: string; date: string; completed: boolean }
  ) => {
    return (
      status === "item delivered" &&
      !completed &&
      prevStatus?.status === "item shipped" &&
      prevStatus?.completed
    );
  };
  const handleEmailClick = () => {
    setEmailType("delay");
    onOpen("email");
  };
  return (
    <>
      <div className="p-4 border rounded-lg border-gray-2">
        <h1 className="font-bold">Order Status</h1>
        <div className="flex flex-col mt-4 gap-y-4">
          {orderProgress?.map((order, i) => (
            <div key={order.status} className="flex flex-col gap-y-5">
              <div className="flex justify-between">
                <OrderStatusDisplay
                  status={order.status}
                  date={order.date}
                  completed={order.completed}
                />
                <div>
                  {showMarkAsShippedButton(order.status, order.completed) && (
                    <Button
                      className={`${loading ? "opacity-50" : ""}`}
                      disabled={loading}
                      onClick={handleMarkAsShipped}
                    >
                      {loading ? "Marking as shipped..." : "Mark as shipped"}
                    </Button>
                  )}
                  {showMarkAsDeliveredButton(
                    order.status,
                    order.completed,
                    orderProgress[i - 1]
                  ) && (
                    <Button
                      className={`${loading ? "opacity-50" : ""}`}
                      disabled={loading}
                      onClick={handleMarkAsDelivered}
                    >
                      {loading
                        ? "Marking as delivered..."
                        : "Mark as delivered"}
                    </Button>
                  )}
                  {showRequestButton(order.status, order.completed) && (
                    <Button onClick={() => onOpen("request")}>
                      Review request
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
          <div className="flex flex-col justify-between gap-y-4 md:gap-y-0 md:flex-row">
            <div className="flex shrink-0">
              <p className="text-sm text-neutral-300 md:text-base">
                Send a mail to your customer if there is any delay.
                <button
                  onClick={handleEmailClick}
                  className="mx-1 text-sm text-orange-1 md:text-base"
                >
                  Click here
                </button>
              </p>
            </div>
          </div>
        </div>
      </div>
      {isOpen.request && (
        <RequestModal
          currentOrderStatus={currentOrderStatus}
          isOpen={isOpen.request}
          onClose={() => onClose("request")}
          onOpenEmail={() => onOpen("email")}
        />
      )}
      {isOpen.email && (
        <EmailModal
          refetch={refetch}
          heading="Send return instructions"
          subheading="For any delivery related information, send a mail to your customer."
          type={emailType}
          isOpen={isOpen.email}
          onClose={() => onClose("email")}
        />
      )}
    </>
  );
}

export default OrderStatusSection;
