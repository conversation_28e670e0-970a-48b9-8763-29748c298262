import AuthLayout from "../components/AuthLayout";
import FingerPrintImage from "@/assets/fingerprint.png";
import { checkAuthentication } from "@/lib/utils";
import { Navigate, useLocation } from "react-router-dom";

function CheckEmail() {
  const location = useLocation();
  const state = location.state as { email: string };
  const { isLoggedIn, href } = checkAuthentication();
  if (isLoggedIn) {
    return <Navigate to={href} />;
  }
  return (
    <AuthLayout>
      <div className="w-full max-w-md text-center">
        <div className="flex flex-col ">
          <img
            src={FingerPrintImage}
            alt="Fingerprint"
            className="mx-auto object-cover"
          />
          <div className="w-full flex flex-col items-center gap-y-5 my-6">
            <h1 className="text-3xl mx-auto font-medium text-center flex-grow font-prettywise">
              Check your Email
            </h1>
            <p className="text-gray-500  mb-6">
              A link to reset your password has been sent to
              <span className="font-bold mx-1">{state.email}</span>successfully.
            </p>
          </div>
        </div>
      </div>
    </AuthLayout>
  );
}

export default CheckEmail;
