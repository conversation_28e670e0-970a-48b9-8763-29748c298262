import { Separator } from "@radix-ui/react-select";
import FingerPrintImage from "@/assets/fingerprint.png";
import PlusIcon from "@/assets/plus.png";
import { useState } from "react";
import AddProductModal from "./AddProductModal";
import ProductCard from "./ProductCard";
import { Button } from "@/components/ui/button";
import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { showToast } from "@/lib/toast";
// import { sellerAddProductApi } from "../api";
import { ProductCreationPayload } from "../type";
import { useNavigate } from "react-router-dom";
import {
  deleteSellerProductsApi,
  getSellerProductsApi,
  validateSellerApi,
} from "../api";
function AddProductForm() {
  const navigate = useNavigate();
  const sellerId = localStorage.getItem("sellerId");
  const [isOpen, setIsOpen] = useState(false);
  const [refetch, setRefetch] = useState(false);
  const onClose = () => setIsOpen(false);
  const { data } = useQuery({
    queryKey: ["sellerProducts", refetch],
    queryFn: () => getSellerProductsApi(sellerId as string),
  });
  const { mutate } = useMutation({
    mutationFn: deleteSellerProductsApi,
    onSuccess: () => {
      showToast("Product deleted successfully!", "success");
      setRefetch(!refetch);
    },
    onError: (error: AxiosError) => {
      console.error(error.response?.data);
      showToast("Failed to delete product. Please try again.", "error");
    },
  });
  const handleDelete = (productId: string) => {
    mutate({ sellerId: sellerId as string, productId });
  };

  const { mutate: validateSeller, isPending: loading } = useMutation({
    mutationFn: validateSellerApi,
    onSuccess: () => {
      navigate("/seller/verify-account");
    },
    onError: (error: AxiosError) => {
      console.error(error.response?.data);
    },
  });
  const onSubmit = () => {
    if (loading) return;
    validateSeller(sellerId as string);
  };
  const handleAddProduct = () => {
    setIsOpen(true);
  };
  return (
    <>
      <div className="flex flex-col items-center justify-center w-full max-w-md px-4 mx-auto mb-4 space-y-5 text-center md:mx-0">
        <img
          src={FingerPrintImage}
          alt="Fingerprint"
          className="object-cover w-16 h-16 mx-auto"
        />
        <h1 className="text-2xl font-bold md:text-3xl font-prettywise">
          Setup your profile
        </h1>
        <div className="w-full">
          <div className="flex justify-between">
            <p className=" text-neutral-100">Store details</p>
            <p className="text-orange-1">Products</p>
          </div>
          <div className="flex justify-center w-9/12 mx-auto mt-2">
            <div className="my-auto bg-black rounded-full min-h-2 min-w-2"></div>
            <Separator className="w-full my-auto h-0.5 bg-neutral-300" />
            <div className="my-auto rounded-full min-h-2 min-w-2 bg-orange-1"></div>
          </div>
        </div>
        <div
          onClick={handleAddProduct}
          className="flex justify-center w-full p-3 border rounded-full cursor-pointer gap-x-3 border-gray-2"
        >
          <button className="text-orange-1">Add Product</button>
          <img src={PlusIcon} alt="plus" />
        </div>
        <div className="max-h-[300px] w-full overflow-y-auto flex flex-col gap-y-2">
          {data?.data.products.map(
            (product: ProductCreationPayload, index: number) => {
              return (
                <ProductCard
                  onDelete={() => handleDelete(product._id as string)}
                  index={index}
                  image={product.images && product.images[0].image}
                  title={product.title}
                  price={product.price}
                  key={index}
                />
              );
            }
          )}
        </div>
        <div className="flex justify-end w-full mt-2">
          {
            <Button
              onClick={onSubmit}
              disabled={data?.data.products.length < 1}
              className="px-8"
            >
              {loading ? "Validating..." : "Next"}
            </Button>
          }
        </div>
      </div>
      <AddProductModal
        setRefetch={setRefetch}
        isOpen={isOpen}
        onClose={onClose}
      />
    </>
  );
}

export default AddProductForm;
