import { Navigate } from "react-router-dom";
import AuthLayout from "../components/AuthLayout";
import ResetPasswordForm from "../components/ResetPasswordForm";
import { checkAuthentication } from "@/lib/utils";

function ResetPassword() {
  const { isLoggedIn, href } = checkAuthentication();

  return isLoggedIn ? (
    <Navigate to={href} />
  ) : (
    <AuthLayout>
      <ResetPasswordForm />
    </AuthLayout>
  );
}

export default ResetPassword;
