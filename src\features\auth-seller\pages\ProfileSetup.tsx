import { Navigate } from "react-router-dom";
import AuthLayout from "../components/AuthLayout";
import ProfileSetupForm from "../components/ProfileSetupForm";
import { checkAuthentication } from "@/lib/utils";

function SellerProfileSetup() {
   const { isLoggedIn, href } = checkAuthentication();
 
   return isLoggedIn ? (
     <Navigate to={href} />
   ) :(
    <AuthLayout>
      <ProfileSetupForm />
    </AuthLayout>
  );
}

export default SellerProfileSetup;
