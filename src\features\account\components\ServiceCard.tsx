import { useState } from "react";
import { BookedServicesCardItem } from "../type";
import ProivderRefundPolicyModal from "./ProivderRefundPolicyModal";
import CancelModal from "@/components/ui/cancel-modal";
import { showToast } from "@/lib/toast";
import ServiceAndCallStatus from "./ServiceAndCallStatus";
import ServiceReviewModal from "./ServiceReviewModal";
import { Button } from "@/components/ui/button";
import Buttonwithicon from "@/components/ui/buttonwithicon";
import ChatIcon from "@/assets/orange-chat-icon.svg";

function ServiceCard({ data }: { data: BookedServicesCardItem }) {
  const [isOpen, setIsOpen] = useState({
    refund: false,
    cancel: false,
    review: false,
  });
  const onOpen = (modal: keyof typeof isOpen) => {
    setIsOpen((prev) => ({ ...prev, [modal]: true }));
  };
  const onClose = (modal: keyof typeof isOpen) => {
    setIsOpen((prev) => ({ ...prev, [modal]: false }));
  };
  const showReviewButton = () => {
    return data.hasRequestedReview && !data.reviewAdded;
  };
  const showChatButton = () => {
    return !["rejected", "cancelled"].includes(data.status);
  };
  return (
    <>
      <div className="container p-4 border rounded-lg border-gray-2 ">
        <div className="flex gap-x-3">
          <div className="w-full">
            <div className="flex justify-between gap-x-2">
              <h1 className="text-md md:text-lg ">{data.provider.specialty}</h1>
              <div className="hidden md:flex gap-x-2">
                {showChatButton() && (
                  <Buttonwithicon
                    text="Contact provider"
                    href="/chat/provider"
                    variant="outline"
                    icon={ChatIcon}
                    classname="w-full text-center"
                  />
                )}

                {showReviewButton() && (
                  <Button onClick={() => onOpen("review")} className="py-1">
                    Write a review
                  </Button>
                )}
              </div>
            </div>
            <div className="flex flex-col mt-1 md:flex-row">
              <h1 className="text-sm text-neutral-300 md:text-base">
                {data.provider.firstName} {data.provider.lastName}
              </h1>
            </div>
          </div>
        </div>
        <div className="flex flex-col justify-between mt-3 md:flex-row">
          <ServiceAndCallStatus
            openRefundPolicy={() => onOpen("refund")}
            status={data.status}
            date={data.bookedAt}
          />
          <div className="flex mt-2 md:hidden gap-x-2">
            {showChatButton() && (
              <Buttonwithicon
                text="Contact"
                href="/chat/provider"
                variant="outline"
                icon={ChatIcon}
                classname="text-center "
              />
            )}

            {showReviewButton() && (
              <Button
                size="sm"
                onClick={() => onOpen("review")}
                className="py-0.5 rounded-full"
              >
                Write a review
              </Button>
            )}
          </div>
          {data.status !== "cancelled" && (
            <button
              onClick={() => onOpen("cancel")}
              className="self-end mt-2 text-sm underline md:self-auto md:mt-0"
            >
              Cancel
            </button>
          )}
        </div>
      </div>

      {isOpen.cancel && (
        <CancelModal
          isOpen={isOpen.cancel}
          onClose={() => onClose("cancel")}
          handleConfirm={() => {
            showToast("Call cancelled successfully", "success");
          }}
        />
      )}
      {isOpen.refund && (
        <ProivderRefundPolicyModal
          isOpen={isOpen.refund}
          onClose={() => onClose("refund")}
          refundPolicy={data.provider.refundPolicy}
        />
      )}
      {isOpen.review && (
        <ServiceReviewModal
          providerName={`${data.provider.firstName} ${data.provider.lastName}`}
          isOpen={isOpen.review}
          onClose={() => onClose("review")}
          serviceId={data._id}
        />
      )}
    </>
  );
}

export default ServiceCard;
