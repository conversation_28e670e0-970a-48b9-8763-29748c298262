import { Skeleton } from "@/components/ui/skeleton";

function MessageSkeleton({ isSender }: { isSender: boolean }) {
  return (
    <div
      className={`my-3 p-3 max-w-[80%] md:min-w-[49%] md:max-w-[49%] border rounded-xl flex flex-col gap-2
        ${isSender
          ? "self-end  rounded-tr-none"
          : "self-start  rounded-tl-none"
        }`}
    >
      <Skeleton className="w-3/4 h-4" />
      <Skeleton className="self-end w-1/4 h-3" />
    </div>
  );
}


export default MessageSkeleton
