import RatingIcon from "@/assets/rating-star.png";
import ReviewCard from "./ReviewCard";
import DynamicPagination from "@/components/ui/dynamic-pagination";
import { useState } from "react";
import { Separator } from "@/components/ui/separator";
import { useQuery } from "@tanstack/react-query";
import { getProductReviewsApi } from "../api";
import ReviewCardSkelton from "./ReviewCardSkelton";

function ReviewSection({ productId }: { productId: string }) {
  const [currentPage, setCurrentPage] = useState(1);
  let totalPage = 1;
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const {
    data,
    isSuccess,
    isPending: loading,
  } = useQuery({
    queryKey: ["review", currentPage],
    queryFn: () => getProductReviewsApi(productId, currentPage),
  });
  if (isSuccess) {
    totalPage = data?.totalPages;
  }
  return !data?.reviews.length ? (
    <div className="mt-20 md:mt-28"></div>
  ) : (
    <div className="mt-10 md:mt-20">
      {/* header section */}
      <div className="flex gap-x-3">
        <div className="flex gap-x-1">
          <img src={RatingIcon} className="w-6 h-6 my-auto" />
          <p>
            <span className="mr-2 text-3xl">{data?.overAllRating || 0}</span>
            <span className="mt-auto text-neutral-300">overall rating</span>
          </p>
        </div>
        <Separator
          orientation="vertical"
          className="h-8 my-auto text-neutral-300"
        />
        <div className="flex">
          <span className="mr-2 text-3xl">{data?.totalReviews || 0}</span>
          <span className="mt-auto text-neutral-300">reviews</span>
        </div>
      </div>
      <div className="mt-5">
        {loading ? (
          <ReviewCardSkelton />
        ) : (
          data?.reviews.map(
            (review: {
              _id?: string;
              userId: { firstName: string };
              rating: number;
              review: string;
            }) => (
              <ReviewCard
                key={review._id}
                name={review.userId?.firstName}
                rating={review.rating}
                content={review.review}
              />
            )
          )
        )}
      </div>
      <div className="flex justify-end">
        <DynamicPagination
          currentPage={currentPage}
          totalPages={totalPage}
          onPageChange={handlePageChange}
        />
      </div>
    </div>
  );
}

export default ReviewSection;
