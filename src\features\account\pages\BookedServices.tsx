import { useQuery } from "@tanstack/react-query";
import { fetchBookedServicesApi } from "../api";
import AccountLayoutWrapper from "../components/AccountLayoutWrapper";
import ScheduledCallsCardSkelton from "../components/ScheduledCallsCardSkelton";
import ServiceCard from "../components/ServiceCard";
import { BookedServicesCardItem } from "../type";

function BookedServices() {
  const { data, isPending: loading } = useQuery({
    queryKey: ["scheduled-calls"],
    queryFn: fetchBookedServicesApi,
  });

  return (
    <AccountLayoutWrapper>
      <div>
        {loading ? (
          <div className="space-y-3">
            {[1, 2].map((_, index) => (
              <ScheduledCallsCardSkelton key={index} />
            ))}
          </div>
        ) : data?.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-gray-500">
              You haven't scheduled any calls yet.
            </p>
          </div>
        ) : (
          <div className="flex flex-col gap-y-3  max-h-[500px] scrollbar-hide  overflow-y-scroll">
            {data?.map((call: BookedServicesCardItem) => (
              <ServiceCard key={call._id} data={call} />
            ))}
          </div>
        )}
      </div>
    </AccountLayoutWrapper>
  );
}

export default BookedServices;
