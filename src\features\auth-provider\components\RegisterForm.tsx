import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { LuEye } from "react-icons/lu";
import { PiEyeSlash } from "react-icons/pi";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

import { signupSchema } from "../validation";
import GoogleButton from "@/components/ui/google-button";
import FingerPrintImage from "@/assets/fingerprint.png";
import { FormData, RegisterPayload } from "../type";
import { providerGoogleLoginApi, registerApi } from "../api";
import { showToast } from "@/lib/toast";
import { useAuthStore } from "@/store/authStore";

export function RegisterForm() {
  const login = useAuthStore((state) => state.login);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: joiResolver(signupSchema),
  });

  const { mutate, isPending } = useMutation({
    mutationFn: registerApi,
    onSuccess: (response) => {
      console.log("Signup success response:", response);
      // const emailInput = document.querySelector<HTMLInputElement>(
      //   'input[name="email"]'
      // );
      // const email = emailInput?.value;

      // // Check if the user needs to verify email
      // if (response.needsEmailVerification) {
      //   navigate("/provider/verify-otp", {
      //     state: { email },
      //   });
      // } else
      if (response.data?.auth?._id) {
        // If response has auth data in the format: { status, message, data: { auth: { _id, email, ... } } }
        // This is the format returned by the API as shown in the example response
        localStorage.setItem("userAuthId", response.data.auth._id);

        // Check if the message indicates OTP has been sent
        if (
          response.message &&
          response.message.includes("OTP has been sent")
        ) {
          showToast("OTP has been sent to your email", "success");
          navigate("/provider/verify-otp", {
            state: { email: response.data.auth.email },
          });
        } else {
          // If no OTP message, go to profile setup
          navigate("/provider/setup-profile");
        }
      } else if (response.userId) {
        // If email is already verified, store auth ID and redirect to profile setup
        localStorage.setItem("userAuthId", response.userId);
        navigate("/provider/setup-profile");
      } else {
        // Fallback to login page
        showToast("Account created successfully. Please login.", "success");
        navigate("/provider/login");
      }
    },
    onError: (error: AxiosError) => {
      console.error("Signup error:", error);
      if (
        error.response?.status === 409 ||
        (error.response?.status === 400 &&
          error.response?.data &&
          Array.isArray(error.response.data) &&
          error.response.data[0]?.message === "User already exist")
      ) {
        showToast("Email already exists", "error");
      } else {
        showToast("Something went wrong", "error");
      }
    },
  });

  const onSubmit = (formData: FormData) => {
    const payload: RegisterPayload = {
      email: formData.email,
      password: formData.password,
    };
    mutate(payload);
  };

  const googleLogin = useMutation({
    mutationFn: providerGoogleLoginApi,
    onSuccess: ({ data }) => {
      console.log(data, "google login response");
      if (!data.auth.hasCompletedProfile) {
        localStorage.setItem("userAuthId", data.auth._id);
        navigate("/provider/setup-profile", {
          state: {
            loginViaGoogle: true,
          },
        });
        return;
      }
      if (data.provider) {
        localStorage.setItem("providerId", data.provider._id);
        localStorage.setItem(
          "hasSetAvailability",
          data.provider.hasSetAvailability.toString()
        );
        localStorage.setItem(
          "hasAddedServices",
          data.provider.hasAddedServices.toString()
        );
        localStorage.setItem(
          "planChoosen",
          data.provider.planChoosen.toString()
        );
        if (
          !data.provider.hasAddedServices ||
          !data.provider.hasSetAvailability
        ) {
          navigate("/provider/setup-profile", {
            state: {
              loginViaGoogle: true,
            },
          });
          return;
        }
        if (!data.provider.planChoosen) {
          navigate("/provider/setup-profile/plan-selection");
          return;
        }
        if (!data.provider.isApproved) {
          showToast("Admin verfication pending", "error");
          return;
        }
      }
      login({ accessToken: data.accessToken as string, role: "provider" });
      navigate("/provider/dashboard");
    },
    onError: (error: AxiosError<Array<{ message?: string }>>) => {
      console.log(error, "error");
      if (
        error.response?.status === 400 &&
        error.response?.data[0]?.message == "User already exist"
      ) {
        showToast("User already exists", "error");
        return;
      }
      showToast("Something went wrong", "error");
    },
  });
  const onGoogleLogin = (token: string) => {
    googleLogin.mutate(token);
  };
  return (
    <div className="w-full max-w-md space-y-6 text-center">
      <div className="space-y-2">
        <Link to={"/"}>
          <img
            src={FingerPrintImage}
            alt="Fingerprint"
            className="object-cover mx-auto"
          />
        </Link>
        <p className="mt-5 text-neutral-100">Providers</p>
        <h1 className="text-3xl font-bold md:text-4xl font-prettywise">
          Welcome to Nurture
        </h1>
      </div>

      <GoogleButton
        disabled={googleLogin.isPending}
        onGoogleLogin={onGoogleLogin}
        text="Sign up with Google"
      />

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="px-2 bg-white text-muted-foreground">or</span>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-4 text-left">
          {/* Email Input */}
          <div>
            <Input
              className="border-input-border focus:border-slate-300"
              placeholder="Email"
              type="email"
              {...register("email")}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
          </div>

          {/* Password Input */}
          <div>
            <div className="flex border rounded-sm border-input-border focus:border-slate-300">
              <Input
                className="border-none"
                placeholder="Password"
                type={showPassword ? "text" : "password"}
                {...register("password")}
              />
              {showPassword ? (
                <PiEyeSlash
                  onClick={() => setShowPassword(false)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              ) : (
                <LuEye
                  onClick={() => setShowPassword(true)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              )}
            </div>
            {errors.password && (
              <p className="text-sm text-red-500">{errors.password.message}</p>
            )}
          </div>

          {/* Confirm Password Input */}
          <div>
            <div className="flex border rounded-sm border-input-border focus:border-slate-300">
              <Input
                className="border-none"
                placeholder="Confirm Password"
                type={showConfirmPassword ? "text" : "password"}
                {...register("confirmPassword")}
              />
              {showConfirmPassword ? (
                <PiEyeSlash
                  onClick={() => setShowConfirmPassword(false)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              ) : (
                <LuEye
                  onClick={() => setShowConfirmPassword(true)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              )}
            </div>
            {errors.confirmPassword && (
              <p className="text-sm text-red-500">
                {errors.confirmPassword.message}
              </p>
            )}
          </div>
        </div>

        <div className="flex flex-col mt-6 space-y-2 md:mt-12 gap-y-3 md:gap-y-1">
          <Button
            type="submit"
            className="w-full p-2 hover:bg-[#c65a3c]"
            disabled={isPending}
          >
            {isPending ? "Signing up..." : "Sign up"}
          </Button>
        </div>
      </form>

      <div className="flex justify-center gap-x-1">
        <p>Already have an account?</p>
        <Link
          to="/provider/login"
          className="font-semibold underline text-orange-1"
        >
          Login
        </Link>
      </div>
    </div>
  );
}

export default RegisterForm;
