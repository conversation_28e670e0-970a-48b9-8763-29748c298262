import SignupImage from "@/assets/admin-auth.png";
import LoginForm from "../components/LoginForm";
import { Navigate } from "react-router-dom";
import { checkAuthentication } from "@/lib/utils";
function Login() {
  const { isLoggedIn, href } = checkAuthentication();
  return isLoggedIn ? (
    <Navigate to={href} />
  ) : (
    <div className="flex flex-col mb-2 md:mb-0 md:flex-row">
      <div className="md:w-1/2 mb-14 md:mb-0">
        <img
          src={SignupImage}
          alt="Mom and baby"
          className="h-[280px] w-full md:w-auto block md:h-screen "
        />
      </div>
      <div className="w-full md:w-1/2 flex items-center justify-center">
        <div className="w-11/12 mx-auto">
          <LoginForm />
        </div>
      </div>
    </div>
  );
}

export default Login;
