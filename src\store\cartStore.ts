import { create } from "zustand";
import { fetchCartItemsCountApi } from "@/features/cart/api";
import { CartItem } from "@/features/cart/type";

type CartState = {
  cartCount: number;
  cartItems: CartItem[];
  setCart: (cartItems: CartItem[]) => void;
  fetchCart: () => CartItem[];
  incrementCartCount: () => void;
  decrementCartCount: () => void;
  fetchCartCount: () => Promise<void>;
};

export const useCartStore = create<CartState>((set) => ({
  cartCount: 0,
  cartItems: [],

  setCart: (cartItems: CartItem[]) => {
    set({ cartItems });
  },

  fetchCart: (): CartItem[] => useCartStore.getState().cartItems,

  incrementCartCount: () =>
    set((state) => ({ cartCount: state.cartCount + 1 })),

  decrementCartCount: () =>
    set((state) => ({
      cartCount: state.cartCount > 0 ? state.cartCount - 1 : 0,
    })),

  fetchCartCount: async () => {
    try {
      const { data } = await fetchCartItemsCountApi();
      set({ cartCount: data?.totalItem });
    } catch (error) {
      console.error("Error fetching cart count:", error);
    }
  },
}));
