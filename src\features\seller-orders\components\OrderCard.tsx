import OrderStatusBadge from "@/components/ui/order-status-badge";
import { OrderItems, OrderStatusType } from "../type";

function OrderCard({ order }: { order: OrderItems }) {
  return (
    <>
      <div
        key={order?._id}
        className="flex justify-between p-3 overflow-hidden border rounded-lg md:p-5 md:gap-x-5 border-gray-2"
      >
        <img
          src={order?.product?.images[0]?.image}
          alt={order?.product?.title}
          className={` object-cover w-20 h-20 rounded-md `}
        />
        <div className="flex flex-col justify-between grow gap-y-2">
          <h2 className="text-sm md:text-base text-neutral-300">
            Order ID - {order?._id}
          </h2>
          <h1 className="md:text-lg">{order?.product?.title}</h1>
          <div className="flex justify-between">
            <h2 className="text-sm md:text-base text-neutral-300">
              Quantity: {order?.quantity}
            </h2>
            <OrderStatusBadge
              className="self-start px-3 py-1 lg:hidden"
              status={order?.status as OrderStatusType}
            />
          </div>
        </div>
        <OrderStatusBadge
          className="self-start hidden px-3 py-1 lg:block"
          status={order?.status as OrderStatusType}
        />
      </div>
    </>
  );
}

export default OrderCard;
